class CreateEmailBroadcastSchema < ActiveRecord::Migration[7.0]
  def change
    create_table :user_filters do |t|
      t.references :tenant, null: false, foreign_key: { on_delete: :cascade }

      t.string :name, null: false
      t.text :description, null: true
      t.string :list_type, null: false, default: 'email_filter'
      t.jsonb :filters, null: false, default: {}

      t.timestamps
    end

    add_index :user_filters, [:tenant_id, :list_type, :name], unique: true

    create_table :emails_broadcasts do |t|
      t.references :tenant, null: false, foreign_key: { on_delete: :cascade }
      t.references :signature, null: true, foreign_key: { to_table: :emails_signatures }
      t.references :template, null: true, foreign_key: { to_table: :emails_templates }
      t.bigint :filter_id, null: true

      t.string :title, null: false
      t.string :subject, null: false
      t.string :sidekiq_id, null: true
      t.text :body, null: false

      t.integer :sent, null: false, default: 0
      t.integer :delivered, null: false, default: 0
      t.integer :bounced, null: false, default: 0

      t.string :status, null: false, default: 'draft'
      t.string :scheduled_type, null: false, default: 'send_now'
      t.datetime :scheduled_at, null: true
      t.string :sent_to, null: false, default: 'all_members'
      t.boolean :send_copy_to_sender, null: false, default: false
      t.references :sender_id, null: true, foreign_key: { to_table: :users } #change to sender
      t.boolean :broadcast_status, null: false, default: false
      t.timestamps
    end

    add_foreign_key :emails_broadcasts, :user_filters, column: :filter_id

    create_table :emails_activities do |t|
      t.references :tenant, null: false, foreign_key: { on_delete: :cascade }
      t.references :broadcast, null: false, foreign_key: { to_table: :emails_broadcasts, on_delete: :cascade }
      t.references :user, null: true, foreign_key: { on_delete: :cascade }

      t.string :email_address, null: false
      t.string :status, null: false, default: 'pending'
      t.string :message_id, null: true
      t.string :error_message, null: true
      t.integer :error_code, null: true
      t.datetime :occurred_at, null: false, default: -> { 'CURRENT_TIMESTAMP' }

      t.timestamps
    end

    add_index :emails_activities, :occurred_at
    add_column :tenants, :mandrill_enabled, :boolean
    add_column :tenants, :mandrill_api_key, :string
  end
end
