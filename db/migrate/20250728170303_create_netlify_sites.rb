class CreateNetlifySites < ActiveRecord::Migration[7.0]
  def change
    create_table :netlify_sites do |t|
      t.string :site_id, null: false, index: { unique: true }
      t.string :site_name, null: false
      t.integer :domain_aliases_count, default: 0, null: false
      t.boolean :active, default: true, null: false
      t.json :metadata, default: {}

      t.timestamps
    end
    
    add_index :netlify_sites, :active
    add_index :netlify_sites, :domain_aliases_count
    add_index :netlify_sites, [:active, :domain_aliases_count], name: 'index_netlify_sites_on_active_and_domain_count'
  end
end
