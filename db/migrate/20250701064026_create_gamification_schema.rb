class CreateGamificationSchema < ActiveRecord::Migration[7.0]
  def change
    add_column :tenants, :gamification_enabled, :boolean, default: false

    create_table :gamification_settings do |t|
      t.references :tenant, null: false, foreign_key: true
      t.boolean :points_enabled, default: false
      t.boolean :badges_enabled, default: false
      t.boolean :configured_settings, default: false
      t.boolean :apply_to_past_data, default: false      

      t.timestamps
    end

    create_table :gamification_level_settings do |t|
      t.references :tenant, null: false, foreign_key: true
      t.references :gamification_setting, null: false, foreign_key: true
      t.string :title, null: false
      t.jsonb :icon_data
      t.integer :min_points, null: false, default: 0
      t.integer :max_points, null: false
      t.boolean :enabled, default: true
      t.integer :position

      t.timestamps
    end

    add_index :gamification_level_settings, :enabled
    add_index :gamification_level_settings, :min_points

    create_table :gamification_point_settings do |t|
      t.references :tenant, null: false, foreign_key: true
      t.references :gamification_setting, null: false, foreign_key: true
      t.string :action_type, null: false
      t.integer :points, default: 0
      t.string :description
      t.boolean :enabled, default: true

      t.timestamps
    end
    add_index :gamification_point_settings, [:action_type], unique: true

    create_table :gamification_badge_settings do |t|
      t.references :tenant, null: false, foreign_key: true
      t.references :gamification_setting, null: false, foreign_key: true
      t.string :title, null: false
      t.jsonb :icon_data
      t.text :description
      t.string :condition_type, null: false
      t.integer :points, default: 0
      t.boolean :enabled, default: true
      t.timestamps
    end

    create_table :gamification_reward_settings do |t|
      t.references :tenant, null: false, foreign_key: true
      t.references :gamification_setting, null: false, foreign_key: true
      t.string :condition_type, null: false
      t.integer :condition_value, null: false, default: 0
      t.string :reward_type, null: false
      t.references :rewardable, polymorphic: true, null: false
      t.boolean :enabled, default: true
      t.boolean :apply_to_past_data, default: false      
      t.timestamps
    end

    create_table :gamification_user_profiles do |t|
      t.references :tenant, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true, index: { unique: true }
      t.references :level_setting, null: false, foreign_key: { to_table: :gamification_level_settings }
      t.integer :total_points, default: 0
      t.integer :current_level_progress, default: 0
      t.timestamps
    end

    create_table :gamification_user_badges do |t|
      t.references :tenant, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.references :badge_setting, null: false, foreign_key: { to_table: :gamification_badge_settings }
      t.datetime :earned_at
      t.timestamps
    end

    create_table :gamification_user_rewards do |t|
      t.references :tenant, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.references :reward_setting, null: false, foreign_key: { to_table: :gamification_reward_settings }
      t.datetime :claimed_at
      t.integer :claimed_status
      t.timestamps
    end

    create_table :gamification_user_points_activities do |t|
      t.references :tenant, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.string :action_type, null: false
      t.integer :points_earned, null: false
      t.date :activity_date, null: false
      t.datetime :earned_at, null: false
      t.timestamps
    end
  end
end
