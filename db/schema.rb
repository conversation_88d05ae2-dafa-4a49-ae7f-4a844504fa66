# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_07_31_180056) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "citext"
  enable_extension "plpgsql"

  create_table "admin_roles", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "role", default: "admin", null: false
    t.boolean "courses", default: true, null: false
    t.boolean "packs", default: true, null: false
    t.boolean "users", default: true, null: false
    t.boolean "website", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id", "role"], name: "index_admin_roles_on_tenant_id_and_role", unique: true
    t.index ["tenant_id"], name: "index_admin_roles_on_tenant_id"
  end

  create_table "affiliate_informations", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "nature", default: "Not specified"
    t.string "location", default: "Not specified"
    t.date "birthdate"
    t.string "identity_document", default: "Not provided"
    t.string "facebook"
    t.string "instagram"
    t.string "whatsapp"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "email"
    t.string "linked_in"
    t.string "youtube"
    t.string "reddit"
    t.index ["user_id"], name: "index_affiliate_informations_on_user_id"
  end

  create_table "affiliate_invitations", force: :cascade do |t|
    t.string "email", null: false
    t.bigint "inviter_id", null: false
    t.string "token", null: false
    t.string "status", default: "pending", null: false
    t.bigint "tenant_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_affiliate_invitations_on_email", unique: true
    t.index ["tenant_id"], name: "index_affiliate_invitations_on_tenant_id"
  end

  create_table "affiliates_affiliate_commissions", force: :cascade do |t|
    t.integer "amount_cents", null: false
    t.string "currency", default: "usd", null: false
    t.integer "commission_cents", null: false
    t.bigint "affiliate_purchase_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "stripe_transfer_id"
    t.boolean "refunded", default: false
    t.index ["affiliate_purchase_id"], name: "index_affiliates_affiliate_commissions_on_affiliate_purchase_id"
  end

  create_table "affiliates_affiliate_purchases", force: :cascade do |t|
    t.bigint "affiliate_id"
    t.bigint "purchase_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["affiliate_id"], name: "index_affiliates_affiliate_purchases_on_affiliate_id"
    t.index ["purchase_id"], name: "index_affiliates_affiliate_purchases_on_purchase_id"
  end

  create_table "affiliates_courses", force: :cascade do |t|
    t.bigint "affiliate_id", null: false
    t.bigint "course_id", null: false
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["affiliate_id", "course_id"], name: "index_affiliates_courses_on_affiliate_id_and_course_id"
  end

  create_table "affiliates_links", force: :cascade do |t|
    t.bigint "course_id", null: false
    t.bigint "affiliate_id", null: false
    t.bigint "student_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["affiliate_id"], name: "index_affiliates_links_on_affiliate_id"
    t.index ["course_id"], name: "index_affiliates_links_on_course_id"
    t.index ["student_id"], name: "index_affiliates_links_on_student_id"
  end

  create_table "affiliates_payment_methods", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "name", null: false
    t.string "type", null: false
    t.jsonb "config", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id", "name"], name: "index_affiliates_payment_methods_on_user_id_and_name", unique: true
  end

  create_table "affiliates_tenants", force: :cascade do |t|
    t.bigint "affiliate_id", null: false
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "status", default: 0, null: false
    t.index ["affiliate_id", "tenant_id"], name: "index_affiliates_tenants_on_affiliate_id_and_tenant_id", unique: true
  end

  create_table "audit_versions", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "whodunnit"
    t.bigint "item_id", null: false
    t.string "item_type", null: false
    t.string "item_subtype", null: false
    t.string "event", null: false
    t.string "whodunnit_type"
    t.string "whodunnit_name"
    t.string "comment"
    t.jsonb "object"
    t.jsonb "object_changes"
    t.jsonb "previous_object"
    t.jsonb "new_object"
    t.datetime "created_at"
    t.index ["tenant_id", "item_type", "item_id"], name: "index_audit_versions_on_tenant_id_and_item_type_and_item_id"
    t.index ["tenant_id"], name: "index_audit_versions_on_tenant_id"
    t.index ["whodunnit"], name: "index_audit_versions_on_whodunnit"
  end

  create_table "bunny_videos", force: :cascade do |t|
    t.string "bunny_video_id"
    t.string "videoable_type", null: false
    t.bigint "videoable_id", null: false
    t.bigint "tenant_id", null: false
    t.boolean "migrated", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_bunny_videos_on_tenant_id"
    t.index ["videoable_type", "videoable_id"], name: "index_bunny_videos_on_videoable"
  end

  create_table "communities", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.date "start_date"
    t.date "expiration_date"
    t.date "end_date"
    t.boolean "has_expiration", default: false, null: false
    t.integer "expires_in_days"
    t.integer "price_cents"
    t.string "price_currency"
    t.integer "discounted_price_cents"
    t.string "discounted_price_currency"
    t.string "payment_platform"
    t.boolean "allow_comments", default: true, null: false
    t.boolean "featured", default: false, null: false
    t.jsonb "promo_picture_data"
    t.jsonb "cover_picture_data"
    t.boolean "is_visible", default: false, null: false
    t.boolean "show_preview", default: true, null: false
    t.string "payment_link"
    t.boolean "is_free", default: true, null: false
    t.integer "position"
    t.string "stripe_product_id"
    t.string "stripe_connect_product_id"
    t.string "associated_type"
    t.bigint "associated_id"
    t.bigint "created_by_id"
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "tiny_url"
    t.index ["associated_type", "associated_id"], name: "index_communities_on_associated"
    t.index ["created_by_id"], name: "index_communities_on_created_by_id"
    t.index ["tenant_id"], name: "index_communities_on_tenant_id"
  end

  create_table "community_post_attachments", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "community_post_id", null: false
    t.jsonb "file_data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["community_post_id"], name: "index_community_post_attachments_on_community_post_id"
    t.index ["tenant_id"], name: "index_community_post_attachments_on_tenant_id"
  end

  create_table "community_post_survey_responses", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "community_post_id", null: false
    t.bigint "user_id", null: false
    t.jsonb "survey_answer_data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["community_post_id"], name: "index_community_post_survey_responses_on_community_post_id"
    t.index ["tenant_id"], name: "index_community_post_survey_responses_on_tenant_id"
    t.index ["user_id"], name: "index_community_post_survey_responses_on_user_id"
  end

  create_table "community_posts", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "community_id", null: false
    t.bigint "user_id", null: false
    t.text "title"
    t.text "content"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "attachment_data"
    t.string "media_link"
    t.jsonb "survey_options", default: []
    t.boolean "survey_enabled", default: false
    t.index ["community_id"], name: "index_community_posts_on_community_id"
    t.index ["tenant_id"], name: "index_community_posts_on_tenant_id"
    t.index ["user_id"], name: "index_community_posts_on_user_id"
  end

  create_table "community_teachers", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "community_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["community_id"], name: "index_community_teachers_on_community_id"
    t.index ["tenant_id", "community_id", "user_id"], name: "index_by_tenant_community_teacher", unique: true
    t.index ["tenant_id"], name: "index_community_teachers_on_tenant_id"
    t.index ["user_id"], name: "index_community_teachers_on_user_id"
  end

  create_table "courses_affiliate_settings", force: :cascade do |t|
    t.decimal "commission_percentage", precision: 5, scale: 2
    t.string "landing_page_url"
    t.string "affiliate_invitation_link"
    t.text "description"
    t.string "attribution_model"
    t.integer "cookies_duration_time"
    t.integer "course_id"
    t.boolean "approval_requirement", default: false, null: false
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_courses_affiliate_settings_on_tenant_id"
  end

  create_table "courses_categories", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "migration_data"
    t.index ["tenant_id", "name"], name: "index_courses_categories_on_tenant_id_and_name", unique: true
    t.index ["tenant_id"], name: "index_courses_categories_on_tenant_id"
  end

  create_table "courses_course_teachers", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "course_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["course_id"], name: "index_courses_course_teachers_on_course_id"
    t.index ["tenant_id", "course_id", "user_id"], name: "index_by_tenant_course_teacher", unique: true
    t.index ["tenant_id"], name: "index_courses_course_teachers_on_tenant_id"
    t.index ["user_id"], name: "index_courses_course_teachers_on_user_id"
  end

  create_table "courses_courses", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "name", null: false
    t.bigint "category_id"
    t.text "description"
    t.date "start_date"
    t.date "end_date"
    t.boolean "has_expiration", default: false, null: false
    t.integer "expires_in_days"
    t.integer "price_cents"
    t.string "price_currency"
    t.integer "discounted_price_cents"
    t.string "discounted_price_currency"
    t.string "payment_platform"
    t.boolean "allow_comments", default: false, null: false
    t.boolean "featured", default: false, null: false
    t.boolean "has_certificate", default: false, null: false
    t.string "certificate_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_visible", default: false, null: false
    t.boolean "reviews", default: false, null: false
    t.jsonb "promo_picture_data"
    t.jsonb "promo_video_data"
    t.jsonb "certificate_picture_data"
    t.integer "certificate_generation_percentage", default: 60, null: false
    t.date "expiration_date"
    t.integer "my_modules_count", default: 0, null: false
    t.integer "lessons_count", default: 0, null: false
    t.bigint "created_by_id"
    t.integer "subscriptions_count", default: 0, null: false
    t.string "payment_link"
    t.jsonb "migration_data"
    t.jsonb "cover_picture_data"
    t.jsonb "video_preview_picture_data"
    t.jsonb "calendar_events", default: [], null: false
    t.string "seo_title"
    t.string "seo_description"
    t.boolean "is_free", default: true, null: false
    t.boolean "is_available_on_marketplace", default: false, null: false
    t.boolean "is_licensed", default: false, null: false
    t.boolean "show_languaje", default: false
    t.boolean "show_student_counter", default: false
    t.boolean "show_date_updated", default: false
    t.string "duration_course"
    t.integer "position"
    t.string "stripe_product_id"
    t.decimal "total_rating", precision: 5, scale: 4, default: "0.0"
    t.string "bunny_video_id"
    t.string "stripe_connect_product_id"
    t.boolean "migrated", default: false
    t.string "tiny_url"
    t.boolean "is_available_on_affiliate", default: false, null: false
    t.string "custom_url_name"
    t.boolean "certificate_qr_enabled", default: false
    t.string "certificate_qr_position", default: "bottom_center"
    t.string "certificate_color", default: "#647b9c"
    t.string "certificate_font", default: "poppins"
    t.string "certificate_date_alignment", default: "center"
    t.string "certificate_custom_text_alignment", default: "center"
    t.string "certificate_custom_text", default: ""
    t.jsonb "certificate_preview_data"
    t.boolean "show_course_duration", default: false
    t.boolean "certificate_custom_text_enabled", default: false
    t.index ["category_id"], name: "index_courses_courses_on_category_id"
    t.index ["created_by_id"], name: "index_courses_courses_on_created_by_id"
    t.index ["is_available_on_affiliate"], name: "index_courses_courses_on_is_available_on_affiliate"
    t.index ["is_available_on_marketplace"], name: "index_courses_courses_on_is_available_on_marketplace"
    t.index ["is_visible"], name: "index_courses_courses_on_is_visible"
    t.index ["tenant_id", "name"], name: "index_courses_courses_on_tenant_id_and_name", unique: true
    t.index ["tenant_id"], name: "index_courses_courses_on_tenant_id"
  end

  create_table "courses_courses_packs", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "course_id", null: false
    t.bigint "pack_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["course_id"], name: "index_courses_courses_packs_on_course_id"
    t.index ["pack_id", "course_id"], name: "index_courses_courses_packs_on_pack_id_and_course_id", unique: true
    t.index ["pack_id"], name: "index_courses_courses_packs_on_pack_id"
    t.index ["tenant_id"], name: "index_courses_courses_packs_on_tenant_id"
  end

  create_table "courses_exam_question_statistics", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "question_id", null: false
    t.integer "correct_response_time", default: 0, null: false, comment: "In seconds. For passed exams, the time it took to answer the question correctly"
    t.integer "incorrect_response_time", default: 0, null: false, comment: "In seconds. For passed exams, the time it took to answer the question incorrectly"
    t.integer "correct_response_count", default: 0, null: false, comment: "For passed exams, the number of times the question was answered correctly"
    t.integer "incorrect_response_count", default: 0, null: false, comment: "For passed exams, the number of times the question was answered incorrectly"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["question_id"], name: "index_courses_exam_question_statistics_on_question_id"
    t.index ["tenant_id"], name: "index_courses_exam_question_statistics_on_tenant_id"
  end

  create_table "courses_exam_questions", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "exam_id", null: false
    t.string "type", null: false
    t.integer "weight", null: false
    t.text "body", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position", null: false
    t.string "feedback"
    t.jsonb "picture_data"
    t.bigint "related_lesson_id"
    t.jsonb "answer_options", default: [], null: false
    t.jsonb "migration_data"
    t.index ["exam_id"], name: "index_courses_exam_questions_on_exam_id"
    t.index ["related_lesson_id"], name: "index_courses_exam_questions_on_related_lesson_id"
    t.index ["tenant_id"], name: "index_courses_exam_questions_on_tenant_id"
  end

  create_table "courses_lesson_audios", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "audio_data"
    t.jsonb "migration_data"
    t.bigint "content_id", null: false
    t.index ["content_id"], name: "index_courses_lesson_audios_on_content_id"
    t.index ["tenant_id"], name: "index_courses_lesson_audios_on_tenant_id"
  end

  create_table "courses_lesson_downloadables", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "downloadable_data"
    t.jsonb "migration_data"
    t.bigint "content_id", null: false
    t.index ["content_id"], name: "index_courses_lesson_downloadables_on_content_id"
    t.index ["tenant_id"], name: "index_courses_lesson_downloadables_on_tenant_id"
  end

  create_table "courses_lesson_exams", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.integer "duration_minutes"
    t.decimal "passing_percentage", precision: 5, scale: 4, null: false
    t.integer "max_retries", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "migration_data"
    t.jsonb "advanced_settings", default: {}
    t.index ["tenant_id"], name: "index_courses_lesson_exams_on_tenant_id"
  end

  create_table "courses_lesson_htmls", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.text "html", null: false
    t.text "css"
    t.text "js"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_courses_lesson_htmls_on_tenant_id"
  end

  create_table "courses_lesson_legacy_htmls", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.jsonb "html_file_data"
    t.jsonb "css_file_data"
    t.jsonb "migration_data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "js_file_data"
    t.index ["tenant_id"], name: "index_courses_lesson_legacy_htmls_on_tenant_id"
  end

  create_table "courses_lesson_pdfs", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "pdf_file_data"
    t.jsonb "migration_data"
    t.bigint "content_id", null: false
    t.index ["content_id"], name: "index_courses_lesson_pdfs_on_content_id"
    t.index ["tenant_id"], name: "index_courses_lesson_pdfs_on_tenant_id"
  end

  create_table "courses_lesson_presentations", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "content_id"
    t.jsonb "migration_data"
    t.jsonb "presentation_file_data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["content_id"], name: "index_courses_lesson_presentations_on_content_id"
    t.index ["tenant_id"], name: "index_courses_lesson_presentations_on_tenant_id"
  end

  create_table "courses_lesson_scorms", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "content_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "upload_status", default: "pending"
    t.text "upload_message"
    t.index ["content_id"], name: "index_courses_lesson_scorms_on_content_id"
    t.index ["tenant_id"], name: "index_courses_lesson_scorms_on_tenant_id"
  end

  create_table "courses_lesson_surveys", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "submit_button_text", default: "Submit"
    t.boolean "allow_anonymous", default: false
    t.text "thank_you_message"
    t.boolean "skippable", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_courses_lesson_surveys_on_tenant_id"
  end

  create_table "courses_lesson_videocalls", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.text "description"
    t.datetime "date", precision: nil, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "conferencing_provider", default: "jitsi", null: false
    t.jsonb "conferencing_data", default: {}, null: false
    t.string "student_link", null: false
    t.string "instructor_link", null: false
    t.jsonb "migration_data"
    t.boolean "hide_hour", default: false, null: false
    t.string "remider_job_sidekiq_id"
    t.index ["tenant_id"], name: "index_courses_lesson_videocalls_on_tenant_id"
  end

  create_table "courses_lesson_videos", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "video_data"
    t.jsonb "migration_data"
    t.bigint "content_id"
    t.string "external_url"
    t.boolean "is_url", default: false
    t.index ["content_id"], name: "index_courses_lesson_videos_on_content_id"
    t.index ["tenant_id"], name: "index_courses_lesson_videos_on_tenant_id"
  end

  create_table "courses_lesson_webinars", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "url"
    t.text "description"
    t.datetime "date", precision: nil, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "migration_data"
    t.boolean "hide_hour", default: false, null: false
    t.string "remider_job_sidekiq_id"
    t.index ["tenant_id"], name: "index_courses_lesson_webinars_on_tenant_id"
  end

  create_table "courses_lessons", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "my_module_id", null: false
    t.string "content_type", null: false
    t.string "title", null: false
    t.integer "position", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "allows_comments", default: true, null: false
    t.jsonb "migration_data"
    t.text "description"
    t.boolean "is_public", default: false, null: false
    t.index ["content_type"], name: "index_courses_lessons_on_content_type"
    t.index ["my_module_id"], name: "index_courses_lessons_on_my_module_id"
    t.index ["tenant_id", "my_module_id", "id"], name: "index_courses_lessons_on_tenant_id_and_my_module_id_and_id", unique: true
    t.index ["tenant_id"], name: "index_courses_lessons_on_tenant_id"
  end

  create_table "courses_my_modules", force: :cascade do |t|
    t.string "title", null: false
    t.bigint "tenant_id", null: false
    t.bigint "course_id", null: false
    t.integer "position"
    t.boolean "required_to_next", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "release_date", precision: nil
    t.jsonb "migration_data"
    t.index ["course_id"], name: "index_courses_my_modules_on_course_id"
    t.index ["tenant_id", "course_id", "title"], name: "index_courses_my_modules_on_tenant_id_and_course_id_and_title", unique: true
    t.index ["tenant_id"], name: "index_courses_my_modules_on_tenant_id"
  end

  create_table "courses_packs", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "name", null: false
    t.text "description"
    t.datetime "start_date", precision: nil
    t.date "end_date"
    t.jsonb "promo_picture_data"
    t.integer "price_cents"
    t.string "price_currency"
    t.integer "discounted_price_cents"
    t.string "discounted_price_currency"
    t.string "payment_platform"
    t.boolean "is_visible", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.date "expiration_date"
    t.string "payment_link"
    t.jsonb "migration_data"
    t.bigint "created_by_id"
    t.jsonb "promo_video_data"
    t.jsonb "cover_picture_data"
    t.jsonb "video_preview_picture_data"
    t.string "seo_title"
    t.string "seo_description"
    t.boolean "is_free", default: true, null: false
    t.string "stripe_product_id"
    t.string "bunny_video_id"
    t.string "stripe_connect_product_id"
    t.boolean "migrated", default: false
    t.string "tiny_url"
    t.index ["created_by_id"], name: "index_courses_packs_on_created_by_id"
    t.index ["tenant_id", "name"], name: "index_courses_packs_on_tenant_id_and_name", unique: true
    t.index ["tenant_id"], name: "index_courses_packs_on_tenant_id"
  end

  create_table "courses_survey_questions", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "survey_id", null: false
    t.string "type", null: false
    t.text "body", null: false
    t.integer "position", null: false
    t.bigint "related_lesson_id"
    t.jsonb "answer_options", default: [], null: false
    t.jsonb "migration_data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["related_lesson_id"], name: "index_courses_survey_questions_on_related_lesson_id"
    t.index ["survey_id"], name: "index_courses_survey_questions_on_exam_id"
    t.index ["tenant_id"], name: "index_courses_survey_questions_on_tenant_id"
  end

  create_table "custom_domains", force: :cascade do |t|
    t.string "domain", null: false
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["domain"], name: "index_custom_domains_on_domain", unique: true
    t.index ["tenant_id"], name: "index_custom_domains_on_tenant_id"
  end

  create_table "email_activities", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "broadcast_id", null: false
    t.bigint "user_id"
    t.string "activity_type", null: false
    t.string "email_address", null: false
    t.string "status", default: "pending", null: false
    t.string "message_id"
    t.string "provider"
    t.jsonb "provider_data", default: {}, null: false
    t.jsonb "metadata", default: {}, null: false
    t.datetime "occurred_at", default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["broadcast_id", "activity_type"], name: "index_email_activities_on_broadcast_id_and_activity_type"
    t.index ["broadcast_id"], name: "index_email_activities_on_broadcast_id"
    t.index ["message_id"], name: "index_email_activities_on_message_id"
    t.index ["tenant_id", "activity_type"], name: "index_email_activities_on_tenant_id_and_activity_type"
    t.index ["tenant_id", "broadcast_id"], name: "index_email_activities_on_tenant_id_and_broadcast_id"
    t.index ["tenant_id", "email_address"], name: "index_email_activities_on_tenant_id_and_email_address"
    t.index ["tenant_id", "occurred_at"], name: "index_email_activities_on_tenant_id_and_occurred_at"
    t.index ["tenant_id", "status"], name: "index_email_activities_on_tenant_id_and_status"
    t.index ["tenant_id"], name: "index_email_activities_on_tenant_id"
    t.index ["user_id", "activity_type"], name: "index_email_activities_on_user_id_and_activity_type"
    t.index ["user_id"], name: "index_email_activities_on_user_id"
  end

  create_table "emails_activities", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "broadcast_id", null: false
    t.bigint "user_id"
    t.string "email_address", null: false
    t.string "status", default: "pending", null: false
    t.string "message_id"
    t.string "error_message"
    t.integer "error_code"
    t.datetime "occurred_at", default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["broadcast_id"], name: "index_emails_activities_on_broadcast_id"
    t.index ["occurred_at"], name: "index_emails_activities_on_occurred_at"
    t.index ["tenant_id"], name: "index_emails_activities_on_tenant_id"
    t.index ["user_id"], name: "index_emails_activities_on_user_id"
  end

  create_table "emails_automations", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "email_id"
    t.boolean "is_default", default: false, null: false
    t.boolean "to_admin", default: false, null: false
    t.string "when", null: false
    t.jsonb "variables", default: [], null: false
    t.jsonb "jsonb", default: [], null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "entity_type"
    t.bigint "entity_ids", default: [], array: true
    t.index ["email_id"], name: "index_emails_automations_on_email_id"
    t.index ["entity_ids"], name: "index_emails_automations_on_entity_ids", using: :gin
    t.index ["entity_type"], name: "index_emails_automations_on_entity_type"
    t.index ["tenant_id"], name: "index_emails_automations_on_tenant_id"
  end

  create_table "emails_broadcasts", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "signature_id"
    t.bigint "template_id"
    t.bigint "filter_id"
    t.string "title", null: false
    t.string "subject", null: false
    t.string "sidekiq_id"
    t.text "body", null: false
    t.integer "sent", default: 0, null: false
    t.integer "delivered", default: 0, null: false
    t.integer "bounced", default: 0, null: false
    t.string "status", default: "draft", null: false
    t.string "scheduled_type", default: "send_now", null: false
    t.datetime "scheduled_at"
    t.string "sent_to", default: "all_members", null: false
    t.boolean "send_copy_to_sender", default: false, null: false
    t.boolean "signature_status", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["signature_id"], name: "index_emails_broadcasts_on_signature_id"
    t.index ["template_id"], name: "index_emails_broadcasts_on_template_id"
    t.index ["tenant_id"], name: "index_emails_broadcasts_on_tenant_id"
  end

  create_table "emails_emails", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "template_id"
    t.bigint "signature_id"
    t.string "title", null: false
    t.integer "sent", default: 0, null: false
    t.integer "delivered", default: 0, null: false
    t.integer "bounced", default: 0, null: false
    t.string "subject", null: false
    t.string "status", default: "active", null: false
    t.text "body", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["signature_id"], name: "index_emails_emails_on_signature_id"
    t.index ["template_id"], name: "index_emails_emails_on_template_id"
    t.index ["tenant_id", "title"], name: "index_emails_emails_on_tenant_id_and_title", unique: true
    t.index ["tenant_id"], name: "index_emails_emails_on_tenant_id"
  end

  create_table "emails_signatures", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "postmark_id", null: false
    t.string "email", null: false
    t.string "name", null: false
    t.string "reply"
    t.boolean "confirmed", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id", "email"], name: "index_emails_signatures_on_tenant_id_and_email", unique: true
    t.index ["tenant_id"], name: "index_emails_signatures_on_tenant_id"
  end

  create_table "emails_templates", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "name", null: false
    t.string "alias"
    t.jsonb "image_data"
    t.jsonb "style"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id", "name"], name: "index_emails_templates_on_tenant_id_and_name", unique: true
    t.index ["tenant_id"], name: "index_emails_templates_on_tenant_id"
  end

  create_table "exchange_rates", force: :cascade do |t|
    t.string "from", null: false
    t.string "to", null: false
    t.decimal "rate", precision: 10, scale: 6, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["from", "to"], name: "index_exchange_rates_on_from_and_to", unique: true
  end

  create_table "feature_plans", primary_key: "feature_plan", id: :string, force: :cascade do |t|
    t.string "site_customization", null: false
    t.boolean "custom_domain", null: false
    t.string "support_tier", null: false
    t.integer "storage_capacity_gb"
    t.jsonb "user_limits", null: false
    t.boolean "transactional_emails", null: false
    t.integer "active_course_limit"
    t.boolean "paid_courses", null: false
    t.boolean "mandatory_marketplace", null: false
    t.jsonb "restricted_lesson_types", default: [], null: false
    t.boolean "groups", null: false
    t.boolean "exams", null: false
    t.boolean "certificates", null: false
    t.boolean "bulk_uploads", null: false
    t.jsonb "media_variants", default: {}, null: false
    t.string "reports_tier", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "marketing_integrations", null: false
    t.boolean "packs", null: false
    t.boolean "create_user", default: false, null: false
    t.boolean "product_discounts", default: false, null: false
    t.boolean "custom_login_forms", default: false, null: false
    t.boolean "video_transcoding", default: false, null: false
    t.boolean "manual_course_pack_assignment", default: false, null: false
    t.boolean "zapier_integration", default: false, null: false
    t.boolean "subscriptions", default: false, null: false
    t.boolean "coupons", default: false, null: false
    t.boolean "disable_course", default: false, null: false
    t.boolean "manual_course_assignment", default: false, null: false
    t.boolean "audit_trail", default: false, null: false
    t.boolean "email_marketing", default: false, null: false
    t.boolean "automations", default: false, null: false
    t.integer "contact_limits", default: 0
    t.integer "website_limits", default: 0
    t.integer "community_limits", default: 0
    t.bigint "max_upload_size", default: 1073741824
  end

  create_table "filters", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "name", null: false
    t.text "description"
    t.string "list_type", default: "email_filter", null: false
    t.jsonb "filters", default: {}, null: false
    t.jsonb "metadata", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_filters_on_tenant_id"
  end

  create_table "gamification_badge_settings", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "gamification_setting_id", null: false
    t.string "title", null: false
    t.jsonb "icon_data"
    t.text "description"
    t.string "condition_type", null: false
    t.integer "points", default: 0
    t.boolean "enabled", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["gamification_setting_id"], name: "index_gamification_badge_settings_on_gamification_setting_id"
    t.index ["tenant_id"], name: "index_gamification_badge_settings_on_tenant_id"
  end

  create_table "gamification_level_settings", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "gamification_setting_id", null: false
    t.string "title", null: false
    t.jsonb "icon_data"
    t.integer "min_points", default: 0, null: false
    t.integer "max_points", null: false
    t.boolean "enabled", default: true
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["enabled"], name: "index_gamification_level_settings_on_enabled"
    t.index ["gamification_setting_id"], name: "index_gamification_level_settings_on_gamification_setting_id"
    t.index ["min_points"], name: "index_gamification_level_settings_on_min_points"
    t.index ["tenant_id"], name: "index_gamification_level_settings_on_tenant_id"
  end

  create_table "gamification_point_settings", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "gamification_setting_id", null: false
    t.string "action_type", null: false
    t.integer "points", default: 0
    t.string "description"
    t.boolean "enabled", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["gamification_setting_id"], name: "index_gamification_point_settings_on_gamification_setting_id"
    t.index ["tenant_id", "action_type"], name: "index_point_settings_on_tenant_and_action", unique: true
    t.index ["tenant_id"], name: "index_gamification_point_settings_on_tenant_id"
  end

  create_table "gamification_reward_settings", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "gamification_setting_id", null: false
    t.string "condition_type", null: false
    t.integer "condition_value", default: 0, null: false
    t.string "reward_type", null: false
    t.string "rewardable_type", null: false
    t.bigint "rewardable_id", null: false
    t.boolean "enabled", default: true
    t.boolean "apply_to_past_data", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["gamification_setting_id"], name: "index_gamification_reward_settings_on_gamification_setting_id"
    t.index ["rewardable_type", "rewardable_id"], name: "index_gamification_reward_settings_on_rewardable"
    t.index ["tenant_id"], name: "index_gamification_reward_settings_on_tenant_id"
  end

  create_table "gamification_settings", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.boolean "points_enabled", default: false
    t.boolean "badges_enabled", default: false
    t.boolean "configured_settings", default: false
    t.boolean "apply_to_past_data", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_gamification_settings_on_tenant_id"
  end

  create_table "gamification_user_badges", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "user_id", null: false
    t.bigint "badge_setting_id", null: false
    t.datetime "earned_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["badge_setting_id"], name: "index_gamification_user_badges_on_badge_setting_id"
    t.index ["tenant_id"], name: "index_gamification_user_badges_on_tenant_id"
    t.index ["user_id"], name: "index_gamification_user_badges_on_user_id"
  end

  create_table "gamification_user_points_activities", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "user_id", null: false
    t.string "action_type", null: false
    t.integer "points_earned", null: false
    t.date "activity_date", null: false
    t.datetime "earned_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_gamification_user_points_activities_on_tenant_id"
    t.index ["user_id"], name: "index_gamification_user_points_activities_on_user_id"
  end

  create_table "gamification_user_profiles", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "user_id", null: false
    t.bigint "level_setting_id", null: false
    t.integer "total_points", default: 0
    t.integer "current_level_progress", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["level_setting_id"], name: "index_gamification_user_profiles_on_level_setting_id"
    t.index ["tenant_id"], name: "index_gamification_user_profiles_on_tenant_id"
    t.index ["user_id"], name: "index_gamification_user_profiles_on_user_id", unique: true
  end

  create_table "gamification_user_rewards", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "user_id", null: false
    t.bigint "reward_setting_id", null: false
    t.datetime "claimed_at"
    t.integer "claimed_status"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["reward_setting_id"], name: "index_gamification_user_rewards_on_reward_setting_id"
    t.index ["tenant_id"], name: "index_gamification_user_rewards_on_tenant_id"
    t.index ["user_id"], name: "index_gamification_user_rewards_on_user_id"
  end

  create_table "groups_group_courses", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "group_id", null: false
    t.bigint "course_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["course_id"], name: "index_groups_group_courses_on_course_id"
    t.index ["group_id"], name: "index_groups_group_courses_on_group_id"
    t.index ["tenant_id", "group_id", "course_id"], name: "index_by_tenant_group_course", unique: true
    t.index ["tenant_id"], name: "index_groups_group_courses_on_tenant_id"
  end

  create_table "groups_group_instructors", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "group_id", null: false
    t.bigint "instructor_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["group_id"], name: "index_groups_group_instructors_on_group_id"
    t.index ["instructor_id"], name: "index_groups_group_instructors_on_instructor_id"
    t.index ["tenant_id", "group_id", "instructor_id"], name: "index_by_tenant_group_instructor", unique: true
    t.index ["tenant_id"], name: "index_groups_group_instructors_on_tenant_id"
  end

  create_table "groups_group_packs", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "group_id", null: false
    t.bigint "pack_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["group_id"], name: "index_groups_group_packs_on_group_id"
    t.index ["pack_id"], name: "index_groups_group_packs_on_pack_id"
    t.index ["tenant_id", "group_id", "pack_id"], name: "index_groups_group_packs_on_tenant_id_and_group_id_and_pack_id", unique: true
    t.index ["tenant_id"], name: "index_groups_group_packs_on_tenant_id"
  end

  create_table "groups_group_students", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "group_id", null: false
    t.bigint "student_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["group_id"], name: "index_groups_group_students_on_group_id"
    t.index ["student_id"], name: "index_groups_group_students_on_student_id"
    t.index ["tenant_id", "group_id", "student_id"], name: "index_by_tenant_group_student", unique: true
    t.index ["tenant_id"], name: "index_groups_group_students_on_tenant_id"
  end

  create_table "groups_groups", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "students_count", default: 0, null: false
    t.integer "instructors_count", default: 0, null: false
    t.integer "courses_count", default: 0, null: false
    t.integer "packs_count", default: 0, null: false
    t.jsonb "migration_data"
    t.index ["tenant_id", "name"], name: "index_groups_groups_on_tenant_id_and_name", unique: true
    t.index ["tenant_id"], name: "index_groups_groups_on_tenant_id"
  end

  create_table "invoices_invoices", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "invoice_id"
    t.integer "amount_cents", null: false
    t.string "amount_currency", null: false
    t.string "provider", default: "stripe", null: false
    t.string "description", null: false
    t.string "status", null: false
    t.string "charge_id"
    t.string "type", default: "Invoices::Subscription", null: false
    t.datetime "payment_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "hosted_invoice_url"
    t.index ["tenant_id"], name: "index_invoices_invoices_on_tenant_id"
  end

  create_table "marketplaces_categories", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "type", default: "Marketplaces::Category"
    t.index ["name"], name: "index_marketplaces_categories_on_name", unique: true
  end

  create_table "marketplaces_courses_categories", force: :cascade do |t|
    t.bigint "category_id", null: false
    t.bigint "course_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["category_id", "course_id"], name: "index_courses_categories_on_category_id_and_course_id", unique: true
    t.index ["category_id"], name: "index_marketplaces_courses_categories_on_category_id"
    t.index ["course_id"], name: "index_marketplaces_courses_categories_on_course_id"
  end

  create_table "marketplaces_licenses", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "course_id", null: false
    t.string "license_type", null: false
    t.integer "quantity_purchased", default: 0, null: false
    t.integer "quantity_used", default: 0, null: false
    t.bigint "licensed_by_tenant", null: false
    t.bigint "licensed_by_course", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "name"
    t.jsonb "promo_picture_data"
    t.jsonb "promo_video_data"
    t.jsonb "cover_picture_data"
    t.jsonb "video_preview_picture_data"
    t.string "stripe_connect_product_id"
    t.string "stripe_product_id"
    t.index ["course_id"], name: "index_marketplaces_licenses_on_course_id"
    t.index ["tenant_id"], name: "index_marketplaces_licenses_on_tenant_id"
  end

  create_table "marketplaces_relation_categories", force: :cascade do |t|
    t.integer "main_id"
    t.integer "branch_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "media_lesson_contents", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "file_type", null: false
    t.string "file_name", null: false
    t.jsonb "file_data", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "lesson_videos_count", default: 0, null: false
    t.integer "lesson_audios_count", default: 0, null: false
    t.integer "lesson_pdfs_count", default: 0, null: false
    t.integer "lesson_downloadables_count", default: 0, null: false
    t.virtual "lessons_count", type: :integer, as: "(((lesson_videos_count + lesson_audios_count) + lesson_pdfs_count) + lesson_downloadables_count)", stored: true
    t.integer "lesson_scorms_count", default: 0, null: false
    t.boolean "is_licensed", default: false, null: false
    t.string "bunny_video_id", comment: "Bunny CDN Video ID for the lesson content video"
    t.boolean "migrated", default: false
    t.string "scorm_cloud_course_id"
    t.string "scorm_storage_provider"
    t.integer "lesson_presentations_count", default: 0, null: false
    t.index ["tenant_id", "file_type"], name: "index_media_lesson_contents_on_tenant_id_and_file_type"
    t.index ["tenant_id"], name: "index_media_lesson_contents_on_tenant_id"
  end

  create_table "netlify_sites", force: :cascade do |t|
    t.string "site_id", null: false
    t.string "site_name", null: false
    t.integer "domain_aliases_count", default: 0, null: false
    t.boolean "active", default: true, null: false
    t.json "metadata", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active", "domain_aliases_count"], name: "index_netlify_sites_on_active_and_domain_count"
    t.index ["active"], name: "index_netlify_sites_on_active"
    t.index ["domain_aliases_count"], name: "index_netlify_sites_on_domain_aliases_count"
    t.index ["site_id"], name: "index_netlify_sites_on_site_id", unique: true
  end

  create_table "network_rest_webhooks", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.jsonb "events", null: false
    t.string "target", null: false
    t.boolean "active", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_network_rest_webhooks_on_tenant_id"
  end

  create_table "notices", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "url"
    t.jsonb "image_data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_notices_on_tenant_id"
  end

  create_table "notification_players", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "player_id", null: false
    t.string "device_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_notification_players_on_user_id"
  end

  create_table "platform_admins", force: :cascade do |t|
    t.string "email", null: false
    t.string "crypted_password"
    t.string "salt"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_token_expires_at"
    t.datetime "reset_password_email_sent_at"
    t.integer "access_count_to_reset_password_page", default: 0
    t.integer "failed_logins_count", default: 0
    t.datetime "lock_expires_at"
    t.string "unlock_token"
    t.datetime "last_login_at"
    t.datetime "last_logout_at"
    t.datetime "last_activity_at"
    t.string "last_login_from_ip_address"
    t.string "first_name"
    t.string "last_name"
    t.index ["email"], name: "index_platform_admins_on_email", unique: true
    t.index ["last_logout_at", "last_activity_at"], name: "index_platform_admins_on_last_logout_at_and_last_activity_at"
    t.index ["reset_password_token"], name: "index_platform_admins_on_reset_password_token"
    t.index ["unlock_token"], name: "index_platform_admins_on_unlock_token"
  end

  create_table "purchases_coupon_products", force: :cascade do |t|
    t.string "product_type", null: false
    t.bigint "product_id", null: false
    t.bigint "coupon_id", null: false
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["coupon_id"], name: "index_purchases_coupon_products_on_coupon_id"
    t.index ["product_type", "product_id"], name: "index_purchases_coupon_products_on_product"
    t.index ["tenant_id", "product_id", "product_type", "coupon_id"], name: "index_coupon_products_on_tenant_and_product_and_coupon", unique: true
    t.index ["tenant_id"], name: "index_purchases_coupon_products_on_tenant_id"
  end

  create_table "purchases_coupons", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "code", null: false
    t.string "description"
    t.integer "discount_percent"
    t.date "start_date", null: false
    t.date "end_date", null: false
    t.string "type", default: "Purchases::CouponPriceDiscount"
    t.integer "quantity", null: false
    t.integer "quantity_used", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "enabled", default: true, null: false
    t.integer "trial_period_days"
    t.index ["tenant_id", "code"], name: "index_purchases_coupons_on_tenant_id_and_code", unique: true
    t.index ["tenant_id"], name: "index_purchases_coupons_on_tenant_id"
  end

  create_table "purchases_payment_methods", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "name", null: false
    t.string "type", null: false
    t.jsonb "config", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id", "name"], name: "index_purchases_payment_methods_on_tenant_id_and_name", unique: true
    t.index ["tenant_id"], name: "index_purchases_payment_methods_on_tenant_id"
  end

  create_table "purchases_prices", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "product_type", null: false
    t.bigint "product_id", null: false
    t.integer "price_cents", null: false
    t.string "currency", null: false
    t.string "payment_link"
    t.integer "discounted_price_cents"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "subscription", default: false, null: false
    t.string "provider_plan_id"
    t.integer "trial_period_days"
    t.string "stripe_price_id"
    t.integer "mercado_pago_installments", default: 6, null: false
    t.string "stripe_connect_price_id"
    t.boolean "installments_enabled", default: false
    t.string "thank_you_page_url"
    t.boolean "thank_you_page_enabled", default: false
    t.index ["tenant_id", "product_type", "product_id", "currency"], name: "index_prices_on_tenant_and_product_and_currency", unique: true
    t.index ["tenant_id"], name: "index_purchases_prices_on_tenant_id"
  end

  create_table "purchases_prices_payment_methods", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "price_id", null: false
    t.bigint "payment_method_id", null: false
    t.index ["payment_method_id"], name: "index_payment_methods"
    t.index ["price_id"], name: "index_purchases_prices_payment_methods_on_price_id"
    t.index ["tenant_id"], name: "index_purchases_prices_payment_methods_on_tenant_id"
  end

  create_table "purchases_product_purchases", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "user_id", null: false
    t.string "product_type", null: false
    t.bigint "product_id", null: false
    t.string "status", null: false
    t.string "provider", null: false
    t.jsonb "provider_data", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "amount_cents", null: false
    t.string "amount_currency", null: false
    t.string "origin", null: false
    t.string "provider_id", null: false
    t.bigint "price_id"
    t.bigint "payment_method_id"
    t.bigint "coupon_id"
    t.integer "quantity", default: 1, null: false
    t.string "payment_type", default: "single", null: false
    t.boolean "visibility", default: true, null: false
    t.index ["coupon_id"], name: "index_purchases_product_purchases_on_coupon_id"
    t.index ["payment_method_id"], name: "index_purchases_product_purchases_on_payment_method_id"
    t.index ["price_id"], name: "index_purchases_product_purchases_on_price_id"
    t.index ["product_type", "product_id"], name: "index_transactions_on_product"
    t.index ["tenant_id"], name: "index_purchases_product_purchases_on_tenant_id"
    t.index ["user_id"], name: "index_purchases_product_purchases_on_user_id"
  end

  create_table "purchases_refund_requests", force: :cascade do |t|
    t.bigint "product_purchase_id", null: false
    t.bigint "tenant_id", null: false
    t.string "status", default: "pending", null: false
    t.string "stripe_refund_id"
    t.string "transfer_reversal_id"
    t.text "reason", null: false
    t.datetime "approved_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_purchase_id"], name: "index_purchases_refund_requests_on_product_purchase_id"
    t.index ["tenant_id"], name: "index_purchases_refund_requests_on_tenant_id"
  end

  create_table "report_tabs", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_report_tabs_on_tenant_id"
  end

  create_table "reports", force: :cascade do |t|
    t.string "name", null: false
    t.jsonb "content"
    t.string "chart_type"
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "report_tab_id"
    t.text "default_chart_name"
    t.index ["report_tab_id"], name: "index_reports_on_report_tab_id"
    t.index ["tenant_id"], name: "index_reports_on_tenant_id"
  end

  create_table "sabionet_users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "provider", default: "email", null: false
    t.string "uid", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.string "confirmation_token"
    t.datetime "confirmed_at"
    t.datetime "confirmation_sent_at"
    t.string "unconfirmed_email"
    t.string "first_name"
    t.string "last_name"
    t.string "phone"
    t.jsonb "avatar_data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["confirmation_token"], name: "index_sabionet_users_on_confirmation_token", unique: true
    t.index ["email"], name: "index_sabionet_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_sabionet_users_on_reset_password_token", unique: true
  end

  create_table "social_comments", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "commentable_type", null: false
    t.bigint "commentable_id", null: false
    t.bigint "author_id", null: false
    t.text "content", null: false
    t.jsonb "attachment_data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["author_id"], name: "index_social_comments_on_author_id"
    t.index ["commentable_type", "commentable_id"], name: "index_social_comments_on_commentable"
    t.index ["tenant_id"], name: "index_social_comments_on_tenant_id"
  end

  create_table "social_reactions", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "reactionable_type", null: false
    t.bigint "reactionable_id", null: false
    t.bigint "author_id", null: false
    t.string "type", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["author_id"], name: "index_social_reactions_on_author_id"
    t.index ["reactionable_type", "reactionable_id"], name: "index_social_reactions_on_reactionable"
    t.index ["tenant_id", "reactionable_type", "reactionable_id", "author_id"], name: "index_reactions_on_tenant_and_reactionable and_author", unique: true
    t.index ["tenant_id"], name: "index_social_reactions_on_tenant_id"
  end

  create_table "student_certificates", force: :cascade do |t|
    t.string "academy_name", null: false
    t.string "student_full_name", null: false
    t.string "student_email", null: false
    t.string "course_title", null: false
    t.text "course_description"
    t.jsonb "modules_data"
    t.float "total_study_time"
    t.decimal "final_grade_average", precision: 5, scale: 2
    t.string "certificate_qr_code"
    t.datetime "issued_at"
    t.jsonb "completion_certificate_data"
    t.jsonb "student_avatar_data"
    t.bigint "subscription_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["subscription_id"], name: "index_student_certificates_on_subscription_id"
  end

  create_table "subscriptions_exam_answers", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "type", null: false
    t.text "answer_text"
    t.integer "answer_option_ids", array: true
    t.jsonb "answer_file_data"
    t.bigint "exam_attempt_id", null: false
    t.bigint "question_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "scored_by_id"
    t.float "instructor_score"
    t.string "instructor_feedback"
    t.jsonb "answer_data"
    t.virtual "completion_status", type: :string, as: "\nCASE\n    WHEN (instructor_score IS NULL) THEN 'unscored'::text\n    WHEN (instructor_score >= (0.6)::double precision) THEN 'correct'::text\n    ELSE 'incorrect'::text\nEND", stored: true
    t.boolean "instructor_feedback_useful"
    t.jsonb "migration_data"
    t.integer "time_taken", default: 0, comment: "Time taken to answer the question in seconds."
    t.index ["exam_attempt_id"], name: "index_subscriptions_exam_answers_on_exam_attempt_id"
    t.index ["question_id"], name: "index_subscriptions_exam_answers_on_question_id"
    t.index ["scored_by_id"], name: "index_subscriptions_exam_answers_on_scored_by_id"
    t.index ["tenant_id", "question_id", "exam_attempt_id"], name: "index_exam_answers_on_tenant_and_question_and_attempt", unique: true
    t.index ["tenant_id"], name: "index_subscriptions_exam_answers_on_tenant_id"
  end

  create_table "subscriptions_exam_attempts", force: :cascade do |t|
    t.string "status", default: "initiated", null: false
    t.integer "attempt_number", null: false
    t.bigint "subscription_id", null: false
    t.bigint "exam_id", null: false
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "total_score", default: 0.0
    t.datetime "finished_at"
    t.index ["exam_id"], name: "index_subscriptions_exam_attempts_on_exam_id"
    t.index ["subscription_id"], name: "index_subscriptions_exam_attempts_on_subscription_id"
    t.index ["tenant_id"], name: "index_subscriptions_exam_attempts_on_tenant_id"
  end

  create_table "subscriptions_exam_questions", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "exam_attempt_id", null: false
    t.bigint "question_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["exam_attempt_id"], name: "index_subscriptions_exam_questions_on_exam_attempt_id"
    t.index ["question_id"], name: "index_subscriptions_exam_questions_on_question_id"
    t.index ["tenant_id"], name: "index_subscriptions_exam_questions_on_tenant_id"
  end

  create_table "subscriptions_extra_exam_attempts", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "exam_id", null: false
    t.bigint "subscription_id", null: false
    t.integer "attempts", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["exam_id"], name: "index_subscriptions_extra_exam_attempts_on_exam_id"
    t.index ["subscription_id"], name: "index_subscriptions_extra_exam_attempts_on_subscription_id"
    t.index ["tenant_id", "exam_id", "subscription_id"], name: "index_by_tenant_exam_subscription", unique: true
    t.index ["tenant_id"], name: "index_subscriptions_extra_exam_attempts_on_tenant_id"
  end

  create_table "subscriptions_progresses", force: :cascade do |t|
    t.bigint "subscription_id", null: false
    t.bigint "lesson_id", null: false
    t.bigint "tenant_id", null: false
    t.decimal "percentage", precision: 5, scale: 4, default: "1.0", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["lesson_id"], name: "index_subscriptions_progresses_on_lesson_id"
    t.index ["subscription_id"], name: "index_subscriptions_progresses_on_subscription_id"
    t.index ["tenant_id", "subscription_id", "lesson_id"], name: "progress_by_tenant_subscription_and_lesson_index", unique: true
    t.index ["tenant_id"], name: "index_subscriptions_progresses_on_tenant_id"
  end

  create_table "subscriptions_student_community_accesses", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "subscription_id", null: false
    t.bigint "creator_id", null: false
    t.bigint "product_purchase_id"
    t.bigint "community_id"
    t.string "source", null: false
    t.date "expiration_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["community_id"], name: "index_subscriptions_student_community_accesses_on_community_id"
    t.index ["creator_id"], name: "index_subscriptions_student_community_accesses_on_creator_id"
    t.index ["product_purchase_id"], name: "index_community_access_on_product_purchase"
    t.index ["subscription_id"], name: "index_community_access_on_subscription"
    t.index ["tenant_id"], name: "index_subscriptions_student_community_accesses_on_tenant_id"
  end

  create_table "subscriptions_student_course_accesses", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "subscription_id", null: false
    t.bigint "creator_id", null: false
    t.bigint "product_purchase_id"
    t.bigint "group_id"
    t.bigint "pack_id"
    t.string "source", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.date "expiration_date"
    t.bigint "license_id"
    t.index ["creator_id"], name: "index_subscriptions_student_course_accesses_on_creator_id"
    t.index ["group_id"], name: "index_subscriptions_student_course_accesses_on_group_id"
    t.index ["license_id"], name: "index_subscriptions_student_course_accesses_on_license_id"
    t.index ["pack_id"], name: "index_subscriptions_student_course_accesses_on_pack_id"
    t.index ["product_purchase_id"], name: "index_student_course_accesses_on_product_purchase"
    t.index ["subscription_id"], name: "index_subscriptions_student_course_accesses_on_subscription_id"
    t.index ["tenant_id"], name: "index_subscriptions_student_course_accesses_on_tenant_id"
  end

  create_table "subscriptions_student_reviews", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "subscription_id", null: false
    t.text "body", null: false
    t.string "status", default: "pending", null: false
    t.decimal "rating", precision: 5, scale: 4, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id", "subscription_id"], name: "index_student_reviews_on_tenant_and_subscription", unique: true
  end

  create_table "subscriptions_subscriptions", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "student_id", null: false
    t.bigint "course_id"
    t.float "total_progress", default: 0.0, null: false
    t.jsonb "completion_certificate_data"
    t.boolean "disabled", default: false, null: false
    t.string "completion_certificate_reference_code"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "pack_id"
    t.date "expiration_date"
    t.datetime "completion_certificate_generated_at", precision: nil
    t.integer "max_progress", null: false
    t.virtual "completion_percentage", type: :float, as: "\nCASE\n    WHEN (max_progress = 0) THEN NULL::double precision\n    ELSE (total_progress / (max_progress)::double precision)\nEND", stored: true
    t.string "review", default: "not_required", null: false
    t.virtual "completion_status", type: :string, as: "\nCASE\n    WHEN (max_progress = 0) THEN 'INVALID'::text\n    WHEN (total_progress = (0)::double precision) THEN 'NOT_STARTED'::text\n    WHEN ((total_progress > (0)::double precision) AND ((total_progress / (max_progress)::double precision) < (1)::double precision)) THEN 'IN_PROGRESS'::text\n    WHEN ((review)::text = 'pending'::text) THEN 'IN_PROGRESS'::text\n    WHEN (((total_progress / (max_progress)::double precision) = (1)::double precision) AND (((review)::text = 'not_required'::text) OR ((review)::text = 'send'::text))) THEN 'FINISHED'::text\n    ELSE NULL::text\nEND", stored: true
    t.string "type", default: "Subscriptions::FixedSubscription"
    t.string "status"
    t.jsonb "last_seen", default: {"lesson_id"=>nil, "module_id"=>nil}, null: false
    t.string "stripe_subscription_id"
    t.string "stripe_connect_subscription_id"
    t.integer "community_id"
    t.float "time_spent_in_hours", default: 0.0
    t.index ["course_id"], name: "index_subscriptions_subscriptions_on_course_id"
    t.index ["pack_id"], name: "index_subscriptions_subscriptions_on_pack_id"
    t.index ["student_id"], name: "index_subscriptions_subscriptions_on_student_id"
    t.index ["tenant_id", "student_id", "course_id"], name: "index_subscriptions_on_tenant_student_course", unique: true
    t.index ["tenant_id"], name: "index_subscriptions_subscriptions_on_tenant_id"
  end

  create_table "subscriptions_survey_answers", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "type", null: false
    t.bigint "survey_response_id", null: false
    t.bigint "question_id", null: false
    t.bigint "user_id"
    t.jsonb "answer_data"
    t.jsonb "migration_data"
    t.integer "time_taken", default: 0, comment: "Time taken to answer the question in seconds."
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["question_id"], name: "index_subscriptions_survey_answers_on_question_id"
    t.index ["survey_response_id"], name: "index_subscriptions_survey_answers_on_survey_response_id"
    t.index ["tenant_id", "question_id", "survey_response_id"], name: "index_survey_answers_on_tenant_and_question_and_response", unique: true
    t.index ["tenant_id"], name: "index_subscriptions_survey_answers_on_tenant_id"
    t.index ["user_id"], name: "index_subscriptions_survey_answers_on_user_id"
  end

  create_table "subscriptions_survey_questions", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "survey_response_id", null: false
    t.bigint "question_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["question_id"], name: "index_subscriptions_survey_questions_on_question_id"
    t.index ["survey_response_id"], name: "index_subscriptions_survey_questions_on_survey_response_id"
    t.index ["tenant_id"], name: "index_subscriptions_survey_questions_on_tenant_id"
  end

  create_table "subscriptions_survey_responses", force: :cascade do |t|
    t.string "status", default: "initiated", null: false
    t.integer "attempt_number", default: 0, null: false
    t.bigint "subscription_id"
    t.bigint "survey_id", null: false
    t.bigint "tenant_id", null: false
    t.datetime "finished_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["subscription_id"], name: "index_subscriptions_survey_responses_on_subscription_id"
    t.index ["survey_id"], name: "index_subscriptions_survey_responses_on_survey_id"
    t.index ["tenant_id"], name: "index_subscriptions_survey_responses_on_tenant_id"
  end

  create_table "tags", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_tags_on_tenant_id"
  end

  create_table "tags_users", id: false, force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "tag_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tag_id"], name: "index_tags_users_on_tag_id"
    t.index ["user_id", "tag_id"], name: "index_tags_users_on_user_id_and_tag_id", unique: true
    t.index ["user_id"], name: "index_tags_users_on_user_id"
  end

  create_table "templates", force: :cascade do |t|
    t.string "name"
    t.boolean "active", default: true
    t.jsonb "thumbnail_data"
    t.jsonb "preview_data"
    t.bigint "website_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["website_id"], name: "index_templates_on_website_id"
  end

  create_table "tenant_metrics", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.integer "subscription_type", default: 2, null: false
    t.boolean "is_customer", default: false
    t.boolean "payment_method_setuped", default: false
    t.integer "pages_created", default: 0
    t.integer "super_admin_session_count", default: 0
    t.decimal "revenue_usd", precision: 10, scale: 2, default: "0.0", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "utm_gl_data"
    t.integer "payment_methods_count", default: 0
    t.date "subscription_date"
    t.date "first_subscription_date"
    t.index ["tenant_id"], name: "index_tenant_metrics_on_tenant_id"
  end

  create_table "tenants", force: :cascade do |t|
    t.citext "subdomain", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.citext "country", null: false
    t.string "language", null: false
    t.string "timezone", null: false
    t.string "default_currency", null: false
    t.string "school_name"
    t.text "school_description"
    t.citext "contact_email"
    t.string "feature_plan_id", default: "free", null: false
    t.string "contact_number"
    t.integer "courses_count", default: 0, null: false
    t.integer "students_count", default: 0, null: false
    t.integer "admins_count", default: 0, null: false
    t.integer "instructors_count", default: 0, null: false
    t.date "feature_plan_expiration_date"
    t.date "trial_expiration_date"
    t.boolean "disabled", default: false, null: false
    t.string "google_tag_manager"
    t.string "facebook_pixel"
    t.string "google_analytics"
    t.float "private_storage_used_mib", default: 0.0, null: false
    t.float "public_storage_used_mib", default: 0.0, null: false
    t.datetime "storage_last_calculated_at", precision: nil
    t.string "mercado_pago_public_key"
    t.string "mercado_pago_secret_access_token"
    t.string "pay_pal_client_id"
    t.string "pay_pal_client_secret"
    t.string "stripe_api_key"
    t.citext "domain"
    t.jsonb "migration_data"
    t.jsonb "phone_field_config", default: {"savePhone"=>false, "requirePhone"=>false}, null: false
    t.jsonb "custom_fields_config", default: {}, null: false
    t.integer "hubspot_id"
    t.string "stripe_customer_id"
    t.string "seo_title"
    t.string "seo_description"
    t.boolean "is_trial", default: true
    t.virtual "used_storage_mib", type: :float, as: "(private_storage_used_mib + public_storage_used_mib)", stored: true
    t.boolean "login_page_only", default: false, null: false
    t.jsonb "logo_light_data"
    t.jsonb "logo_dark_data"
    t.jsonb "favicon_data"
    t.string "api_key"
    t.string "project_type"
    t.jsonb "data_protection"
    t.boolean "video_transcoding", default: false
    t.boolean "enabled_whatsapp", default: false, null: false
    t.string "link_whatsapp"
    t.string "mercado_pago_application_id"
    t.string "color_mode_light", default: "#C03BFF"
    t.string "color_mode_dark", default: "#CA75FF"
    t.boolean "dark_mode", default: false, null: false
    t.boolean "agree_new_plan", default: false, null: false
    t.jsonb "stripe_payment_methods", default: [], null: false
    t.jsonb "extra_fields", default: {"welcome_video"=>true}, null: false
    t.string "bunny_api_key", comment: "Bunny CDN API Key specific to the tenant"
    t.integer "bunny_video_library_id", comment: "Bunny CDN Video Library ID under which collections will be created for the tenant courses"
    t.string "bunny_token_security_key", comment: "Bunny CDN token security key used to create a secure video URL"
    t.string "facebook_id"
    t.string "tiktok_id"
    t.integer "communities_count", default: 0, null: false
    t.boolean "show_register", default: true
    t.float "bunny_storage_used_mib", default: 0.0, null: false
    t.integer "max_login_sessions", default: 3
    t.boolean "requires_verification", default: false
    t.boolean "restrict_sessions", default: false
    t.string "mailchimp_api_key"
    t.string "mailchimp_list_id"
    t.boolean "has_completed_onboarding", default: false, null: false
    t.jsonb "email_logo_data"
    t.boolean "mixed_theme", default: true, null: false
    t.boolean "gamification_enabled", default: false
    t.boolean "mandrill_enabled"
    t.string "mandrill_api_key"
    t.index ["api_key"], name: "index_tenants_on_api_key", unique: true
    t.index ["domain"], name: "index_tenants_on_domain", unique: true
    t.index ["feature_plan_id"], name: "index_tenants_on_feature_plan_id"
    t.index ["hubspot_id"], name: "index_tenants_on_hubspot_id", unique: true
    t.index ["stripe_customer_id"], name: "index_tenants_on_stripe_customer_id"
    t.index ["subdomain"], name: "index_tenants_on_subdomain", unique: true
  end

  create_table "triggers", force: :cascade do |t|
    t.string "when"
    t.string "action"
    t.string "when_entity_type"
    t.string "action_entity_type"
    t.bigint "when_entity_ids", default: [], array: true
    t.bigint "action_entity_ids", default: [], array: true
    t.bigint "tenant_id", null: false
    t.bigint "course_id"
    t.boolean "apply_to_past_entities", default: false, null: false
    t.index ["course_id"], name: "index_triggers_on_course_id"
    t.index ["tenant_id"], name: "index_triggers_on_tenant_id"
  end

  create_table "user_activities", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "tenant_id", null: false
    t.float "time_spent_in_hours", default: 0.0
    t.date "activity_date", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id", "activity_date"], name: "index_user_activities_on_tenant_id_and_activity_date"
    t.index ["tenant_id"], name: "index_user_activities_on_tenant_id"
    t.index ["user_id", "tenant_id", "activity_date"], name: "index_user_activities_on_user_tenant_date", unique: true
    t.index ["user_id"], name: "index_user_activities_on_user_id"
  end

  create_table "user_filters", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "name", null: false
    t.text "description"
    t.string "list_type", default: "email_filter", null: false
    t.jsonb "filters", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id", "list_type", "name"], name: "index_user_filters_on_tenant_id_and_list_type_and_name", unique: true
    t.index ["tenant_id"], name: "index_user_filters_on_tenant_id"
  end

  create_table "user_sessions", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "ip_address"
    t.string "user_agent"
    t.string "sign_in_token"
    t.datetime "expires_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_user_sessions_on_user_id"
  end

  create_table "user_social_networks", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "tenant_id", null: false
    t.string "platform"
    t.string "profile_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_user_social_networks_on_tenant_id"
    t.index ["user_id"], name: "index_user_social_networks_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.bigint "tenant_id"
    t.string "type", null: false
    t.string "provider", default: "email", null: false
    t.string "uid", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.boolean "allow_password_change", default: false
    t.datetime "remember_created_at", precision: nil
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at", precision: nil
    t.datetime "last_sign_in_at", precision: nil
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "confirmation_token"
    t.datetime "confirmed_at", precision: nil
    t.datetime "confirmation_sent_at", precision: nil
    t.integer "failed_attempts", default: 0, null: false
    t.string "unlock_token"
    t.datetime "locked_at", precision: nil
    t.string "first_name", null: false
    t.string "last_name"
    t.jsonb "avatar_data"
    t.citext "email", null: false
    t.json "tokens"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "admin_role_id"
    t.text "bio"
    t.string "country", default: "", null: false
    t.string "language", default: "", null: false
    t.string "timezone", default: "", null: false
    t.datetime "last_activity_at", precision: nil
    t.string "cube_js_token_jwt"
    t.datetime "cube_js_token_expiration_date"
    t.jsonb "migration_data"
    t.string "default_avatar_url"
    t.jsonb "phone"
    t.jsonb "custom_fields", default: {}, null: false
    t.bigint "hubspot_id"
    t.jsonb "subscription_failed"
    t.boolean "dark_mode", default: false, null: false
    t.string "stripe_customer_id"
    t.date "last_day_activity"
    t.string "stripe_connect_customer_id"
    t.string "affiliate_invitation_token"
    t.integer "affiliate_status", default: 0, null: false
    t.string "affiliate_confirmation_token"
    t.datetime "affiliate_confirmed_at"
    t.boolean "affiliate_user", default: false
    t.bigint "affiliate_id"
    t.string "login_verification_code"
    t.string "mailchimp_id"
    t.integer "name_change_count", default: 0, null: false
    t.index ["admin_role_id"], name: "index_users_on_admin_role_id"
    t.index ["affiliate_invitation_token"], name: "index_users_on_affiliate_invitation_token", unique: true
    t.index ["email"], name: "index_users_on_email"
    t.index ["tenant_id", "confirmation_token"], name: "index_users_on_tenant_id_and_confirmation_token", unique: true
    t.index ["tenant_id", "email"], name: "index_users_on_tenant_id_and_email", unique: true
    t.index ["tenant_id", "hubspot_id"], name: "index_users_on_tenant_id_and_hubspot_id", unique: true
    t.index ["tenant_id", "phone"], name: "index_users_on_tenant_id_and_phone", unique: true
    t.index ["tenant_id", "reset_password_token"], name: "index_users_on_tenant_id_and_reset_password_token", unique: true
    t.index ["tenant_id", "uid", "provider"], name: "index_users_on_tenant_id_and_uid_and_provider", unique: true
    t.index ["tenant_id", "unlock_token"], name: "index_users_on_tenant_id_and_unlock_token", unique: true
    t.index ["tenant_id"], name: "index_users_on_tenant_id"
  end

  create_table "users_tenants", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_users_tenants_on_tenant_id"
    t.index ["user_id"], name: "index_users_tenants_on_user_id"
  end

  create_table "website_footer_columns", force: :cascade do |t|
    t.bigint "tenant_id"
    t.bigint "website_footer_id", null: false
    t.string "title", null: false
    t.jsonb "link_rows", default: [], null: false
    t.integer "position", default: 1, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["link_rows"], name: "index_website_footer_columns_on_link_rows", using: :gin
    t.index ["position"], name: "index_website_footer_columns_on_position"
    t.index ["tenant_id"], name: "index_website_footer_columns_on_tenant_id"
    t.index ["website_footer_id"], name: "index_website_footer_columns_on_website_footer_id"
  end

  create_table "website_footers", force: :cascade do |t|
    t.bigint "tenant_id"
    t.bigint "website_id", null: false
    t.string "template_type", default: "compact_contact"
    t.string "social_links_style", default: "only_icons"
    t.string "bg_text_icon_color", default: "#D9D9D9"
    t.boolean "bg_color_visibility", default: true
    t.boolean "bg_image_visibility", default: false
    t.jsonb "bg_image_data"
    t.boolean "contact_information_visibility", default: true
    t.string "contact_information_title", default: "Contact Us"
    t.string "contact_information_address"
    t.boolean "social_networks_visibility", default: true
    t.boolean "columns_visibility", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_website_footers_on_tenant_id"
    t.index ["website_id"], name: "index_website_footers_on_website_id"
  end

  create_table "websites", force: :cascade do |t|
    t.bigint "tenant_id"
    t.string "main_color", default: "#AAAAAA", null: false
    t.integer "header_logo_size", default: 100, null: false
    t.string "header_color", default: "#FFFFFF", null: false
    t.boolean "header_transparent", default: false, null: false
    t.string "footer_copyright_label", default: "", null: false
    t.string "footer_color", default: "#A270FE", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "header_logo_data"
    t.jsonb "header_logo_sticky_data"
    t.jsonb "social_links", default: [], null: false
    t.jsonb "header_links", default: [], null: false
    t.jsonb "school_favicon_data"
    t.jsonb "school_logo_data"
    t.jsonb "migration_data"
    t.string "header_text_color", default: "#3E4E67", null: false
    t.string "header_code"
    t.string "header_text_font", default: "Poppins"
    t.boolean "show_link_course", default: true, null: false
    t.boolean "show_link_pack", default: true, null: false
    t.string "title", default: "", null: false
    t.string "url", default: "", null: false
    t.integer "state", default: 0
    t.bigint "user_id"
    t.index ["url", "tenant_id"], name: "index_websites_on_url_and_tenant_id", unique: true
    t.index ["user_id"], name: "index_websites_on_user_id"
  end

  create_table "websites_body_section_banners", force: :cascade do |t|
    t.bigint "tenant_id"
    t.bigint "website_button_id"
    t.string "media_align"
    t.string "text_align"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "image_data"
    t.jsonb "image_mobile_data"
    t.boolean "active_carousel", default: false
    t.jsonb "items_banner", default: []
    t.index ["tenant_id"], name: "index_websites_body_section_banners_on_tenant_id"
    t.index ["website_button_id"], name: "index_websites_body_section_banners_on_website_button_id"
  end

  create_table "websites_body_section_buttons", force: :cascade do |t|
    t.bigint "tenant_id"
    t.string "button_type"
    t.string "text"
    t.string "style"
    t.string "border"
    t.integer "radius"
    t.string "color_text", default: "#FFF"
    t.string "color_button"
    t.string "url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_websites_body_section_buttons_on_tenant_id"
  end

  create_table "websites_body_section_cards", force: :cascade do |t|
    t.bigint "tenant_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_websites_body_section_cards_on_tenant_id"
  end

  create_table "websites_body_section_cards_columns", force: :cascade do |t|
    t.string "title"
    t.text "description"
    t.jsonb "image_data"
    t.bigint "body_section_card_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["body_section_card_id"], name: "index_wbs_card_c_on_wbs_card_id"
  end

  create_table "websites_body_section_counters", force: :cascade do |t|
    t.bigint "tenant_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "count_backwards", default: false
    t.index ["tenant_id"], name: "index_websites_body_section_counters_on_tenant_id"
  end

  create_table "websites_body_section_counters_rows", force: :cascade do |t|
    t.string "text"
    t.integer "number"
    t.string "units"
    t.boolean "count_backwards", default: false
    t.bigint "body_section_counter_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "start_date"
    t.boolean "start_now", default: false
    t.index ["body_section_counter_id"], name: "index_wbscr_on_wbsc_id"
  end

  create_table "websites_body_section_email_leads_forms", force: :cascade do |t|
    t.bigint "tenant_id"
    t.string "name"
    t.jsonb "custom_fields_config", default: {}, null: false
    t.string "heading"
    t.text "description"
    t.string "checkbox_message"
    t.string "button_color"
    t.string "button_text"
    t.integer "views_count", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "redirect_url"
    t.boolean "checkbox_active", default: true
    t.index ["tenant_id"], name: "index_websites_body_section_email_leads_forms_on_tenant_id"
  end

  create_table "websites_body_section_faqs", force: :cascade do |t|
    t.bigint "tenant_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_websites_body_section_faqs_on_tenant_id"
  end

  create_table "websites_body_section_free_codes", force: :cascade do |t|
    t.text "html_code"
    t.text "js_code"
    t.bigint "tenant_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_websites_body_section_free_codes_on_tenant_id"
  end

  create_table "websites_body_section_frequent_questions", force: :cascade do |t|
    t.string "question"
    t.text "answer"
    t.integer "position", default: 1, null: false
    t.bigint "body_section_faq_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["body_section_faq_id"], name: "index_wfaq_on_wbs_frequent_questions"
  end

  create_table "websites_body_section_media", force: :cascade do |t|
    t.bigint "tenant_id"
    t.jsonb "media_data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "bunny_video_id"
    t.boolean "migrated", default: false
    t.index ["tenant_id"], name: "index_websites_body_section_media_on_tenant_id"
  end

  create_table "websites_body_section_price_card_items", force: :cascade do |t|
    t.bigint "tenant_id"
    t.bigint "body_section_price_card_id"
    t.string "text", null: false
    t.integer "position", default: 1
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["body_section_price_card_id"], name: "index_wbs_price_f_on_wbs_price_c_id"
    t.index ["position"], name: "index_websites_body_section_price_card_items_on_position"
    t.index ["tenant_id"], name: "index_websites_body_section_price_card_items_on_tenant_id"
  end

  create_table "websites_body_section_price_cards", force: :cascade do |t|
    t.bigint "tenant_id"
    t.bigint "body_section_price_id"
    t.jsonb "image_data"
    t.string "background_color", default: "#FFFFFF"
    t.string "text_color", default: "#000000"
    t.string "font", default: "Poppins"
    t.string "title"
    t.string "title_size", default: "14px"
    t.boolean "title_font_bold", default: false
    t.boolean "title_font_cursive", default: false
    t.boolean "title_font_underlined", default: false
    t.boolean "title_font_line_through", default: false
    t.string "title_font_alignment", default: "center"
    t.string "subtitle"
    t.string "subtitle_size", default: "12px"
    t.boolean "subtitle_font_bold", default: false
    t.boolean "subtitle_font_cursive", default: false
    t.boolean "subtitle_font_underlined", default: false
    t.boolean "subtitle_font_line_through", default: false
    t.string "subtitle_font_alignment", default: "center"
    t.string "description"
    t.boolean "description_font_bold", default: false
    t.boolean "description_font_cursive", default: false
    t.boolean "description_font_underlined", default: false
    t.boolean "description_font_line_through", default: false
    t.string "description_size", default: "10px"
    t.string "description_font_alignment", default: "center"
    t.string "icon_color", default: "#2d3954"
    t.boolean "visibility", default: true
    t.string "button_type"
    t.string "button_text", default: "Get Started"
    t.string "button_url"
    t.boolean "button_visibility", default: true
    t.integer "position", default: 1
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["body_section_price_id"], name: "index_wbs_price_c_on_wbs_price_id"
    t.index ["position"], name: "index_websites_body_section_price_cards_on_position"
    t.index ["tenant_id"], name: "index_websites_body_section_price_cards_on_tenant_id"
  end

  create_table "websites_body_section_prices", force: :cascade do |t|
    t.bigint "tenant_id"
    t.integer "cards_count", default: 3
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_websites_body_section_prices_on_tenant_id"
  end

  create_table "websites_body_section_product_lists", force: :cascade do |t|
    t.bigint "tenant_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "type_section_product"
    t.index ["tenant_id"], name: "index_websites_body_section_product_lists_on_tenant_id"
  end

  create_table "websites_body_section_product_lists_courses", force: :cascade do |t|
    t.bigint "course_id", null: false
    t.bigint "body_section_product_list_id", null: false
    t.bigint "tenant_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["body_section_product_list_id", "course_id"], name: "index_product_list_courses_on_product_list_id_and_course_id", unique: true
    t.index ["body_section_product_list_id"], name: "index_product_lists_courses_on_product_list_id"
    t.index ["course_id"], name: "index_websites_body_section_product_lists_courses_on_course_id"
    t.index ["tenant_id"], name: "index_websites_body_section_product_lists_courses_on_tenant_id"
  end

  create_table "websites_body_section_testimonials", force: :cascade do |t|
    t.bigint "tenant_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_websites_body_section_testimonials_on_tenant_id"
  end

  create_table "websites_body_section_text_media", force: :cascade do |t|
    t.bigint "tenant_id"
    t.text "description"
    t.bigint "website_button_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "media_data"
    t.string "media_align"
    t.string "subheader_font"
    t.boolean "description_active", default: true, null: false
    t.string "text_align", default: "left", null: false
    t.jsonb "general_text_style"
    t.string "content_font_color", default: "#2d3954", null: false
    t.string "content_font_size", default: "15px", null: false
    t.string "content_font", default: "Poppins", null: false
    t.boolean "content_font_bold", default: false, null: false
    t.boolean "content_font_cursive", default: false, null: false
    t.boolean "content_font_underlined", default: false, null: false
    t.string "bunny_video_id"
    t.boolean "migrated", default: false
    t.index ["tenant_id"], name: "index_websites_body_section_text_media_on_tenant_id"
    t.index ["website_button_id"], name: "index_websites_body_section_text_media_on_website_button_id"
  end

  create_table "websites_body_section_texts", force: :cascade do |t|
    t.bigint "tenant_id"
    t.string "content"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_websites_body_section_texts_on_tenant_id"
  end

  create_table "websites_body_sections", force: :cascade do |t|
    t.bigint "tenant_id"
    t.string "content_type", null: false
    t.bigint "content_id", null: false
    t.string "header"
    t.string "subheader"
    t.integer "position", default: 1, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "header_color", default: "#2D3954"
    t.string "subheader_color", default: "#2D3954"
    t.string "header_font"
    t.string "subheader_font"
    t.bigint "website_id", null: false
    t.string "header_size"
    t.string "subheader_size"
    t.boolean "header_active", default: true, null: false
    t.boolean "subheader_active", default: true, null: false
    t.boolean "header_font_bold", default: true, null: false
    t.boolean "header_font_cursive", default: false, null: false
    t.boolean "header_font_underlined", default: false, null: false
    t.string "header_font_alignment", default: "center"
    t.boolean "subheader_font_line_through", default: false, null: false
    t.string "subheader_font_line_height", default: "1.7", null: false
    t.boolean "subheader_font_bold", default: false, null: false
    t.boolean "subheader_font_cursive", default: false, null: false
    t.boolean "subheader_font_underlined", default: false, null: false
    t.string "subheader_font_alignment", default: "center"
    t.boolean "header_font_line_through", default: false, null: false
    t.string "header_font_line_height", default: "1.2", null: false
    t.boolean "visibility", default: true, null: false
    t.string "background_color", default: "#FFF", null: false
    t.boolean "background_visibility", default: false, null: false
    t.boolean "image_visibility", default: true, null: false
    t.jsonb "image_data"
    t.jsonb "image_mobile_data"
    t.boolean "dark_elements", default: false, null: false
    t.integer "parent_banner_id"
    t.integer "existing_email_form_id"
    t.boolean "existing_email_form_enabled", default: false
    t.index ["content_type", "content_id"], name: "index_websites_body_sections_on_content"
    t.index ["tenant_id"], name: "index_websites_body_sections_on_tenant_id"
    t.index ["website_id"], name: "index_websites_body_sections_on_website_id"
  end

  create_table "websites_button_styles", force: :cascade do |t|
    t.bigint "tenant_id"
    t.bigint "button_id", null: false
    t.bigint "style_id", null: false
    t.boolean "active", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["button_id"], name: "index_websites_button_styles_on_button_id"
    t.index ["style_id"], name: "index_websites_button_styles_on_style_id"
    t.index ["tenant_id", "button_id", "style_id"], name: "index_by_tenant_button_style", unique: true
    t.index ["tenant_id"], name: "index_websites_button_styles_on_tenant_id"
  end

  create_table "websites_buttons", force: :cascade do |t|
    t.bigint "tenant_id"
    t.string "title", default: "VER CURSOS", null: false
    t.string "url", default: "/products"
    t.string "target"
    t.string "color", default: "#A270FE", null: false
    t.string "color_hover", default: "#AAAAAA", null: false
    t.string "style"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "align"
    t.boolean "active", default: true, null: false
    t.bigint "user_id"
    t.index ["tenant_id"], name: "index_websites_buttons_on_tenant_id"
    t.index ["user_id"], name: "index_websites_buttons_on_user_id"
  end

  create_table "websites_leads_form_contacts", force: :cascade do |t|
    t.bigint "tenant_id"
    t.bigint "lead_forms_section_id"
    t.jsonb "custom_fields", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "mailchimp_id"
    t.index ["lead_forms_section_id"], name: "index_websites_leads_form_contacts_on_lead_forms_section_id"
    t.index ["tenant_id"], name: "index_websites_leads_form_contacts_on_tenant_id"
  end

  create_table "websites_margins", force: :cascade do |t|
    t.string "margin_type", null: false
    t.integer "top"
    t.integer "bottom"
    t.integer "left"
    t.integer "right"
    t.bigint "body_section_id"
    t.bigint "tenant_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["body_section_id"], name: "index_websites_margins_on_body_section_id"
    t.index ["tenant_id"], name: "index_websites_margins_on_tenant_id"
  end

  create_table "websites_personal_testimonials", force: :cascade do |t|
    t.bigint "tenant_id"
    t.bigint "section_testimonial_id"
    t.string "name", null: false
    t.string "occupation"
    t.string "comment"
    t.integer "position", default: 1, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "picture_data"
    t.index ["section_testimonial_id"], name: "index_websites_personal_testimonials_on_section_testimonial_id"
    t.index ["tenant_id"], name: "index_websites_personal_testimonials_on_tenant_id"
  end

  create_table "websites_styles", force: :cascade do |t|
    t.bigint "tenant_id"
    t.string "style", null: false
    t.string "border", default: "rounded", null: false
    t.string "align", default: "center", null: false
    t.string "color", default: "#C03BFF", null: false
    t.string "color_hover"
    t.string "color_text", default: "#FFF", null: false
    t.string "font", default: "Poppins", null: false
    t.string "font_size", default: "14px", null: false
    t.boolean "font_bold", default: false, null: false
    t.boolean "font_cursive", default: false, null: false
    t.boolean "font_underlined", default: false, null: false
    t.boolean "font_line_through", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.index ["tenant_id"], name: "index_websites_styles_on_tenant_id"
    t.index ["user_id"], name: "index_websites_styles_on_user_id"
  end

  add_foreign_key "admin_roles", "tenants"
  add_foreign_key "affiliate_informations", "users"
  add_foreign_key "affiliate_invitations", "tenants"
  add_foreign_key "affiliate_invitations", "users", column: "inviter_id"
  add_foreign_key "affiliates_courses", "courses_courses", column: "course_id"
  add_foreign_key "affiliates_courses", "users", column: "affiliate_id"
  add_foreign_key "affiliates_tenants", "tenants"
  add_foreign_key "affiliates_tenants", "users", column: "affiliate_id"
  add_foreign_key "audit_versions", "tenants"
  add_foreign_key "bunny_videos", "tenants"
  add_foreign_key "communities", "tenants"
  add_foreign_key "community_post_attachments", "community_posts"
  add_foreign_key "community_post_attachments", "tenants"
  add_foreign_key "community_post_survey_responses", "community_posts"
  add_foreign_key "community_post_survey_responses", "tenants"
  add_foreign_key "community_post_survey_responses", "users"
  add_foreign_key "community_posts", "communities"
  add_foreign_key "community_posts", "tenants"
  add_foreign_key "community_posts", "users"
  add_foreign_key "community_teachers", "communities"
  add_foreign_key "community_teachers", "tenants"
  add_foreign_key "community_teachers", "users"
  add_foreign_key "courses_affiliate_settings", "tenants"
  add_foreign_key "courses_categories", "tenants"
  add_foreign_key "courses_course_teachers", "courses_courses", column: "course_id"
  add_foreign_key "courses_course_teachers", "tenants"
  add_foreign_key "courses_course_teachers", "users"
  add_foreign_key "courses_courses", "courses_categories", column: "category_id"
  add_foreign_key "courses_courses", "tenants"
  add_foreign_key "courses_courses", "users", column: "created_by_id"
  add_foreign_key "courses_courses_packs", "courses_courses", column: "course_id"
  add_foreign_key "courses_courses_packs", "courses_packs", column: "pack_id"
  add_foreign_key "courses_courses_packs", "tenants"
  add_foreign_key "courses_exam_question_statistics", "courses_exam_questions", column: "question_id"
  add_foreign_key "courses_exam_question_statistics", "tenants", on_delete: :cascade
  add_foreign_key "courses_exam_questions", "courses_lesson_exams", column: "exam_id", on_update: :cascade
  add_foreign_key "courses_exam_questions", "courses_lessons", column: "related_lesson_id"
  add_foreign_key "courses_exam_questions", "tenants"
  add_foreign_key "courses_lesson_audios", "courses_lessons", column: "id", on_update: :cascade, on_delete: :cascade
  add_foreign_key "courses_lesson_audios", "media_lesson_contents", column: "content_id"
  add_foreign_key "courses_lesson_audios", "tenants"
  add_foreign_key "courses_lesson_downloadables", "courses_lessons", column: "id", on_update: :cascade, on_delete: :cascade
  add_foreign_key "courses_lesson_downloadables", "media_lesson_contents", column: "content_id"
  add_foreign_key "courses_lesson_downloadables", "tenants"
  add_foreign_key "courses_lesson_exams", "courses_lessons", column: "id", on_update: :cascade, on_delete: :cascade
  add_foreign_key "courses_lesson_exams", "tenants"
  add_foreign_key "courses_lesson_htmls", "tenants"
  add_foreign_key "courses_lesson_legacy_htmls", "courses_lessons", column: "id", on_update: :cascade, on_delete: :cascade
  add_foreign_key "courses_lesson_legacy_htmls", "tenants"
  add_foreign_key "courses_lesson_pdfs", "courses_lessons", column: "id", on_update: :cascade, on_delete: :cascade
  add_foreign_key "courses_lesson_pdfs", "media_lesson_contents", column: "content_id"
  add_foreign_key "courses_lesson_pdfs", "tenants"
  add_foreign_key "courses_lesson_presentations", "courses_lessons", column: "id", on_update: :cascade, on_delete: :cascade
  add_foreign_key "courses_lesson_presentations", "media_lesson_contents", column: "content_id"
  add_foreign_key "courses_lesson_presentations", "tenants"
  add_foreign_key "courses_lesson_scorms", "courses_lessons", column: "id", on_update: :cascade, on_delete: :cascade
  add_foreign_key "courses_lesson_scorms", "media_lesson_contents", column: "content_id"
  add_foreign_key "courses_lesson_scorms", "tenants"
  add_foreign_key "courses_lesson_surveys", "courses_lessons", column: "id", on_update: :cascade, on_delete: :cascade
  add_foreign_key "courses_lesson_surveys", "tenants"
  add_foreign_key "courses_lesson_videocalls", "courses_lessons", column: "id", on_update: :cascade, on_delete: :cascade
  add_foreign_key "courses_lesson_videocalls", "tenants"
  add_foreign_key "courses_lesson_videos", "courses_lessons", column: "id", on_update: :cascade, on_delete: :cascade
  add_foreign_key "courses_lesson_videos", "media_lesson_contents", column: "content_id"
  add_foreign_key "courses_lesson_videos", "tenants"
  add_foreign_key "courses_lesson_webinars", "courses_lessons", column: "id", on_update: :cascade, on_delete: :cascade
  add_foreign_key "courses_lesson_webinars", "tenants"
  add_foreign_key "courses_lessons", "courses_my_modules", column: "my_module_id"
  add_foreign_key "courses_lessons", "tenants"
  add_foreign_key "courses_my_modules", "courses_courses", column: "course_id"
  add_foreign_key "courses_my_modules", "tenants"
  add_foreign_key "courses_packs", "tenants"
  add_foreign_key "courses_packs", "users", column: "created_by_id"
  add_foreign_key "courses_survey_questions", "courses_lesson_surveys", column: "survey_id"
  add_foreign_key "courses_survey_questions", "courses_lessons", column: "related_lesson_id"
  add_foreign_key "courses_survey_questions", "tenants"
  add_foreign_key "custom_domains", "tenants"
  add_foreign_key "email_activities", "tenants", on_delete: :cascade
  add_foreign_key "email_activities", "users", on_delete: :nullify
  add_foreign_key "emails_activities", "emails_broadcasts", column: "broadcast_id", on_delete: :cascade
  add_foreign_key "emails_activities", "tenants", on_delete: :cascade
  add_foreign_key "emails_activities", "users", on_delete: :cascade
  add_foreign_key "emails_automations", "tenants", on_delete: :cascade
  add_foreign_key "emails_broadcasts", "emails_signatures", column: "signature_id"
  add_foreign_key "emails_broadcasts", "emails_templates", column: "template_id"
  add_foreign_key "emails_broadcasts", "tenants", on_delete: :cascade
  add_foreign_key "emails_broadcasts", "user_filters", column: "filter_id"
  add_foreign_key "emails_emails", "tenants", on_delete: :cascade
  add_foreign_key "emails_signatures", "tenants", on_delete: :cascade
  add_foreign_key "emails_templates", "tenants", on_delete: :cascade
  add_foreign_key "filters", "tenants", on_delete: :cascade
  add_foreign_key "gamification_badge_settings", "gamification_settings"
  add_foreign_key "gamification_badge_settings", "tenants"
  add_foreign_key "gamification_level_settings", "gamification_settings"
  add_foreign_key "gamification_level_settings", "tenants"
  add_foreign_key "gamification_point_settings", "gamification_settings"
  add_foreign_key "gamification_point_settings", "tenants"
  add_foreign_key "gamification_reward_settings", "gamification_settings"
  add_foreign_key "gamification_reward_settings", "tenants"
  add_foreign_key "gamification_settings", "tenants"
  add_foreign_key "gamification_user_badges", "gamification_badge_settings", column: "badge_setting_id"
  add_foreign_key "gamification_user_badges", "tenants"
  add_foreign_key "gamification_user_badges", "users"
  add_foreign_key "gamification_user_points_activities", "tenants"
  add_foreign_key "gamification_user_points_activities", "users"
  add_foreign_key "gamification_user_profiles", "gamification_level_settings", column: "level_setting_id"
  add_foreign_key "gamification_user_profiles", "tenants"
  add_foreign_key "gamification_user_profiles", "users"
  add_foreign_key "gamification_user_rewards", "gamification_reward_settings", column: "reward_setting_id"
  add_foreign_key "gamification_user_rewards", "tenants"
  add_foreign_key "gamification_user_rewards", "users"
  add_foreign_key "groups_group_courses", "courses_courses", column: "course_id"
  add_foreign_key "groups_group_courses", "groups_groups", column: "group_id"
  add_foreign_key "groups_group_courses", "tenants"
  add_foreign_key "groups_group_instructors", "groups_groups", column: "group_id"
  add_foreign_key "groups_group_instructors", "tenants"
  add_foreign_key "groups_group_instructors", "users", column: "instructor_id"
  add_foreign_key "groups_group_packs", "courses_packs", column: "pack_id"
  add_foreign_key "groups_group_packs", "groups_groups", column: "group_id"
  add_foreign_key "groups_group_packs", "tenants"
  add_foreign_key "groups_group_students", "groups_groups", column: "group_id"
  add_foreign_key "groups_group_students", "tenants"
  add_foreign_key "groups_group_students", "users", column: "student_id"
  add_foreign_key "groups_groups", "tenants"
  add_foreign_key "invoices_invoices", "tenants"
  add_foreign_key "marketplaces_courses_categories", "courses_courses", column: "course_id"
  add_foreign_key "marketplaces_courses_categories", "marketplaces_categories", column: "category_id"
  add_foreign_key "marketplaces_licenses", "courses_courses", column: "course_id"
  add_foreign_key "marketplaces_licenses", "tenants", on_delete: :cascade
  add_foreign_key "media_lesson_contents", "tenants"
  add_foreign_key "network_rest_webhooks", "tenants"
  add_foreign_key "notices", "tenants"
  add_foreign_key "purchases_coupon_products", "purchases_coupons", column: "coupon_id"
  add_foreign_key "purchases_coupon_products", "tenants"
  add_foreign_key "purchases_coupons", "tenants"
  add_foreign_key "purchases_payment_methods", "tenants", on_delete: :cascade
  add_foreign_key "purchases_prices", "tenants", on_delete: :cascade
  add_foreign_key "purchases_prices_payment_methods", "purchases_payment_methods", column: "payment_method_id", on_delete: :cascade
  add_foreign_key "purchases_prices_payment_methods", "purchases_prices", column: "price_id", on_delete: :cascade
  add_foreign_key "purchases_prices_payment_methods", "tenants", on_delete: :cascade
  add_foreign_key "purchases_product_purchases", "purchases_coupons", column: "coupon_id"
  add_foreign_key "purchases_product_purchases", "purchases_payment_methods", column: "payment_method_id"
  add_foreign_key "purchases_product_purchases", "purchases_prices", column: "price_id", on_delete: :nullify
  add_foreign_key "purchases_product_purchases", "tenants"
  add_foreign_key "purchases_product_purchases", "users"
  add_foreign_key "purchases_refund_requests", "purchases_product_purchases", column: "product_purchase_id", on_delete: :cascade
  add_foreign_key "purchases_refund_requests", "tenants", on_delete: :cascade
  add_foreign_key "report_tabs", "tenants"
  add_foreign_key "reports", "report_tabs"
  add_foreign_key "reports", "tenants"
  add_foreign_key "social_comments", "tenants"
  add_foreign_key "social_comments", "users", column: "author_id"
  add_foreign_key "social_reactions", "tenants"
  add_foreign_key "social_reactions", "users", column: "author_id"
  add_foreign_key "student_certificates", "subscriptions_subscriptions", column: "subscription_id", on_delete: :nullify
  add_foreign_key "subscriptions_exam_answers", "courses_exam_questions", column: "question_id"
  add_foreign_key "subscriptions_exam_answers", "subscriptions_exam_attempts", column: "exam_attempt_id"
  add_foreign_key "subscriptions_exam_answers", "tenants"
  add_foreign_key "subscriptions_exam_answers", "users", column: "scored_by_id"
  add_foreign_key "subscriptions_exam_attempts", "courses_lesson_exams", column: "exam_id"
  add_foreign_key "subscriptions_exam_attempts", "subscriptions_subscriptions", column: "subscription_id"
  add_foreign_key "subscriptions_exam_attempts", "tenants"
  add_foreign_key "subscriptions_exam_questions", "courses_exam_questions", column: "question_id"
  add_foreign_key "subscriptions_exam_questions", "subscriptions_exam_attempts", column: "exam_attempt_id"
  add_foreign_key "subscriptions_exam_questions", "tenants", on_delete: :cascade
  add_foreign_key "subscriptions_extra_exam_attempts", "courses_lesson_exams", column: "exam_id"
  add_foreign_key "subscriptions_extra_exam_attempts", "subscriptions_subscriptions", column: "subscription_id"
  add_foreign_key "subscriptions_extra_exam_attempts", "tenants"
  add_foreign_key "subscriptions_progresses", "courses_lessons", column: "lesson_id"
  add_foreign_key "subscriptions_progresses", "subscriptions_subscriptions", column: "subscription_id"
  add_foreign_key "subscriptions_progresses", "tenants"
  add_foreign_key "subscriptions_student_community_accesses", "communities"
  add_foreign_key "subscriptions_student_community_accesses", "purchases_product_purchases", column: "product_purchase_id"
  add_foreign_key "subscriptions_student_community_accesses", "subscriptions_subscriptions", column: "subscription_id"
  add_foreign_key "subscriptions_student_community_accesses", "tenants"
  add_foreign_key "subscriptions_student_community_accesses", "users", column: "creator_id"
  add_foreign_key "subscriptions_student_course_accesses", "courses_packs", column: "pack_id"
  add_foreign_key "subscriptions_student_course_accesses", "groups_groups", column: "group_id"
  add_foreign_key "subscriptions_student_course_accesses", "marketplaces_licenses", column: "license_id"
  add_foreign_key "subscriptions_student_course_accesses", "purchases_product_purchases", column: "product_purchase_id"
  add_foreign_key "subscriptions_student_course_accesses", "subscriptions_subscriptions", column: "subscription_id"
  add_foreign_key "subscriptions_student_course_accesses", "tenants"
  add_foreign_key "subscriptions_student_course_accesses", "users", column: "creator_id"
  add_foreign_key "subscriptions_student_reviews", "subscriptions_subscriptions", column: "subscription_id"
  add_foreign_key "subscriptions_student_reviews", "tenants"
  add_foreign_key "subscriptions_subscriptions", "courses_courses", column: "course_id"
  add_foreign_key "subscriptions_subscriptions", "courses_packs", column: "pack_id"
  add_foreign_key "subscriptions_subscriptions", "tenants"
  add_foreign_key "subscriptions_subscriptions", "users", column: "student_id"
  add_foreign_key "subscriptions_survey_answers", "courses_survey_questions", column: "question_id"
  add_foreign_key "subscriptions_survey_answers", "subscriptions_survey_responses", column: "survey_response_id"
  add_foreign_key "subscriptions_survey_answers", "tenants"
  add_foreign_key "subscriptions_survey_answers", "users"
  add_foreign_key "subscriptions_survey_questions", "courses_survey_questions", column: "question_id"
  add_foreign_key "subscriptions_survey_questions", "subscriptions_survey_responses", column: "survey_response_id"
  add_foreign_key "subscriptions_survey_questions", "tenants"
  add_foreign_key "subscriptions_survey_responses", "courses_lesson_surveys", column: "survey_id"
  add_foreign_key "subscriptions_survey_responses", "subscriptions_subscriptions", column: "subscription_id"
  add_foreign_key "subscriptions_survey_responses", "tenants"
  add_foreign_key "tags", "tenants"
  add_foreign_key "tags_users", "tags"
  add_foreign_key "tags_users", "users"
  add_foreign_key "templates", "websites"
  add_foreign_key "tenant_metrics", "tenants"
  add_foreign_key "tenants", "feature_plans", primary_key: "feature_plan"
  add_foreign_key "triggers", "courses_courses", column: "course_id"
  add_foreign_key "triggers", "tenants"
  add_foreign_key "user_activities", "tenants"
  add_foreign_key "user_activities", "users"
  add_foreign_key "user_filters", "tenants", on_delete: :cascade
  add_foreign_key "user_sessions", "users"
  add_foreign_key "user_social_networks", "tenants"
  add_foreign_key "user_social_networks", "users"
  add_foreign_key "users", "admin_roles"
  add_foreign_key "users", "tenants"
  add_foreign_key "users_tenants", "tenants"
  add_foreign_key "users_tenants", "users"
  add_foreign_key "website_footer_columns", "tenants"
  add_foreign_key "website_footer_columns", "website_footers"
  add_foreign_key "website_footers", "tenants"
  add_foreign_key "website_footers", "websites"
  add_foreign_key "websites", "tenants"
  add_foreign_key "websites_body_section_banners", "tenants"
  add_foreign_key "websites_body_section_banners", "websites_buttons", column: "website_button_id"
  add_foreign_key "websites_body_section_buttons", "tenants"
  add_foreign_key "websites_body_section_cards", "tenants"
  add_foreign_key "websites_body_section_cards_columns", "websites_body_section_cards", column: "body_section_card_id"
  add_foreign_key "websites_body_section_counters", "tenants"
  add_foreign_key "websites_body_section_counters_rows", "websites_body_section_counters", column: "body_section_counter_id"
  add_foreign_key "websites_body_section_email_leads_forms", "tenants"
  add_foreign_key "websites_body_section_faqs", "tenants"
  add_foreign_key "websites_body_section_free_codes", "tenants"
  add_foreign_key "websites_body_section_frequent_questions", "websites_body_section_faqs", column: "body_section_faq_id"
  add_foreign_key "websites_body_section_media", "tenants"
  add_foreign_key "websites_body_section_price_card_items", "tenants"
  add_foreign_key "websites_body_section_price_card_items", "websites_body_section_price_cards", column: "body_section_price_card_id"
  add_foreign_key "websites_body_section_price_cards", "tenants"
  add_foreign_key "websites_body_section_price_cards", "websites_body_section_prices", column: "body_section_price_id"
  add_foreign_key "websites_body_section_prices", "tenants"
  add_foreign_key "websites_body_section_product_lists", "tenants"
  add_foreign_key "websites_body_section_product_lists_courses", "courses_courses", column: "course_id"
  add_foreign_key "websites_body_section_product_lists_courses", "tenants"
  add_foreign_key "websites_body_section_product_lists_courses", "websites_body_section_product_lists", column: "body_section_product_list_id"
  add_foreign_key "websites_body_section_testimonials", "tenants"
  add_foreign_key "websites_body_section_text_media", "tenants"
  add_foreign_key "websites_body_section_text_media", "websites_buttons", column: "website_button_id"
  add_foreign_key "websites_body_section_texts", "tenants"
  add_foreign_key "websites_body_sections", "tenants"
  add_foreign_key "websites_body_sections", "websites"
  add_foreign_key "websites_button_styles", "tenants"
  add_foreign_key "websites_button_styles", "websites_buttons", column: "button_id"
  add_foreign_key "websites_button_styles", "websites_styles", column: "style_id"
  add_foreign_key "websites_buttons", "tenants"
  add_foreign_key "websites_leads_form_contacts", "tenants"
  add_foreign_key "websites_leads_form_contacts", "websites_body_section_email_leads_forms", column: "lead_forms_section_id"
  add_foreign_key "websites_margins", "tenants"
  add_foreign_key "websites_margins", "websites_body_sections", column: "body_section_id"
  add_foreign_key "websites_personal_testimonials", "tenants"
  add_foreign_key "websites_personal_testimonials", "websites_body_section_testimonials", column: "section_testimonial_id"
  add_foreign_key "websites_styles", "tenants", on_delete: :cascade

  create_view "groups_group_subscriptions", sql_definition: <<-SQL
      SELECT s.id AS subscription_id,
      s.tenant_id,
      gc.group_id,
      gc.course_id,
      gs.student_id
     FROM subscriptions_subscriptions s,
      groups_group_courses gc,
      groups_group_students gs
    WHERE ((s.course_id = gc.course_id) AND (s.student_id = gs.student_id) AND (gc.group_id = gs.group_id));
  SQL
end
