GIT
  remote: https://github.com//camiloforero/shrine-google_cloud_storage.git
  revision: b9649d8c838a946683b607a83d7d3bc7f7c13de6
  ref: b9649d8
  specs:
    shrine-google_cloud_storage (3.2.0)
      google-cloud-storage (~> 1.6)
      shrine (~> 3.0)

GIT
  remote: https://github.com/camiloforero/paypalhttp_ruby.git
  revision: b926776e96071046933b2bb2f2edf3f61db7b57e
  ref: b926776
  specs:
    paypalhttp (1.0.1)

GIT
  remote: https://github.com/tilo/smarter_csv.git
  revision: 14b7b06b166de66a79cfc180a8ad6d1722087690
  ref: 14b7b06
  specs:
    smarter_csv (2.0.0.pre2)

GEM
  remote: https://rubygems.org/
  specs:
    IPinfo (1.0.1)
      faraday (~> 1.0)
      json (~> 2.1)
      lru_redux (~> 1.1)
    MailchimpMarketing (3.0.80)
      excon (>= 0.76.0, < 1)
      json (~> 2.1, >= 2.1.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.0)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    acts_as_list (1.1.0)
      activerecord (>= 4.2)
    addressable (2.8.1)
      public_suffix (>= 2.0.2, < 6.0)
    ast (2.4.2)
    aws-eventstream (1.3.2)
    aws-partitions (1.1076.0)
    aws-sdk-core (3.222.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.99.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.182.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.11.0)
      aws-eventstream (~> 1, >= 1.0.2)
    backport (1.2.0)
    base64 (0.2.0)
    bcrypt (3.1.18)
    benchmark (0.2.1)
    bootsnap (1.16.0)
      msgpack (~> 1.2)
    builder (3.2.4)
    chunky_png (1.4.0)
    coderay (1.1.3)
    concurrent-ruby (1.2.2)
    connection_pool (2.4.0)
    content_disposition (1.0.0)
    counter_culture (3.3.0)
      activerecord (>= 4.2)
      activesupport (>= 4.2)
    countries (4.2.3)
      i18n_data (~> 0.16.0)
      sixarm_ruby_unaccent (~> 1.1)
    crass (1.0.6)
    database_cleaner-active_record (2.1.0)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.3.3)
    debug (1.7.2)
      irb (>= 1.5.0)
      reline (>= 0.3.1)
    declarative (0.0.20)
    devise (4.9.0)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise_token_auth (1.2.1)
      bcrypt (~> 3.0)
      devise (> 3.5.2, < 5)
      rails (>= 4.2.0, < 7.1)
    diff-lcs (1.5.0)
    digest-crc (0.6.4)
      rake (>= 12.0.0, < 14.0.0)
    dnsruby (1.72.2)
      simpleidn (~> 0.2.1)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    dotenv (2.8.1)
    dotenv-rails (2.8.1)
      dotenv (= 2.8.1)
      railties (>= 3.2)
    down (5.4.0)
      addressable (~> 2.8)
    e2mmap (0.1.0)
    erubi (1.12.0)
    ethon (0.16.0)
      ffi (>= 1.15.0)
    excon (0.112.0)
    factory_bot (6.2.1)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.2.0)
      factory_bot (~> 6.2.0)
      railties (>= 5.0.0)
    faker (3.1.1)
      i18n (>= 1.8.11, < 2)
    faraday (1.10.3)
      faraday-em_http (~> 1.0)
      faraday-em_synchrony (~> 1.0)
      faraday-excon (~> 1.1)
      faraday-httpclient (~> 1.0)
      faraday-multipart (~> 1.0)
      faraday-net_http (~> 1.0)
      faraday-net_http_persistent (~> 1.0)
      faraday-patron (~> 1.0)
      faraday-rack (~> 1.0)
      faraday-retry (~> 1.0)
      ruby2_keywords (>= 0.0.4)
    faraday-em_http (1.0.0)
    faraday-em_synchrony (1.0.0)
    faraday-excon (1.1.0)
    faraday-httpclient (1.0.1)
    faraday-multipart (1.0.4)
      multipart-post (~> 2)
    faraday-net_http (1.0.1)
    faraday-net_http_persistent (1.2.0)
    faraday-patron (1.0.0)
    faraday-rack (1.0.0)
    faraday-retry (1.0.3)
    faraday_middleware (1.2.0)
      faraday (~> 1.0)
    ffi (1.15.5)
    gapic-common (0.18.0)
      faraday (>= 1.9, < 3.a)
      faraday-retry (>= 1.0, < 3.a)
      google-protobuf (~> 3.14)
      googleapis-common-protos (>= 1.3.12, < 2.a)
      googleapis-common-protos-types (>= 1.3.1, < 2.a)
      googleauth (~> 1.0)
      grpc (~> 1.36)
    gibbon (3.5.0)
      faraday (>= 1.0)
      multi_json (>= 1.11.0)
    globalid (1.1.0)
      activesupport (>= 5.0)
    google-apis-core (0.11.0)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (>= 0.16.2, < 2.a)
      httpclient (>= 2.8.1, < 3.a)
      mini_mime (~> 1.0)
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
      rexml
      webrick
    google-apis-iamcredentials_v1 (0.17.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-apis-storage_v1 (0.19.0)
      google-apis-core (>= 0.9.0, < 2.a)
    google-cloud-core (1.6.0)
      google-cloud-env (~> 1.0)
      google-cloud-errors (~> 1.0)
    google-cloud-env (1.6.0)
      faraday (>= 0.17.3, < 3.0)
    google-cloud-errors (1.3.1)
    google-cloud-storage (1.44.0)
      addressable (~> 2.8)
      digest-crc (~> 0.4)
      google-apis-iamcredentials_v1 (~> 0.1)
      google-apis-storage_v1 (~> 0.19.0)
      google-cloud-core (~> 1.6)
      googleauth (>= 0.16.2, < 2.a)
      mini_mime (~> 1.0)
    google-cloud-video-transcoder (1.3.0)
      google-cloud-core (~> 1.6)
      google-cloud-video-transcoder-v1 (>= 0.7, < 2.a)
    google-cloud-video-transcoder-v1 (0.7.0)
      gapic-common (>= 0.18.0, < 2.a)
      google-cloud-errors (~> 1.0)
    google-protobuf (3.22.2-arm64-darwin)
    google-protobuf (3.22.2-x86_64-linux)
    googleapis-common-protos (1.4.0)
      google-protobuf (~> 3.14)
      googleapis-common-protos-types (~> 1.2)
      grpc (~> 1.27)
    googleapis-common-protos-types (1.5.0)
      google-protobuf (~> 3.14)
    googleauth (1.5.0)
      faraday (>= 0.17.3, < 3.a)
      jwt (>= 1.4, < 3.0)
      memoist (~> 0.16)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    graphlient (0.6.0)
      faraday (>= 1.0)
      faraday_middleware
      graphql-client
    graphql (2.0.19)
    graphql-batch (0.5.2)
      graphql (>= 1.10, < 3)
      promise.rb (~> 0.7.2)
    graphql-client (0.18.0)
      activesupport (>= 3.0)
      graphql
    grpc (1.53.0)
      google-protobuf (~> 3.21)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.53.0-x86_64-linux)
      google-protobuf (~> 3.21)
      googleapis-common-protos-types (~> 1.0)
    hashie (5.0.0)
    hoe (4.2.2)
      rake (>= 0.8, < 15.0)
    http-accept (1.7.0)
    http-cookie (1.0.5)
      domain_name (~> 0.5)
    httparty (0.21.0)
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    httpclient (2.8.3)
    hubspot-api-client (14.5.2)
      json (~> 2.1, >= 2.1.0)
      require_all (~> 3.0.0)
      typhoeus (~> 1.4.0)
    i18n (1.12.0)
      concurrent-ruby (~> 1.0)
    i18n_data (0.16.0)
      simple_po_parser (~> 1.1)
    image_processing (1.12.2)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    io-console (0.6.0)
    irb (1.6.3)
      reline (>= 0.3.0)
    jaro_winkler (1.5.4)
    jmespath (1.6.2)
    json (2.6.3)
    jwt (2.7.0)
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    kramdown (2.4.0)
      rexml
    kramdown-parser-gfm (1.1.0)
      kramdown (~> 2.0)
    listen (3.8.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.7.0)
    loofah (2.19.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    lru_redux (1.1.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.2)
    matrix (0.4.2)
    memoist (0.16.2)
    mercadopago-sdk (2.1.0)
      json (~> 2.5)
      rest-client (~> 2.1)
    method_source (1.0.0)
    mime-types (3.4.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2023.0218.1)
    mini_magick (4.12.0)
    mini_mime (1.1.2)
    minitest (5.18.0)
    monetize (1.12.0)
      money (~> 6.12)
    money (6.16.0)
      i18n (>= 0.6.4, <= 2)
    money-rails (1.15.0)
      activesupport (>= 3.0)
      monetize (~> 1.9)
      money (~> 6.13)
      railties (>= 3.0)
    msgpack (1.6.1)
    multi-tenant-support (1.5.0)
      rails (>= 6.1)
    multi_json (1.15.0)
    multi_xml (0.6.0)
    multipart-post (2.3.0)
    net-imap (0.3.4)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.1)
      timeout
    net-smtp (0.3.3)
      net-protocol
    netrc (0.11.0)
    newrelic_rpm (9.6.0)
      base64
    nio4r (2.6.1)
    nokogiri (1.14.2-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.14.2-x86_64-linux)
      racc (~> 1.4)
    normalize_attributes (0.4.0)
      activerecord
    oauth (0.6.2)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    oauth2 (2.0.9)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    omniauth (2.1.1)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-facebook (9.0.0)
      omniauth-oauth2 (~> 1.2)
    omniauth-google-oauth2 (1.1.1)
      jwt (>= 2.0)
      oauth2 (~> 2.0.6)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8.0)
    omniauth-oauth2 (1.8.0)
      oauth2 (>= 1.4, < 3)
      omniauth (~> 2.0)
    orm_adapter (0.5.0)
    os (1.1.4)
    ostruct (0.6.2)
    paper_trail (14.0.0)
      activerecord (>= 6.0)
      request_store (~> 1.4)
    parallel (1.22.1)
    parser (3.2.1.1)
      ast (~> 2.4.1)
    paypal-checkout-sdk (1.0.4)
      paypalhttp (~> 1.0.1)
    pdf-core (0.9.0)
    pg (1.4.6)
    postmark (1.23.0)
      json
    postmark-rails (0.22.1)
      actionmailer (>= 3.0.0)
      postmark (>= 1.21.3, < 2.0)
    prawn (2.4.0)
      pdf-core (~> 0.9.0)
      ttfunk (~> 1.7)
    promise.rb (0.7.4)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    public_suffix (5.0.1)
    puma (6.4.0)
      nio4r (~> 2.0)
    pundit (2.3.0)
      activesupport (>= 3.0.0)
    racc (1.6.2)
    rack (2.2.6.4)
    rack-cors (2.0.1)
      rack (>= 2.0.0)
    rack-protection (3.0.6)
      rack
    rack-test (2.1.0)
      rack (>= 1.3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.0.3)
      activesupport (>= 4.2.0)
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.5.0)
      loofah (~> 2.19, >= 2.19.1)
    rails-i18n (7.0.6)
      i18n (>= 0.7, < 2)
      railties (>= 6.0.0, < 8)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rainbow (3.1.1)
    rake (13.0.6)
    rb-fsevent (0.11.2)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    redis (5.0.6)
      redis-client (>= 0.9.0)
    redis-client (0.14.0)
      connection_pool
    regexp_parser (2.7.0)
    reline (0.3.3)
      io-console (~> 0.5)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    request_store (1.5.1)
      rack (>= 1.4)
    require_all (3.0.0)
    responders (3.1.0)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    retriable (3.1.2)
    reverse_markdown (2.1.1)
      nokogiri
    rexml (3.2.5)
    rollbar (3.4.0)
    roo (2.8.3)
      nokogiri (~> 1)
      rubyzip (>= 1.3.0, < 3.0.0)
    rqrcode (3.1.0)
      chunky_png (~> 1.0)
      rqrcode_core (~> 2.0)
    rqrcode_core (2.0.0)
    rspec-core (3.12.1)
      rspec-support (~> 3.12.0)
    rspec-expectations (3.12.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.12.0)
    rspec-mocks (3.12.4)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.12.0)
    rspec-rails (5.1.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      railties (>= 5.2)
      rspec-core (~> 3.10)
      rspec-expectations (~> 3.10)
      rspec-mocks (~> 3.10)
      rspec-support (~> 3.10)
    rspec-support (3.12.0)
    rubocop (1.48.1)
      json (~> 2.3)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.26.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.28.0)
      parser (>= *******)
    rubocop-capybara (2.17.1)
      rubocop (~> 1.41)
    rubocop-performance (1.16.0)
      rubocop (>= 1.7.0, < 2.0)
      rubocop-ast (>= 0.4.0)
    rubocop-rails (2.18.0)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.33.0, < 2.0)
    rubocop-rspec (2.19.0)
      rubocop (~> 1.33)
      rubocop-capybara (~> 2.17)
    ruby-progressbar (1.13.0)
    ruby-vips (2.1.4)
      ffi (~> 1.12)
    ruby2_keywords (0.0.5)
    rubyzip (2.3.2)
    scenic (1.7.0)
      activerecord (>= 4.0.0)
      railties (>= 4.0.0)
    shrine (3.4.0)
      content_disposition (~> 1.0)
      down (~> 5.1)
    sidekiq (6.5.5)
      connection_pool (>= 2.2.2)
      rack (~> 2.0)
      redis (>= 4.5.0)
    signet (0.17.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simple_po_parser (1.1.6)
    simpleidn (0.2.3)
    sixarm_ruby_unaccent (1.2.0)
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    solargraph (0.48.0)
      backport (~> 1.2)
      benchmark
      bundler (>= 1.17.2)
      diff-lcs (~> 1.4)
      e2mmap
      jaro_winkler (~> 1.5)
      kramdown (~> 2.3)
      kramdown-parser-gfm (~> 1.1)
      parser (~> 3.0)
      reverse_markdown (>= 1.0.5, < 3)
      rubocop (>= 0.52)
      thor (~> 1.0)
      tilt (~> 2.0)
      yard (~> 0.9, >= 0.9.24)
    sorcery (0.16.4)
      bcrypt (~> 3.1)
      oauth (~> 0.5, >= 0.5.5)
      oauth2 (~> 2.0)
    spring (4.1.1)
    stripe (6.5.0)
    strscan (3.0.1)
    thor (1.2.1)
    tilt (2.1.0)
    timeout (0.3.2)
    tinyurl (1.0.0)
      hoe (>= 1.3.0)
    trailblazer-option (0.1.2)
    ttfunk (1.7.0)
    typhoeus (1.4.0)
      ethon (>= 0.9.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    unf (0.1.4)
      unf_ext
    unf_ext (*******)
    unicode-display_width (2.4.2)
    version_gem (1.1.2)
    warden (1.2.9)
      rack (>= 2.0.9)
    webrick (1.7.0)
    websocket-driver (0.7.5)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    wicked_pdf (2.8.2)
      activesupport
      ostruct
    wkhtmltopdf-binary (********)
    yard (0.9.28)
      webrick (~> 1.7.0)
    zeitwerk (2.6.7)

PLATFORMS
  arm64-darwin-22
  x86_64-linux

DEPENDENCIES
  IPinfo
  MailchimpMarketing (~> 3.0.80)
  acts_as_list
  aws-sdk-s3
  bootsnap (>= 1.10.1)
  chunky_png
  content_disposition
  counter_culture (~> 3.2)
  countries (~> 4.2)
  database_cleaner-active_record
  debug (>= 1.0.0)
  devise (>= 4.9.0)
  devise_token_auth (~> 1.2)
  dnsruby (~> 1.59, >= 1.59.3)
  dotenv-rails
  factory_bot_rails
  faker
  gibbon
  google-cloud-storage
  google-cloud-video-transcoder
  graphlient
  graphql (~> 2.0)
  graphql-batch
  httparty
  hubspot-api-client (< 15)
  image_processing (~> 1.12)
  jwt
  kaminari
  listen (~> 3.7)
  matrix
  mercadopago-sdk (~> 2.1)
  mini_magick
  money-rails
  multi-tenant-support
  newrelic_rpm
  normalize_attributes
  omniauth
  omniauth-facebook
  omniauth-google-oauth2
  paper_trail
  paypal-checkout-sdk (~> 1.0)
  paypalhttp!
  pg (~> 1.4)
  postmark-rails
  prawn (~> 2.4)
  pry
  puma (< 7)
  pundit (~> 2.2)
  rack-cors
  rails (~> 7.0)
  rails-i18n (~> 7.0.6)
  redis (~> 5.0)
  rollbar
  roo (~> 2.8.0)
  rqrcode
  rspec-rails (~> 5.1)
  rubocop
  rubocop-performance
  rubocop-rails
  rubocop-rspec
  scenic
  shrine (~> 3.4)
  shrine-google_cloud_storage!
  sidekiq (< 8)
  smarter_csv!
  solargraph
  sorcery
  spring
  stripe (~> 6.2)
  strscan (= 3.0.1)
  tinyurl
  tzinfo-data
  wicked_pdf
  wkhtmltopdf-binary

RUBY VERSION
   ruby 3.2.1p31

BUNDLED WITH
   2.4.6
