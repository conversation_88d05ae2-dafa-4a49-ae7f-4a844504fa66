diff --git a/app/graphql/queries/gamification/leaderboard.rb b/app/graphql/queries/gamification/leaderboard.rb
index ef8d57c5..57d1026a 100644
--- a/app/graphql/queries/gamification/leaderboard.rb
+++ b/app/graphql/queries/gamification/leaderboard.rb
@@ -17,7 +17,7 @@ module Queries
         entries = leaderboard_data.map.with_index(1) do |entry, index|
           OpenStruct.new(
             user: entry.user,
-            total_points: entry.user.gamification_user_profile&.total_points || 0,
+            total_points: entry.total_points || 0,
             rank: index
           )
         end
@@ -26,11 +26,11 @@ module Queries
           unless entries.any? { |e| e.user.id == context[:current_user].id }
       
             current_user_rank = ::Gamification::UserPointsActivity.user_rank(context[:current_user], period: period.to_sym)
-      
+            current_user_points = ::Gamification::UserPointsActivity.calculate_user_points_for_period(context[:current_user], period: period.to_sym)
             if current_user_rank
               entries << OpenStruct.new(
                 user: context[:current_user],
-                total_points: context[:current_user].gamification_user_profile&.total_points || 0,
+                total_points: current_user_points || 0,
                 rank: current_user_rank
               )
             end
diff --git a/app/models/gamification/user_points_activity.rb b/app/models/gamification/user_points_activity.rb
index db414394..7c59770a 100644
--- a/app/models/gamification/user_points_activity.rb
+++ b/app/models/gamification/user_points_activity.rb
@@ -54,4 +54,17 @@ class Gamification::UserPointsActivity < ApplicationRecord
 
     users_above + 1
   end
+
+  def self.calculate_user_points_for_period(user, period)
+    case period.to_sym
+    when :weekly
+      date_range = Date.current.beginning_of_week..Date.current.end_of_week
+    when :monthly
+      date_range = Date.current.beginning_of_month..Date.current.end_of_month
+    else
+      return 0
+    end
+    where(user: user, activity_date: date_range)
+      .sum(:points_earned)
+  end
 end
