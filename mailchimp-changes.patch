diff --git a/app/graphql/mutations/emails/send_marketing_email.rb b/app/graphql/mutations/emails/send_marketing_email.rb
new file mode 100644
index 00000000..ea305ab5
--- /dev/null
+++ b/app/graphql/mutations/emails/send_marketing_email.rb
@@ -0,0 +1,21 @@
+module Mutations
+  class Emails::SendMarketingEmail < BaseMutation
+    field :broadcast, Types::Objects::Emails::BroadcastType, null: false
+    field :message, String, null: true
+
+    argument :id, ID, required: true
+    argument :receive_copy, Boolean, required: true
+
+    def resolve(id:, receive_copy:)
+      broadcast = context[:current_tenant].emails_broadcasts.find(id)
+      
+      scheduler = Emails::BroadcastSchedulerService.new(broadcast, receive_copy: receive_copy)
+      scheduler.schedule_broadcast
+      
+      {
+        broadcast: broadcast.reload,
+        message: 'Marketing email scheduled successfully'
+      }
+    end
+  end
+end
diff --git a/app/jobs/emails/send_broadcast_job.rb b/app/jobs/emails/send_broadcast_job.rb
new file mode 100644
index 00000000..8e470612
--- /dev/null
+++ b/app/jobs/emails/send_broadcast_job.rb
@@ -0,0 +1,188 @@
+class Emails::SendBroadcastJob < ApplicationJob
+  include UserFilterable
+  
+  queue_as :default
+
+  def perform(broadcast_id, receive_copy: false)
+    @broadcast = Emails::Broadcast.find(broadcast_id)
+    @tenant = @broadcast.tenant
+    @receive_copy = receive_copy
+    send_campaign_email
+  rescue StandardError => e
+    @broadcast&.update(status: 'failed')
+    raise e
+  end
+
+  private
+
+  def send_campaign_email
+    @broadcast.update!(status: 'sending')
+
+    recipients = get_recipients
+    if recipients.empty?
+      @broadcast.update!(status: 'incomplete')
+      Rails.logger.info "No recipients found for this broadcast"
+      return
+    end
+
+    audience_id = '0cb58b5df0'
+    # audience_id = @tenant.mailchimp_list_id
+    client = initialize_mailchimp_client
+
+    members = recipients.map do |recipient|
+      {
+        email_address: recipient.email,
+        status_if_new: 'subscribed',
+        status: 'subscribed',
+        merge_fields: {
+          FNAME: recipient.first_name,
+          LNAME: recipient.last_name
+        }
+      }
+    end
+
+    begin
+      response = client.lists.batch_list_members(audience_id, {
+        members: members,
+        update_existing: true
+      })
+      Rails.logger.info "Mailchimp batch response: #{response}"
+    rescue MailchimpMarketing::ApiError => e
+      Rails.logger.error "Mailchimp batch error: #{e.message}"
+    end
+
+    segment_response = client.lists.create_segment(audience_id, {
+      name: "Broadcast Segment - #{@broadcast.id} - #{Time.current.to_i}",
+      static_segment: recipients.map(&:email)
+    })
+
+    segment_id = segment_response['id']
+
+    subject = personalize_content(@broadcast.subject, recipient)
+    signature = @broadcast.signature.present? ? @broadcast.signature : "#{@tenant.subdomain}@sabionet.com"
+
+    personalized_body = personalize_content(@broadcast.body, recipient)
+    processed_body = process_inline_images(personalized_body)
+    final_body = "<div style='text-align: center; margin: 0 auto; max-width: 500px; padding: 0 20px;'>#{processed_body}</div>"
+
+    campaign = client.campaigns.create({
+      type: "regular",
+      recipients: {
+        list_id: audience_id,
+        segment_opts: {
+          saved_segment_id: segment_id
+        }
+      },
+      settings: {
+        subject_line: subject,
+        title: "Marketing Email Campaign for #{@broadcast.title}",
+        from_name: @tenant.subdomain,
+        reply_to: signature
+      }
+    })
+
+    client.campaigns.set_content(campaign["id"], {
+      html: final_body
+    })
+
+    client.campaigns.send(campaign["id"])
+
+    @broadcast.update!(status: 'delivered')
+
+    Rails.logger.info "Broadcast #{@broadcast.id} proccessed for #{recipients.count} recipients"
+  end
+  
+  def get_recipients
+    recipients = case @broadcast.sent_to
+    when 'all_members'
+      get_all_students
+    when 'choose_saved_list'
+      get_filtered_students
+    else
+      []
+    end
+    if @broadcast.send_copy_to_sender || @receive_copy
+      recipients << @broadcast.sender
+    end
+    recipients.uniq
+  end
+
+  def get_all_students
+    @tenant.students.where.not(email: [nil, ''])
+  end
+
+  def get_filtered_students
+    return [] unless @broadcast.filter.present?
+
+    students = @tenant.students.where.not(email: [nil, ''])
+    filter_data = @broadcast.filter.filters
+    filters = convert_to_filter_struct(filter_data)
+
+    apply_filters(students, filters).uniq
+  end
+
+  def convert_to_filter_struct(filter_data)
+    OpenStruct.new(
+      date_range: filter_data['date_range'],
+      custom_date_range: filter_data['custom_date_range'],
+      filter_groups: filter_data['filter_groups'] || []
+    )
+  end
+
+  def initialize_mailchimp_client
+    MailchimpMarketing::Client.new.tap do |client|
+      client.set_config({
+        api_key:  'e9db08c922261ac34f5f53bd5ce435d3',
+        # api_key: @tenant.mailchimp_api_key,
+        server: extract_mailchimp_server('e9db08c922261ac34f5f53bd5ce435d3')
+      })
+    end
+  end
+
+  def personalize_content(content, recipient)
+    new_content = content.sub('@school_name', @tenant.school_name || '')
+                        .sub('@user_name', recipient.full_name || '')
+                        .sub('@user_email', recipient.email || '')
+                        .sub('@user_firstname', recipient.first_name || '')
+                        .sub('@user_lastname', recipient.last_name || '')
+                        .sub('@school_url', "https://#{@tenant.subdomain}.sabionet.com")
+                        .sub('@academy_url', @tenant.academy_url || "https://#{@tenant.subdomain}.sabionet.com")
+                        .sub('@subdomain', @tenant.subdomain || '')
+                        .sub('@student_name', recipient.full_name || '')
+                        .sub('@admin_name', @tenant.super_admin&.full_name || '')
+
+    new_content = new_content.sub('@current_date', Date.current.strftime("%B %d, %Y"))
+                            .sub('@current_year', Date.current.year.to_s)
+                            .sub('@broadcast_title', @broadcast.title || '')
+
+    new_content
+  end
+
+  def process_inline_images(body)
+    return body unless body.present?
+
+    base64_image_tags = body.scan(/<img[^>]+src=["']data:image\/(png|jpeg|gif);base64,([^"']+)["'][^>]*>/)
+
+    base64_image_tags.each_with_index do |(format, base64_data), index|
+      decoded_image = Base64.decode64(base64_data)
+
+      cid = "image#{index}"
+
+      @inline_attachments ||= []
+      @inline_attachments << {
+        type: "image/#{format}",
+        name: "#{cid}.#{format}",
+        content: Base64.encode64(decoded_image).strip,
+        content_id: cid
+      }
+
+      body.gsub!(%r{<img\s+[^>]*src=["']data:image/#{format};base64,#{Regexp.escape(base64_data)}["'][^>]*>}i, %Q(<img src="cid:#{cid}"))
+    end
+
+    body
+  end
+
+  def extract_mailchimp_server(api_key)
+    api_key.split('-').last
+  end
+end
\ No newline at end of file
diff --git a/app/models/emails/broadcast.rb b/app/models/emails/broadcast.rb
index 332d602f..ea9130f9 100644
--- a/app/models/emails/broadcast.rb
+++ b/app/models/emails/broadcast.rb
@@ -4,11 +4,23 @@ class Emails::Broadcast < ApplicationRecord
   belongs_to :template, class_name: 'Emails::Template', optional: true
   belongs_to :filter, class_name: 'UserFilter', optional: true
   has_many :activities, class_name: 'Emails::Activity', dependent: :destroy
+  belongs_to :sender, class_name: 'User', optional: true
+  before_commit :set_the_sender_id, if: :saved_change_to_send_copy_to_sender?
 
+  after_commit :schedule_broadcast_delivery, on: :create
+  after_commit :reschedule_the_job, if: :schedule_related_changes?
+  before_destroy :cancel_scheduled_job
+
+  def schedule_related_changes?
+    saved_change_to_status? || saved_change_to_scheduled_type? || saved_change_to_scheduled_at?
+  end
+  
   enum status: {
     draft: 'draft',
     ready: 'ready',
-    sent: 'sent',
+    sending: 'sending',
+    incomplete:'incomplete',
+    delivered: 'delivered',
     failed: 'failed'
   }
 
@@ -31,4 +43,29 @@ class Emails::Broadcast < ApplicationRecord
   validates :filter, presence: true, if: :list_with_filters?
 
   scope :by_status, ->(status) { where(status: status) }
+
+  private
+
+  def schedule_broadcast_delivery
+    return unless status != 'ready'
+
+    scheduler = Emails::BroadcastSchedulerService.new(self)
+    scheduler.schedule_broadcast
+  end
+
+  def reschedule_the_job
+    return unless status != 'ready'
+    cancel_scheduled_job
+    schedule_broadcast_delivery
+  end
+
+  def cancel_scheduled_job
+    return unless sidekiq_id.present?
+
+    Sidekiq::ScheduledSet.new.find_job(sidekiq_id)&.delete
+  end
+
+  def set_the_sender_id
+    self.sender_id = context[:current_user] if send_copy_to_sender
+  end
 end
diff --git a/db/migrate/20250731180056_create_email_broadcast_schema.rb b/db/migrate/20250731180056_create_email_broadcast_schema.rb
index a20f818a..c3fbc0c1 100644
--- a/db/migrate/20250731180056_create_email_broadcast_schema.rb
+++ b/db/migrate/20250731180056_create_email_broadcast_schema.rb
@@ -32,6 +32,9 @@ class CreateEmailBroadcastSchema < ActiveRecord::Migration[7.0]
       t.string :scheduled_type, null: false, default: 'send_now'
       t.datetime :scheduled_at, null: true
       t.string :sent_to, null: false, default: 'all_members'
+      t.boolean :send_copy_to_sender, null: false, default: false
+      t.references :sender, null: true, foreign_key: { to_table: :users }
+      t.boolean :active, null: false, default: false
       t.timestamps
     end
 
@@ -53,5 +56,7 @@ class CreateEmailBroadcastSchema < ActiveRecord::Migration[7.0]
     end
 
     add_index :emails_activities, :occurred_at
+    add_column :tenants, :mandrill_enabled, :boolean
+    add_column :tenants, :mandrill_api_key, :string
   end
 end
