 "updated_members"=>
  [{"id"=>"92d9fad000792332f3d0bcc9ab26b618",
    "contact_id"=>"0fd3ac6acbf58aad53dde270cc788902",
    "email_address"=>"<EMAIL>",
    "unique_email_id"=>"adcb3be91e",
    "email_type"=>"html",
    "status"=>"subscribed",
    "merge_fields"=>{"FNAME"=>"132", "LNAME"=>"sh", "ADDRESS"=>"", "PHONE"=>"", "BIRTHDAY"=>"", "COMPANY"=>""},
    "stats"=>{"avg_open_rate"=>0, "avg_click_rate"=>0},
    "ip_signup"=>"",
    "timestamp_signup"=>"",
    "ip_opt"=>"**************",
    "timestamp_opt"=>"2025-08-05T14:05:57+00:00",
    "member_rating"=>2,
    "last_changed"=>"2025-08-05T14:05:57+00:00",
    "language"=>"",
    "vip"=>false,
    "email_client"=>"",
    "location"=>{"latitude"=>0, "longitude"=>0, "gmtoff"=>0, "dstoff"=>0, "country_code"=>"", "timezone"=>""},
    "tags_count"=>1,
    "tags"=>[{"id"=>40225816, "name"=>"Broadcast Segment - 6 - 1754403411"}],
    "list_id"=>"0cb58b5df0",
    "_links"=>
     [{"rel"=>"self",
       "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members/92d9fad000792332f3d0bcc9ab26b618",
       "method"=>"GET",
       "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/Response.json"},
      {"rel"=>"parent",
       "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members",
       "method"=>"GET",
       "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/CollectionResponse.json",
       "schema"=>"https://us14.api.mailchimp.com/schema/3.0/Paths/Lists/Members/Collection.json"},
      {"rel"=>"update",
       "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members/92d9fad000792332f3d0bcc9ab26b618",
       "method"=>"PATCH",
       "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/Response.json",
       "schema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/PATCH.json"},
      {"rel"=>"upsert",
       "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members/92d9fad000792332f3d0bcc9ab26b618",
       "method"=>"PUT",
       "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/Response.json",
       "schema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/PUT.json"},
      {"rel"=>"delete", "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members/92d9fad000792332f3d0bcc9ab26b618", "method"=>"DELETE"},
      {"rel"=>"activity",
       "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members/92d9fad000792332f3d0bcc9ab26b618/activity",
       "method"=>"GET",
       "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/Activity/Response.json"},
      {"rel"=>"goals",
       "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members/92d9fad000792332f3d0bcc9ab26b618/goals",
       "method"=>"GET",
       "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/Goals/Response.json"},
      {"rel"=>"notes",
       "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members/92d9fad000792332f3d0bcc9ab26b618/notes",
       "method"=>"GET",
       "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/Notes/CollectionResponse.json"},
      {"rel"=>"events",
       "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members/92d9fad000792332f3d0bcc9ab26b618/events",
       "method"=>"POST",
       "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/Events/POST.json"},
      {"rel"=>"delete_permanent", "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members/92d9fad000792332f3d0bcc9ab26b618/actions/delete-permanent", "method"=>"POST"}]},
   {"id"=>"f56343b0cadc8cef4d96cad2fda1de62",
    "contact_id"=>"72f7e540dfc102b4e6eeb932624f1a4f",
    "email_address"=>"<EMAIL>",
    "unique_email_id"=>"d4de1e6d4e",
    "email_type"=>"html",
    "status"=>"subscribed",
    "merge_fields"=>{"FNAME"=>"stuudent", "LNAME"=>"g2", "ADDRESS"=>"", "PHONE"=>"", "BIRTHDAY"=>"", "COMPANY"=>""},
    "stats"=>{"avg_open_rate"=>0, "avg_click_rate"=>0},
    "ip_signup"=>"",
    "timestamp_signup"=>"",
    "ip_opt"=>"**************",
    "timestamp_opt"=>"2025-08-05T14:05:57+00:00",
    "member_rating"=>2,
    "last_changed"=>"2025-08-05T14:05:57+00:00",
    "language"=>"",
    "vip"=>false,
    "email_client"=>"",
    "location"=>{"latitude"=>0, "longitude"=>0, "gmtoff"=>0, "dstoff"=>0, "country_code"=>"", "timezone"=>""},
    "tags_count"=>1,
    "tags"=>[{"id"=>40225816, "name"=>"Broadcast Segment - 6 - 1754403411"}],
    "list_id"=>"0cb58b5df0",
    "_links"=>
     [{"rel"=>"self",
       "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members/f56343b0cadc8cef4d96cad2fda1de62",
       "method"=>"GET",
       "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/Response.json"},
      {"rel"=>"parent",
       "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members",
       "method"=>"GET",
       "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/CollectionResponse.json",
       "schema"=>"https://us14.api.mailchimp.com/schema/3.0/Paths/Lists/Members/Collection.json"},
      {"rel"=>"update",
       "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members/f56343b0cadc8cef4d96cad2fda1de62",
       "method"=>"PATCH",
       "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/Response.json",
       "schema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/PATCH.json"},
      {"rel"=>"upsert",
       "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members/f56343b0cadc8cef4d96cad2fda1de62",
       "method"=>"PUT",
       "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/Response.json",
       "schema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/PUT.json"},
      {"rel"=>"delete", "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members/f56343b0cadc8cef4d96cad2fda1de62", "method"=>"DELETE"},
      {"rel"=>"activity",
       "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members/f56343b0cadc8cef4d96cad2fda1de62/activity",
       "method"=>"GET",
       "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/Activity/Response.json"},
      {"rel"=>"goals",
       "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members/f56343b0cadc8cef4d96cad2fda1de62/goals",
       "method"=>"GET",
       "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/Goals/Response.json"},
      {"rel"=>"notes",
       "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members/f56343b0cadc8cef4d96cad2fda1de62/notes",
       "method"=>"GET",
       "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/Notes/CollectionResponse.json"},
      {"rel"=>"events",
       "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members/f56343b0cadc8cef4d96cad2fda1de62/events",
       "method"=>"POST",
       "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/Events/POST.json"},
      {"rel"=>"delete_permanent", "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members/f56343b0cadc8cef4d96cad2fda1de62/actions/delete-permanent", "method"=>"POST"}]}],
 "errors"=>
  [{"email_address"=>"<EMAIL>", "error"=>"<EMAIL> looks fake or invalid, please enter a real email address.", "error_code"=>"ERROR_GENERIC", "field"=>"", "field_message"=>""},
   {"email_address"=>"<EMAIL>",
    "error"=>"<EMAIL> looks fake or invalid, please enter a real email address.",
    "error_code"=>"ERROR_GENERIC",
    "field"=>"",
    "field_message"=>""},
   {"email_address"=>"<EMAIL>", "error"=>"<EMAIL> looks fake or invalid, please enter a real email address.", "error_code"=>"ERROR_GENERIC", "field"=>"", "field_message"=>""},
   {"email_address"=>"<EMAIL>", "error"=>"<EMAIL> looks fake or invalid, please enter a real email address.", "error_code"=>"ERROR_GENERIC", "field"=>"", "field_message"=>""},
   {"email_address"=>"<EMAIL>",
    "error"=>"<EMAIL> looks fake or invalid, please enter a real email address.",
    "error_code"=>"ERROR_GENERIC",
    "field"=>"",
    "field_message"=>""},
   {"email_address"=>"<EMAIL>",
    "error"=>"<EMAIL> looks fake or invalid, please enter a real email address.",
    "error_code"=>"ERROR_GENERIC",
    "field"=>"",
    "field_message"=>""},
   {"email_address"=>"<EMAIL>", "error"=>"<EMAIL> looks fake or invalid, please enter a real email address.", "error_code"=>"ERROR_GENERIC", "field"=>"", "field_message"=>""},
   {"email_address"=>"<EMAIL>", "error"=>"<EMAIL> looks fake or invalid, please enter a real email address.", "error_code"=>"ERROR_GENERIC", "field"=>"", "field_message"=>""},
   {"email_address"=>"<EMAIL>", "error"=>"<EMAIL> looks fake or invalid, please enter a real email address.", "error_code"=>"ERROR_GENERIC", "field"=>"", "field_message"=>""},
   {"email_address"=>"<EMAIL>", "error"=>"<EMAIL> looks fake or invalid, please enter a real email address.", "error_code"=>"ERROR_GENERIC", "field"=>"", "field_message"=>""},
   {"email_address"=>"<EMAIL>", "error"=>"<EMAIL> looks fake or invalid, please enter a real email address.", "error_code"=>"ERROR_GENERIC", "field"=>"", "field_message"=>""},
   {"email_address"=>"<EMAIL>", "error"=>"<EMAIL> looks fake or invalid, please enter a real email address.", "error_code"=>"ERROR_GENERIC", "field"=>"", "field_message"=>""},
   {"email_address"=>"<EMAIL>", "error"=>"<EMAIL> looks fake or invalid, please enter a real email address.", "error_code"=>"ERROR_GENERIC", "field"=>"", "field_message"=>""},
   {"email_address"=>"<EMAIL>", "error"=>"<EMAIL> looks fake or invalid, please enter a real email address.", "error_code"=>"ERROR_GENERIC", "field"=>"", "field_message"=>""}],
 "total_created"=>0,
 "total_updated"=>2,
 "error_count"=>14,
 "_links"=>
  [{"rel"=>"self", "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0", "method"=>"GET", "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Response.json"},
   {"rel"=>"parent",
    "href"=>"https://us14.api.mailchimp.com/3.0/lists",
    "method"=>"GET",
    "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/CollectionResponse.json",
    "schema"=>"https://us14.api.mailchimp.com/schema/3.0/Paths/Lists/Collection.json"},
   {"rel"=>"update",
    "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0",
    "method"=>"PATCH",
    "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Response.json",
    "schema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/PATCH.json"},
   {"rel"=>"batch-sub-unsub-members",
    "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0",
    "method"=>"POST",
    "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/BatchPOST-Response.json",
    "schema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/BatchPOST.json"},
   {"rel"=>"delete", "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0", "method"=>"DELETE"},
   {"rel"=>"abuse-reports",
    "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/abuse-reports",
    "method"=>"GET",
    "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Abuse/CollectionResponse.json",
    "schema"=>"https://us14.api.mailchimp.com/schema/3.0/Paths/Lists/Abuse/Collection.json"},
   {"rel"=>"activity",
    "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/activity",
    "method"=>"GET",
    "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Activity/Response.json"},
   {"rel"=>"clients",
    "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/clients",
    "method"=>"GET",
    "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Clients/Response.json"},
   {"rel"=>"growth-history",
    "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/growth-history",
    "method"=>"GET",
    "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Growth/CollectionResponse.json",
    "schema"=>"https://us14.api.mailchimp.com/schema/3.0/Paths/Lists/Growth/Collection.json"},
   {"rel"=>"interest-categories",
    "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/interest-categories",
    "method"=>"GET",
    "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/InterestCategories/CollectionResponse.json",
    "schema"=>"https://us14.api.mailchimp.com/schema/3.0/Paths/Lists/InterestCategories/Collection.json"},
   {"rel"=>"members",
    "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/members",
    "method"=>"GET",
    "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Members/CollectionResponse.json",
    "schema"=>"https://us14.api.mailchimp.com/schema/3.0/Paths/Lists/Members/Collection.json"},
   {"rel"=>"merge-fields",
    "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/merge-fields",
    "method"=>"GET",
    "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/MergeFields/CollectionResponse.json",
    "schema"=>"https://us14.api.mailchimp.com/schema/3.0/Paths/Lists/MergeFields/Collection.json"},
   {"rel"=>"segments",
    "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/segments",
    "method"=>"GET",
    "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Segments/CollectionResponse.json",
    "schema"=>"https://us14.api.mailchimp.com/schema/3.0/Paths/Lists/Segments/Collection.json"},
   {"rel"=>"webhooks",
    "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/webhooks",
    "method"=>"GET",
    "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Webhooks/CollectionResponse.json",
    "schema"=>"https://us14.api.mailchimp.com/schema/3.0/Paths/Lists/Webhooks/Collection.json"},
   {"rel"=>"signup-forms",
    "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/signup-forms",
    "method"=>"GET",
    "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/SignupForms/CollectionResponse.json",
    "schema"=>"https://us14.api.mailchimp.com/schema/3.0/Paths/Lists/SignupForms/Collection.json"},
   {"rel"=>"locations",
    "href"=>"https://us14.api.mailchimp.com/3.0/lists/0cb58b5df0/locations",
    "method"=>"GET",
    "targetSchema"=>"https://us14.api.mailchimp.com/schema/3.0/Definitions/Lists/Locations/CollectionResponse.json",
    "schema"=>"https://us14.api.mailchimp.com/schema/3.0/Paths/Lists/Locations/Collection.json"}]}
(END)

