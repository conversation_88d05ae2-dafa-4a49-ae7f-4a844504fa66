module Sabiorealm::TransactionProviders
  module StripeConnect
    class << self
      def find(provider_id)
        ::Purchases::ProductPurchase.find_by(provider_id: provider_id)
      end

      def initiate(transaction)
        ::Stripe.api_key = ENV.fetch('STRIPE_CONNECT_API_KEY', '')
        if transaction.price.subscription
          coupon = transaction.coupon
          stripe_product = stripe_product(transaction)
          stripe_price = stripe_price(transaction, stripe_product.id)
          # purchase = coupon&.product_purchases&.find_by(status: 'approved', user_id: transaction.user.id) 
          # raise Sabiorealm::Error, "You already used this coupon" if purchase.present?

          stripe_coupon = stripe_coupon(coupon) if coupon.instance_of? Purchases::CouponPriceDiscount
          trial_period_days = transaction.price.trial_period_days 
          trial_period_days = transaction.coupon.trial_period_days if coupon.instance_of? Purchases::CouponTrialDays
          subscription = stripe_checkout(transaction, stripe_price.id, stripe_coupon, trial_period_days)
         
          transaction.provider_data['initiate_response_data'] = subscription
          transaction.payment_type = 'subscription' 
          transaction.payment_type = 'trial' if trial_period_days.present?         
          transaction.provider_id = fecth_payment_intent_id(subscription)

          { checkout_url: fecth_client_secret(subscription), client_secret:  fecth_client_secret(subscription), setup_intent: is_setup_intent?(subscription) }
        else
          # Build the request data that needs to be sent to the provider API
          request_data = build_request_data(transaction)
          transaction.provider_data['initiate_request_data'] = request_data
          # Build the authenticated client that will be used to execute the request
          client = build_client(transaction.payment_method)
          # Execute the requestc
          response_data = execute_request(client, request_data)
          transaction.provider_data['initiate_response_data'] = response_data
          transaction.provider_id = response_data.id
          # Create the response
          { checkout_url: response_data.client_secret, client_secret: response_data.client_secret, setup_intent: false }
        end
      end

      def verify(transaction)
        ::Stripe.api_key = ENV.fetch('STRIPE_CONNECT_API_KEY', '')

        if transaction.payment_type == 'trial' || transaction.amount_cents.zero?
          response_data = ::Stripe::SetupIntent.retrieve(transaction.provider_id)
        else
          response_data = ::Stripe::PaymentIntent.retrieve(transaction.provider_id)
        end

        transaction.provider_data['verify_response_data'] = response_data
        transaction.verification_status =
              case response_data.status
              when "succeeded"
                :approved
              else
                :in_progress
              end
        response_data
      end

      def cancel_subscription(transaction)
        ::Stripe.api_key = ENV.fetch('STRIPE_CONNECT_API_KEY', '')
        expiration_date = transaction.subscriptions.last.expiration_date#.stripe_connect_subscription_id

        unless expiration_date.present?
          stripe_subscription = get_stripe_subscription(transaction)
          expiration_date = Time.at(stripe_subscription.current_period_end).to_date
          transaction.subscriptions.last.update(expiration_date: expiration_date)
        end

        if (Date.today + 20.days) < expiration_date
          refund_request = create_refund_request(transaction)
          unless refund_request.status == 'approved'
            refund = transaction.process_refund
            refund_request.approved! if refund.status == 'succeeded'
          end
          "cancelled"
        else
          stripe_subscription = get_stripe_subscription(transaction)

          if stripe_subscription.status != 'canceled'
            ::Stripe::Subscription.delete(stripe_subscription.id)
          end
          create_refund_request(transaction)
          "cancelled"
        end
      end

      private

      def get_stripe_subscription(transaction)
        stripe_connect_subscription_id = transaction.subscriptions.last.stripe_connect_subscription_id
        ::Stripe::Subscription.retrieve(stripe_connect_subscription_id)
      end

      def create_refund_request(transaction)
        existing_requests = ::Purchases::RefundRequest.where(product_purchase_id: transaction.id)
      
        if existing_requests.exists?
          existing_requests.find_by(status: :approved) || existing_requests.first
        else
          ::Purchases::RefundRequest.create(
            tenant_id: transaction.tenant_id,
            product_purchase_id: transaction.id,
            status: 'pending',
            reason: 'requested_by_customer'
          )
        end
      end

      def stripe_checkout(transaction, price_id, stripe_coupon, trial_period_days)
        user = transaction.user
        tenant = transaction.tenant
        product = transaction.product
        transfer_amount_percent = 100 - (3 + (30.0 * 100 / transaction.price.real_price_cents))
        if transaction.product_type == 'Courses::Course' && product.is_available_on_affiliate
          transfer_amount_percent -= product.affiliate_setting.commission_percentage.to_i
        end

        if transaction.tenant.feature_plan_id == 'free'
          transfer_amount_percent -= 10
        else
          transfer_amount_percent -= 1.5 # For the other plans charge 1.5 percent commission
        end

        # session_args = {
        #   client_reference_id: transaction.user_id,
        #   success_url: "#{transaction.origin}/callbacks/stripe_subscription_student_success",
        #   cancel_url: "#{transaction.origin}/callbacks/stripe_subscription_student_cancel",
        #   mode: 'subscription',
        #   locale: tenant.language == 'pt-br' ? 'pt-BR' : tenant.language,
        #   line_items: [{ quantity: 1, price: price_id}],
        #   payment_method_types: ['card'],
        #   subscription_data: {},
        #   custom_text: {
        #     submit: {
        #       message: "You may request a instant refund within 10 days of subscribing."
        #     }
        #   },
        # }
        # session_args[:subscription_data][:transfer_data] = { destination: transaction.payment_method&.account_id }
        # session_args[:subscription_data][:transfer_data][:amount_percent] = transfer_amount_percent
        # session_args[:subscription_data][:application_fee_percent] = 9 if transaction.tenant.feature_plan_id == 'free'

        subscription_data = {
          items: [{ price: price_id }],
          payment_behavior: 'default_incomplete',
          payment_settings: { save_default_payment_method: 'on_subscription' },
          expand: ['latest_invoice.payment_intent'],
          metadata: { transaction_id: transaction.id },
          transfer_data: {
            destination: transaction.payment_method.account_id,
            amount_percent: transfer_amount_percent.round(2)
          }
        }

        subscription_data[:trial_period_days] = trial_period_days if trial_period_days.present?
        subscription_data[:discounts] = [{ coupon: stripe_coupon.id }] if stripe_coupon.present?


        transaction.provider_data['initiate_request_data'] = subscription_data
        if user.stripe_connect_customer_id.present?
          subscription_data[:customer] = user.stripe_connect_customer_id
        else
          customer = ::Stripe::Customer.create({ name: user.full_name, email: user.email})
          subscription_data[:customer] = customer.id
          MultiTenantSupport.under_tenant user.tenant do
            user.update(stripe_connect_customer_id: customer.id)
          end
        end

        if tenant.subdomain.eql? 'Groom-Education'
          ::Stripe::Customer.update(user.stripe_connect_customer_id,
            invoice_settings: {
              custom_fields: [
                {
                  name: 'ID / DNI / Pasaporte',
                  value: user.custom_fields['id_dni_pasaporte']
                },
                {
                  name: 'Dirección',
                  value: user.custom_fields['direccion']
                },
                {
                  name: 'Ciudad / City',
                  value: user.custom_fields['ciudad_city']
                },
                {
                  name: 'Código Postal / ZIP Code',
                  value: user.custom_fields['codigo_postal_zip_code']
                }
              ]
            }
          )
        end
        ::Stripe::Subscription.create(subscription_data)
      end

      def stripe_price(transaction, product_id)
        amount = transaction.amount_cents
        currency = transaction.price.real_price.currency.iso_code
        price_id = transaction.price.stripe_connect_price_id

        if price_id.present?
          stripe_price = ::Stripe::Price.retrieve(price_id)
          return stripe_price if (stripe_price.unit_amount == amount) && (stripe_price.currency.upcase! == currency)
          response = ::Stripe::Price.search({ query: "product:'#{product_id}' AND currency:'#{currency}'"})
          stripe_price = response.data.find { |p| p.unit_amount == amount }
        end

        stripe_price = stripe_price_create(transaction, product_id) if stripe_price.nil?
        transaction.price.update(stripe_connect_price_id: stripe_price.id)
        stripe_price
      end

      def stripe_price_create(transaction, product_id)
        ::Stripe::Price.create({
          unit_amount: transaction.amount_cents,
          currency: transaction.price.real_price.currency.iso_code,
          recurring: { interval: 'month' },
          product: product_id
        })
      end

      def stripe_product(transaction)
        product_id = transaction.product.stripe_connect_product_id
        if product_id.nil?
          stripe_product = ::Stripe::Product.create({ name: transaction.product.name })
          transaction.product.update(stripe_connect_product_id: stripe_product.id)
          stripe_product
        else
          ::Stripe::Product.retrieve(product_id)
        end
      end

      def stripe_coupon(coupon)
        response = ::Stripe::Coupon.list
        stripe_coupon = response.data.find { |c| c.percent_off == coupon.discount_percent }
        if stripe_coupon.nil?
          stripe_coupon = ::Stripe::Coupon.create({
            percent_off: coupon.discount_percent,
            duration: 'forever'
          })
        end
        stripe_coupon
      end

      def build_client(payment_method)
        lambda do |request_data|
          ::Stripe::PaymentIntent.create(request_data)
        end
      end

      def build_verification_client(payment_method)
        lambda do |intent_id|
          ::Stripe::PaymentIntent.retrieve(intent_id)
        end
      end

      def build_request_data(transaction)
        sale_amount = transaction.amount_cents
        transfer_amount = sale_amount - (30 + (sale_amount * 3.0)/ 100)
        product = transaction.product

        if transaction.product_type == 'Courses::Course' && product.is_available_on_affiliate
          commission_percentage = product.affiliate_setting.commission_percentage.to_i
          commission_amount = (sale_amount * commission_percentage / 100.0).to_i
          transfer_amount -= commission_amount
        end

        if transaction.tenant.feature_plan_id == 'free'
          commission_percentage = 10
          commission_amount = (sale_amount * commission_percentage / 100.0).to_i

          transfer_amount -= commission_amount
        else
          commission_percentage = 1.5
          commission_amount = (sale_amount * commission_percentage / 100.0).to_i
          transfer_amount -= commission_amount
        end

        payment_methods = ['card']
        if transaction.price&.installments_enabled?
          payment_methods += ['klarna', 'afterpay_clearpay', 'affirm']
        end
        # Builds the request data, saves it to the transaction
        order_data = {
          # success_url: "#{transaction.origin}/callbacks/stripe-success?session_id={CHECKOUT_SESSION_ID}",
          # cancel_url: "#{transaction.origin}/callbacks/stripe-cancel?session_id={CHECKOUT_SESSION_ID}",
          payment_method_types: payment_methods,
          client_reference_id: transaction.id,
          # mode: 'payment',
          # payment_intent_data: {
            transfer_data: { destination: transaction.payment_method&.account_id, amount: transfer_amount.to_i }# Set the transfer amount after commission
          # }
        }
        order_data.merge! product_order_data(transaction)
        order_data.merge! customer_data(transaction.user)
      end

      def execute_request(client, request_data)
        # Calls the API with the request data, processes the response
        client.call(request_data) # response with http request data
      rescue ::Stripe::InvalidRequestError => e
        raise Sabiorealm::Error, e.message
      end

      def execute_verification(client, request_data)
        # Calls the API with the request data, processes the response
        client.call(request_data) # response with http request data
      rescue ::Stripe::InvalidRequestError => e
        raise Sabiorealm::Error, e.message
      end

      def product_order_data(transaction)
        amount_cents = transaction.amount_cents
        currency = transaction.amount.currency.iso_code
        # {
        #   line_items: [
        #     {
        #       quantity: 1,
        #       price_data: {
        #         currency: currency,
        #         unit_amount: amount_cents,
        #         product_data: {
        #           name: transaction.product.name
        #         }
        #       }
        #     }
        #   ]
        # }
        {
          amount: amount_cents,
          currency: currency,
        }
      end

      def customer_data(customer)
        {
          receipt_email: customer.email
        }
      end

      def fecth_client_secret(subscription)
        if subscription.latest_invoice&.payment_intent.present?
          subscription.latest_invoice&.payment_intent&.client_secret
        elsif subscription.pending_setup_intent.present?
          setup_intent = ::Stripe::SetupIntent.retrieve(subscription.pending_setup_intent)
          setup_intent.client_secret 
        end
      end

      def fecth_payment_intent_id(subscription)
        if subscription.latest_invoice&.payment_intent.present?
          subscription.latest_invoice&.payment_intent&.id
        elsif subscription.pending_setup_intent.present?
          subscription.pending_setup_intent
        end
      end

      def is_setup_intent?(subscription)
        subscription.latest_invoice&.payment_intent.blank? && subscription.pending_setup_intent.present?
      end
    end
  end
end