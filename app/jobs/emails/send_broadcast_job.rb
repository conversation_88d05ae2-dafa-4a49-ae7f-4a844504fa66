class Emails::SendBroadcastJob < ApplicationJob
  include UserFilterable
  
  queue_as :default

  def perform(broadcast_id)
    binding.pry
    @broadcast = Emails::Broadcast.find(broadcast_id)
    @tenant = @broadcast.tenant
    send_campaign_email
  rescue StandardError => e
    @broadcast&.update(status: 'failed')
    raise e
  end

  private

  def send_campaign_email
    @broadcast.update!(status: 'sending')

    recipients = get_recipients
    if recipients.empty?
      @broadcast.update!(status: 'incomplete')
      return
    end

    audience_id = @tenant.mailchimp_list_id
    client = initialize_mailchimp_client

    members = recipients.map do |recipient|
      {
        email_address: recipient.email,
        status_if_new: 'subscribed',
        status: 'subscribed',
        merge_fields: {
          FNAME: recipient.first_name,
          LNAME: recipient.last_name,
          EMAIL: recipient.email,
          SCHOOLNAME: @tenant.school_name,
          SUBDOMAIN: @tenant.subdomain
        }
      }
    end

    begin
      binding.pry
      response = client.lists.batch_list_members(audience_id, {
        members: members,
        update_existing: true
      })
    rescue MailchimpMarketing::ApiError => e
      Rails.logger.error "Mailchimp batch error: #{e.message}"
    end

    segment_response = client.lists.create_segment(audience_id, {
      name: "Broadcast Segment - #{@broadcast.id} - #{Time.current.to_i}",
      static_segment: recipients.map(&:email)
    })

    segment_id = segment_response['id']

    subject = personalize_content(@broadcast.subject, recipient)
    subject = @broadcast.subject
    signature = @broadcast.signature.present? ? @broadcast.signature : "#{@tenant.subdomain}@sabionet.com"

    # personalized_body = personalize_content(@broadcast.body, recipient)
    # processed_body = process_inline_images(personalized_body)
    # final_body = "<div style='text-align: center; margin: 0 auto; max-width: 500px; padding: 0 20px;'>#{processed_body}</div>"
    final_body = @broadcast.body
    binding.pry
    campaign = client.campaigns.create({
      type: "regular",
      recipients: {
        list_id: audience_id,
        segment_opts: {
          saved_segment_id: segment_id
        }
      },
      settings: {
        subject_line: subject,
        title: "Marketing Email Campaign for #{@broadcast.title}",
        from_name: @tenant.subdomain,
        reply_to: signature
      }
    })

    client.campaigns.set_content(campaign["id"], {
      html: final_body
    })

    client.campaigns.send(campaign["id"])

    @broadcast.update!(status: 'delivered')
  end
  
  def get_recipients
    recipients = case @broadcast.sent_to
    when 'all_members'
      get_all_students
    when 'choose_saved_list'
      get_filtered_students
    else
      []
    end
    if @broadcast.send_copy_to_sender
      recipients << @broadcast.sender
    end
    recipients.uniq
  end

  def get_all_students
    @tenant.students.where.not(email: [nil, ''])
  end

  def get_filtered_students
    return [] unless @broadcast.filter.present?

    students = @tenant.students.where.not(email: [nil, ''])
    filter_data = @broadcast.filter.filters
    filters = convert_to_filter_struct(filter_data)

    apply_filters(students, filters).uniq
  end

  def convert_to_filter_struct(filter_data)
    OpenStruct.new(
      date_range: filter_data['date_range'],
      custom_date_range: filter_data['custom_date_range'],
      filter_groups: filter_data['filter_groups'] || []
    )
  end

  def initialize_mailchimp_client
    MailchimpMarketing::Client.new.tap do |client|
      client.set_config({
        api_key: '*************************************',
        server: extract_mailchimp_server('*************************************')
      })
    end
  end

  def personalize_content(content, recipient)
    new_content = content.sub('@school_name', @tenant.school_name || '')
                        .sub('@user_name', recipient.full_name || '')
                        .sub('@user_email', recipient.email || '')
                        .sub('@user_firstname', recipient.first_name || '')
                        .sub('@user_lastname', recipient.last_name || '')
                        .sub('@school_url', "https://#{@tenant.subdomain}.sabionet.com")
                        .sub('@academy_url', @tenant.academy_url || "https://#{@tenant.subdomain}.sabionet.com")
                        .sub('@subdomain', @tenant.subdomain || '')
                        .sub('@student_name', recipient.full_name || '')
                        .sub('@admin_name', @tenant.super_admin&.full_name || '')

    new_content = new_content.sub('@current_date', Date.current.strftime("%B %d, %Y"))
                            .sub('@current_year', Date.current.year.to_s)
                            .sub('@broadcast_title', @broadcast.title || '')

    new_content
  end

  def process_inline_images(body)
    return body unless body.present?

    base64_image_tags = body.scan(/<img[^>]+src=["']data:image\/(png|jpeg|gif);base64,([^"']+)["'][^>]*>/)

    base64_image_tags.each_with_index do |(format, base64_data), index|
      decoded_image = Base64.decode64(base64_data)

      cid = "image#{index}"

      @inline_attachments ||= []
      @inline_attachments << {
        type: "image/#{format}",
        name: "#{cid}.#{format}",
        content: Base64.encode64(decoded_image).strip,
        content_id: cid
      }

      body.gsub!(%r{<img\s+[^>]*src=["']data:image/#{format};base64,#{Regexp.escape(base64_data)}["'][^>]*>}i, %Q(<img src="cid:#{cid}"))
    end

    body
  end

  def extract_mailchimp_server(api_key)
    api_key.split('-').last
  end
end
