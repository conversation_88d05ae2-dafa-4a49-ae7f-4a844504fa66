class Emails::SendBroadcastJob < ApplicationJob
  include UserFilterable
  include HTTParty

  queue_as :default

  base_uri 'https://mandrillapp.com/api/1.0'

  def perform(broadcast_id, receive_copy: false)
    @broadcast = Emails::Broadcast.find(broadcast_id)
    @tenant = @broadcast.tenant
    @receive_copy = receive_copy
    send_broadcast_emails
  rescue StandardError => e
    @broadcast&.update(status: 'failed')
    raise e
  end

  private

  def send_broadcast_emails
    @broadcast.update!(status: 'sending')

    recipients = get_recipients
    if recipients.empty?
      @broadcast.update!(status: 'incomplete')
      Rails.logger.info "No recipients found for this broadcast"
      return
    end
    send_emails_to_recipients(recipients)

    @broadcast.update!(status: 'delivered')

    Rails.logger.info "Broadcast #{@broadcast.id} proccessed for #{recipients.count} recipients"
  end
  
  def get_recipients
    recipients = case @broadcast.sent_to
    when 'all_members'
      get_all_students
    when 'choose_saved_list'
      get_filtered_students
    else
      []
    end
    if @broadcast.send_copy_to_sender || @receive_copy
      recipients << @tenant.super_admin
    end
    recipients.uniq
  end

  def get_all_students
    @tenant.students.where.not(email: [nil, ''])
  end

  def get_filtered_students
    return [] unless @broadcast.filter.present?

    students = @tenant.students.where.not(email: [nil, ''])
    filter_data = @broadcast.filter.filters
    filters = convert_to_filter_struct(filter_data)

    filter_users = studentsapply_filters(students, filters).uniq
    filter_users
  end

  def convert_to_filter_struct(filter_data)
    OpenStruct.new(
      date_range: filter_data['date_range'],
      custom_date_range: filter_data['custom_date_range'],
      filter_groups: filter_data['filter_groups'] || []
    )
  end

  def send_emails_to_recipients(recipients)
    delivered_count = 0
    bounced_count = 0
    recipients.each do |recipient|
      create_activity_record(recipient, 'pending')
        result = send_individual_email(recipient)
        if result == 'sent'
          Rails.logger.info "Broadcast #{@broadcast.title} delivered for #{recipient.full_name} recipients"
          delivered_count += 1
        else
          Rails.logger.info "Broadcast #{@broadcast.title} bounced for #{recipient.full_name} recipients"

          bounced_count += 1
        end
      sleep(0.1)
    end

    @broadcast.update!(delivered: delivered_count, bounced: bounced_count)
  end
  
  def send_individual_email(recipient)
    @inline_attachments = []

    subject = personalize_content(@broadcast.subject, recipient)
    personalized_body = personalize_content(@broadcast.body, recipient)

    processed_body = process_inline_images(personalized_body)

    final_body = "<div style='text-align: center; margin: 0 auto; max-width: 500px; padding: 0 20px;'>#{processed_body}</div>"
    signature = @broadcast.signature_status ? @broadcast.signature : @tenant.default_signature
    mandrill_api_key = get_mandrill_api_key 
    message_data = {
      key: mandrill_api_key,
      message: {
        subject: subject,
        from_email: signature,
        from_name: @tenant.super_admin.full_name,
        to: [
          {
            email: recipient.email,
            name: recipient.full_name,
            type: 'to'
          }
        ],
        html: final_body,
        auto_text: true,
        merge: true,
        merge_language: 'handlebars',
        tags: [@broadcast.id.to_s, 'broadcast', @tenant.subdomain]
      }
    }

    if @inline_attachments&.any?
      message_data[:message][:images] = @inline_attachments
    end

    response = self.class.post('/messages/send.json', {
      body: message_data.to_json,
      headers: { 'Content-Type' => 'application/json' }
    })

    if response.first['status'] == 'sent'
      message_id = response.first['_id']
      update_activity_with_message_id(recipient,'success', message_id)
    else
      message_id = response&.dig(0, '_id') || raise("Mandrill response missing _id: #{response.inspect}")
      update_activity_with_message_id(recipient,'failed', message_id)
    end

    response.first['status']
  end
  
  
  def create_activity_record(recipient, status, error_message = nil)
    @tenant.email_activities.create!(
      broadcast: @broadcast,
      user: recipient,
      email_address: recipient.email,
      status: status,
      error_message: error_message,
      occurred_at: Time.current
    )
  end
  
  def update_activity_with_message_id(recipient, status, message_id)
    activity = @tenant.email_activities.find_by(
      broadcast: @broadcast,
      user: recipient,
    )

    activity&.update(status:status, message_id: message_id)
  end

  def get_mandrill_api_key
    superadmin = @tenant.super_admin

    if @tenant.mandrill_enabled &&@tenant.mandrill_api_key.present?
      @tenant.mandrill_api_key
      'md-**********************'
    else
      raise StandardError, "No Mandrill API key found for tenant #{@tenant.id}."
    end
  end

  def personalize_content(content, recipient)
    new_content = content.sub('@school_name', @tenant.school_name || '')
                        .sub('@user_name', recipient.full_name || '')
                        .sub('@user_email', recipient.email || '')
                        .sub('@user_firstname', recipient.first_name || '')
                        .sub('@user_lastname', recipient.last_name || '')
                        .sub('@school_url', "https://#{@tenant.subdomain}.sabionet.com")
                        .sub('@academy_url', @tenant.academy_url || "https://#{@tenant.subdomain}.sabionet.com")
                        .sub('@subdomain', @tenant.subdomain || '')
                        .sub('@student_name', recipient.full_name || '')
                        .sub('@admin_name', @tenant.super_admin&.full_name || '')

    new_content = new_content.sub('@current_date', Date.current.strftime("%B %d, %Y"))
                            .sub('@current_year', Date.current.year.to_s)
                            .sub('@broadcast_title', @broadcast.title || '')

    new_content
  end

  def process_inline_images(body)
    return body unless body.present?
    binding.pry
    base64_image_tags = body.scan(/<img[^>]+src=["']data:image\/(png|jpeg|gif);base64,([^"']+)["'][^>]*>/)

    base64_image_tags.each_with_index do |(format, base64_data), index|
      decoded_image = Base64.decode64(base64_data)

      cid = "image#{index}"

      @inline_attachments ||= []
      @inline_attachments << {
        type: "image/#{format}",
        name: "#{cid}.#{format}",
        content: Base64.encode64(decoded_image).strip,
        content_id: cid
      }

      body.gsub!(%r{<img\s+[^>]*src=["']data:image/#{format};base64,#{Regexp.escape(base64_data)}["'][^>]*>}i, %Q(<img src="cid:#{cid}"))
    end

    body
  end
end