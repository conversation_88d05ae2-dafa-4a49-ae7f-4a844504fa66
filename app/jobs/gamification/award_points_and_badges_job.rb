class Gamification::AwardPointsAndBadgesJob < ApplicationJob
  queue_as :default

  def perform(user, action_type, options = {})
    user_inital_points = user&.gamification_user_profile&.total_points

    if options[:award_points]
      call_points_distributor(user, action_type)
    end

    if options[:unlock_badges]
      call_badge_unlocker(user, action_type)
    end

    user_final_points = user&.gamification_user_profile&.total_points
    if user_final_points > user_inital_points
      call_reward_distributor(user)
    end
  end

  def self.award_points_only(user, action_type)
    perform_later(user, action_type, award_points: true, unlock_badges: false) if user.tenant.gamification_enabled?
  end

  def self.unlock_badges_only(user, action_type)
    perform_later(user, action_type, award_points: false, unlock_badges: true) if user.tenant.gamification_enabled?
  end

  def self.award_points_and_unlock_badges(user, action_type)
    perform_later(user, action_type, award_points: true, unlock_badges: true) if user.tenant.gamification_enabled?
  end

  private

  def call_points_distributor(user, action_type)
    Gamification::PointsDistributor.call(user: user, action_type: action_type)
  rescue StandardError => e
    Rollbar.error(e)
  end

  def call_badge_unlocker(user, action_type)
    Gamification::BadgeUnlocker.call(user: user, action_type: action_type)
  rescue StandardError => e
    Rollbar.error(e)
  end

  def call_reward_distributor(user)
    Gamification::RewardDistributor.call(user: user)
  rescue StandardError => e
    Rollbar.error(e)
  end
end
