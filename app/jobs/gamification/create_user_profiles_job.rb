class Gamification::CreateUserProfilesJob < ApplicationJob
  queue_as :default

  def perform(gamification_setting_id)
    setting = Gamification::Setting.find(gamification_setting_id)
    tenant = setting.tenant

    first_level = setting.level_settings.enabled.order(:position).first
    unless first_level
      Rails.logger.error "No enabled level settings found for tenant: #{tenant.subdomain}"
      return
    end

    students_without_profiles = tenant.students
                                      .left_joins(:gamification_user_profile)
                                      .where(gamification_user_profiles: { id: nil })
        
    students_without_profiles.find_in_batches(batch_size: 100) do |student_batch|
      profiles_to_create = []

      student_batch.each do |student|
        profiles_to_create << {
          user_id: student.id,
          total_points: 0,
          current_level_progress: 0,
          level_setting_id: first_level.id,
        }
      end
      Gamification::UserProfile.insert_all(profiles_to_create) if profiles_to_create.any?
    end
  
    Rails.logger.info "Completed creating gamification user profiles for tenant: #{tenant.subdomain}"
  rescue StandardError => e
    Rails.logger.error "Failed to create user profiles for tenant #{tenant&.subdomain}: #{e.message}"
    Rollbar.error(e)
  end
end
