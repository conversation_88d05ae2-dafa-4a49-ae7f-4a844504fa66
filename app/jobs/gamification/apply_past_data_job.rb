class Gamification::ApplyPastDataJob < ApplicationJob
  queue_as :default

  def perform(gamification_setting_id)
    setting = Gamification::Setting.find(gamification_setting_id)
    tenant = setting.tenant
    return unless tenant.gamification_enabled?

    students = tenant.students

    students.find_each do |student|
      apply_past_badges_for_student(student, setting) if setting.badges_enabled?
    end

  rescue StandardError => e
    Rollbar.error(e)
  end

  private

  def apply_past_badges_for_student(student, setting)
    badge_action_types = [
      'course_completed',
      'exam_passed', 
      'comment_on_lesson',
      'post_on_community',
      'certificate_earned',
      'logged_10_consecutive_day',
      'course_reviewed',
      'received_reaction',
      'comment_on_community'
    ]

    badge_action_types.each do |action_type|
      Gamification::BadgeUnlocker.call(user: student, action_type: action_type)
    end
  end
end
