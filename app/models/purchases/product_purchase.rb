class Purchases::ProductPurchase < ApplicationRecord
  attr_reader :provider_response
  attr_accessor :already_verified, :verification_status

  belongs_to_tenant :tenant
  # Licenses belongs to super admins. Courses and packs belongs to students.
  belongs_to :user
  belongs_to :price, optional: true
  belongs_to :coupon, optional: true
  belongs_to :product, polymorphic: true, optional: true
  belongs_to :payment_method, optional: true

  has_many :student_course_accesses, class_name: "Subscriptions::StudentCourseAccess", dependent: :destroy
  has_many :student_commuity_accesses, class_name: "Subscriptions::StudentCommunityAccess", dependent: :destroy
  has_many :course_subscriptions, through: :student_course_accesses, source: :subscription, class_name: "Subscriptions::Subscription"
  has_many :community_subscriptions, -> { unscope(where: :course_id).where(course_id: nil) }, through: :student_commuity_accesses, source: :subscription, class_name: "Subscriptions::Subscription"

  has_many :affiliate_purchases, class_name: "Affiliates::AffiliatePurchase", foreign_key: :purchase_id
  has_many :affiliate_commissions, class_name: "Affiliates::AffiliateCommission", through: :affiliate_purchases
  has_many :purchase_affiliates, class_name: "Affiliate", through: :affiliate_purchases, source: :affiliate

  has_one :refund_request
  before_update :update_coupon, if: :status_changed?
  after_commit :update_tenant_sales, if: :approved_and_valid_product?, on: [:create, :update]
  after_commit :sent_subscription_email, if: :new_subscription_product?, on: [:create, :update]

  monetize :amount_cents

  enum status: {
    initiated: 'initiated',
    in_progress: 'in_progress',
    approved: 'approved',
    rejected: 'rejected',
    failed: 'failed',
    errored: 'errored',
    timed_out: 'timed_out',
    refunded: 'refunded'
  }

  enum provider: {
    mercado_pago: 'mercado_pago',
    pay_pal: "pay_pal",
    stripe: "stripe",
    stripe_connect: "stripe_connect",
    payu: "payu",
    link: "link",
    custom: "custom"
  }

  PROVIDER_VALUE = {
    mercado_pago: 'Mercado Pago',
    pay_pal: "Paypal",
    stripe: "Stripe",
    payu: "Payu",
  }

  def subscriptions
    Subscriptions::Subscription.unscope(where: :course_id).where(id: course_subscriptions.ids + community_subscriptions.ids)
  end

  def coupon_code=(code)
    self.coupon = Purchases::Coupon.find_by!(code:)
  end


  def update_coupon
    if (self.status == 'approved') && !(self.coupon_id.nil?)
      coupon.update_quantity
    end
  end

  def provider_handler
    "sabiorealm/transaction_providers/#{provider}".classify.constantize
  end

  def verifyiable?
    !(initiated? || in_progress?)
  end

  def initiate
    self.status = :initiated

    self.provider = payment_method.payment_method_type
    self.product = price.product
    self.quantity = 1 if self.quantity.nil?

    self.amount =
      # if coupon&.discount_percent&.eql? 100
      #   price.real_price * 0
      if coupon.instance_of? Purchases::CouponPriceDiscount
        raise Sabiorealm::Error, "Invalid coupon for this product" unless coupon.products.include? product    
        coupon.price_coupon(price.real_price)*self.quantity
      elsif coupon.instance_of? Purchases::CouponTrialDays
        raise Sabiorealm::Error, "Invalid coupon for this product" unless coupon.products.include? product    
        raise Sabiorealm::Error, "Only available for subscriptions with Stripe" unless  price.subscription && 
                                                                                        (['stripe', 'stripe_connect'].include?(provider))
        price.real_price * 0
      elsif price.trial_period_days.present?
        price.real_price * 0
      else 
        price.real_price * self.quantity
      end
    
    # raise Sabiorealm::Error, "Price can't be 0" if (amount.amount == 0 && coupon.nil?)
    # Validaciones si es una licencia
    if ::Marketplaces::License === product
      raise GraphQL::ExecutionError.new "Can't create license for the courses under same domain." if user.tenant == tenant
      raise GraphQL::ExecutionError.new "Course is not added to the marketplace." unless product.course.is_available_on_marketplace
    end

    # aqui usan el cupon del 100%
    # if amount.amount == 0
    #   # aqui creo el nombre para el provider_id
    #   self.provider_id = tenant.subdomain + product.name + product_id.to_s
    #   approve!
    #   coupon.update_quantity
    # else
    #   @provider_response = provider_handler.initiate(self)
    # end

    @provider_response = provider_handler.initiate(self)
    save!
  rescue => e
      self.status = :failed
      @provider_response = e.message
      Rails.logger.error "Error: #{e}"
      Rollbar.error(e)

      MultiTenantSupport.under_tenant tenant do
        UserMessagesManager.send_message_to_user(user, :student_payment_failed, 
                                                 template_variables: { product_name: product.name, 
                                                                       provider: PROVIDER_VALUE[provider.to_sym], payment_type: })
        ::Triggers::Trigger.apply_triggers('student_payment_failed', user)
      end
      false
    # Don't save, just return it and let it begone
    # We don't rescue standarderror because we want it to bubble up
  end

  def verify!(verify_data = nil)
    provider_data['verify_data'] = verify_data if verify_data.present?
    @provider_response = provider_handler.verify(self)
    case @verification_status
    when :approved
      approve!
    when :rejected
      reject!
    else
      save!
    end
  end

  def approve!
    self.status = :approved
    transaction do
      case product_type
      when "Marketplaces::License"
        product.add_license!(product_purchase: self)
      else
        product.add_student!(user, :purchase, user, product_purchase: self)
      end
      save!
    end

    super_admin = nil
    without_tenant_protection do
      super_admin = tenant.super_admin
    end
    MultiTenantSupport.under_tenant tenant do
        UserMessagesManager.send_message_to_user(super_admin, :admin_new_purchase, template_variables: { product_name: product.name },
                                            extras: { avatar_data: user.avatar_url, user_id: user.id, product_id: product.id })
      ::Triggers::Trigger.apply_triggers('admin_new_purchase', user)
    end
  rescue StandardError => e
    error!(e)
  end

  def process_refund
    stripe_connect_subscription_id = subscriptions.last.stripe_connect_subscription_id
    stripe_subscription = ::Stripe::Subscription.retrieve(stripe_connect_subscription_id)
    latest_invoice = ::Stripe::Invoice.retrieve(stripe_subscription.latest_invoice)
    charge_id = latest_invoice.charge

    if charge_id.present?
      transfer_id = Stripe::Charge.retrieve(charge_id).transfer
      if refund_request&.stripe_refund_id.present?
        refund = ::Stripe::Refund.retrieve(refund_request.stripe_refund_id)
      else
        refund = ::Stripe::Refund.create({
          charge: charge_id,
          amount: amount_cents - transaction_stripe_fee(charge_id),
          reason: 'requested_by_customer',
        })

        refund_request.update_column(:stripe_refund_id, refund.id)
      end

      if transfer_id.present?
        unless refund_request&.transfer_reversal_id.present?
          transfer = Stripe::Transfer.retrieve(transfer_id)
          transfer_reversal = transfer.reversals.create({
            amount: transfer.amount,
          })
          refund_request.update_column(:transfer_reversal_id, transfer_reversal.id)
        end
      end
    else
      # No charge was made (likely a trial or 100% off coupon)
      refund =  OpenStruct.new(status: 'succeeded')
    end

    reverse_affiliate_commissions

    # Only delete the subscription if it's not already canceled
    if stripe_subscription.status != 'canceled'
      ::Stripe::Subscription.delete(stripe_connect_subscription_id)
    end
    refund
  end
  
  def reverse_affiliate_commissions
    return unless product_type == 'Courses::Course'
  
    affiliate_commissions.each do |commission|
      commission.refund_transfer
    end
  end

  def distribute_commision_to_affilate
    return unless product_type == 'Courses::Course' && product.is_available_on_affiliate

    return unless ['subscription', 'single'].include?(payment_type)

    platform_account = ::Stripe::Account.retrieve
    source_account = tenant.payment_methods.find_by(type: 'Purchases::PaymentMethodStripeConnect')
    net_amount_cents = amount_cents

    payment_intent = ::Stripe::PaymentIntent.retrieve(provider_id)
    charge_id = payment_intent.latest_charge

    if charge_id.present?
      charge = ::Stripe::Charge.retrieve(charge_id)
      sale_amount = charge.amount
      stripe_fee = ::Stripe::BalanceTransaction.retrieve(charge.balance_transaction).fee
      net_amount_cents = sale_amount - stripe_fee
    end

    attribution_model = product.affiliate_setting.attribution_model
    affiliates = case attribution_model
                  when "first_click"
                    [product.affiliates_links.where(student_id: user_id).order(:created_at).first.affiliate]
                  when "last_click"
                    [product.affiliates_links.where(student_id: user_id).order(:created_at).last.affiliate]
                  when "linear"
                    Affiliate.where(id: product.affiliates_links.where(student_id: user_id).pluck(:affiliate_id))
                  else
                    []
                  end

    affiliates.each do |affiliate|
      affiliate_account_id = affiliate.payment_methods.find_by(type: 'Affiliates::PaymentMethodStripeConnect')&.account_id
      next unless affiliate_account_id.present?

      commission_percentage = product.affiliate_setting.commission_percentage.to_f
      commission_amount = (net_amount_cents * commission_percentage / 100.0).to_i
      transfer_amount = commission_amount / affiliates.count

      transfer = Stripe::Transfer.create({
        amount: transfer_amount,
        currency: "usd",
        destination: affiliate_account_id,
      })

      affiliate_purchase = affiliate.affiliate_purchases.create(purchase_id: self.id)
      affiliate_purchase.affiliate_commissions.create(amount_cents: net_amount_cents, commission_cents: transfer_amount, stripe_transfer_id: transfer.id)

      NotificationsManager.push_notification([affiliate.guid], { type: "AFFILIATE_COMMISSION", message: "You received commision of #{transfer_amount} USD for a new sale #{product.name} at #{Date.today}", extras: { purchase_id: id, affiliate_id: affiliate.id, course_name: product.name } })
      PushNotificationsManager.send_notification({headings: 'New Course Purchased', contents: "#{user.full_name} just purchased your course!", player_ids: affiliate.notification_players.web_players.map(&:player_id) })
    end
  end

  # only to get stripe transaction fee
  def transaction_stripe_fee(charge_id)
    charge = ::Stripe::Charge.retrieve(charge_id)
    sale_amount = charge.amount
    ::Stripe::BalanceTransaction.retrieve(charge.balance_transaction).fee
  end

  def approved?
    self.status == 'approved'
  end

  def rejected?
    self.status == 'rejected'
  end

  def in_progress?
    self.status == 'in_progress'
  end

  def reject!
    self.status = :rejected
    MultiTenantSupport.under_tenant tenant do
      UserMessagesManager.send_message_to_user(user, :student_payment_failed, 
                                               template_variables: { product_name: product.name, 
                                                                     provider: PROVIDER_VALUE[provider.to_sym], payment_type: })
      ::Triggers::Trigger.apply_triggers('student_payment_failed', user)
    end
    save! 
  end

  def expired!
    self.status = :timed_out
    save!
  end

  def in_progress! 
    unless self.in_progress?
      self.status = :in_progress
      save!
    end
  end

  def error!(error)
    self.status = :errored
    pp error
    pp self
    save!

    raise error
    # TODO: ROllbar
  end

  def update_tenant_sales
    converted_amount = amount.exchange_to(:usd).to_f
    tenant&.tenant_metric&.update!(revenue_usd: tenant.tenant_metric&.revenue_usd + converted_amount)
  rescue => e
  end

  def approved_and_valid_product?
    saved_change_to_status? && approved? && product_type != "Marketplaces::License"
  end

  def new_subscription_product?
    saved_change_to_status? && approved? && product_type != "Marketplaces::License" && payment_type == "subscription"
  end

  def sent_subscription_email
    UserMessagesManager.send_message_to_user(user, :new_subscription_product, template_variables: { product_name: product.name })
  end

  def handle_payment_completed
    UserMessagesManager.send_message_to_user(user, :purchase_payment_completed, template_variables: { product_name: product.name })  if product_type != "Marketplaces::License"
  end

  class << self
    def find_by_provider_id(provider, provider_id)
      provider_handler(provider).find(provider_id)
    end

    def provider_handler(provider)
      "sabiorealm/transaction_providers/#{provider}".classify.constantize
    end
  end

end
