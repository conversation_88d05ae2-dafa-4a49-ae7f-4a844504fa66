# frozen_string_literal: true

class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :trackable, :validatable,
         :omniauthable, omniauth_providers: %i[google_oauth2 facebook]

  include DeviseTokenAuth::Concerns::User
  include ImageUploader::Attachment.new(:avatar, store: :public_store)

  belongs_to_tenant :tenant, optional: true # Tenant is optional for affiliates

  after_create :send_new_account_email, unless: :affiliate?

  has_many :created_courses, -> { unscope(where: :tenant_id) }, dependent: :nullify, class_name: "Courses::Course", foreign_key: :created_by_id
  has_many :created_packs, -> { unscope(where: :tenant_id) }, dependent: :nullify, class_name: "Courses::Pack", foreign_key: :created_by_id

  has_many :course_teachers, class_name: "Courses::CourseTeacher", dependent: :destroy
  has_many :dictated_courses, through: :course_teachers, class_name: "Courses::Course", source: :course

  has_many :community_teachers, class_name: "Communities::CommunityTeacher", dependent: :destroy
  has_many :dictated_communities, through: :community_teachers, class_name: "Communities::Community", source: :community
  has_many :dictated_community_posts, through: :dictated_communities, class_name: "Communities::CommunityPost", source: :community_post

  has_many :social_comments, dependent: :destroy, class_name: "Social::Comment", foreign_key: :author_id
  has_many :social_reactions, dependent: :destroy, class_name: "Social::Reaction", foreign_key: :author_id
  has_many :created_student_course_accesses, dependent: :destroy, class_name: "Subscriptions::StudentCourseAccess", foreign_key: :creator_id

  has_many :product_purchases, -> { unscope(where: :tenant_id) }, class_name: "Purchases::ProductPurchase", dependent: :destroy
  has_many :exam_answers, class_name: "Subscriptions::ExamAnswer", foreign_key: :scored_by_id
  has_many :group_instructors, class_name: 'Groups::GroupInstructor', foreign_key: :instructor_id
  has_many :group_students, class_name: 'Groups::GroupStudent', foreign_key: :student_id
  has_many :user_activities, dependent: :destroy
  has_many :users_tenants, dependent: :destroy, foreign_key: :user_id
  has_many :tenants, through: :users_tenants
  has_many :user_sessions, dependent: :destroy

  has_one :gamification_user_profile, dependent: :destroy, class_name: 'Gamification::UserProfile'
  has_many :earned_badges, dependent: :destroy, class_name: 'Gamification::UserBadge'
  has_many :gamification_badges, through: :earned_badges, source: :badge_setting
  has_many :gamification_rewards, dependent: :destroy, class_name: 'Gamification::UserReward'
  has_many :gamification_points_activities, dependent: :destroy, class_name: 'Gamification::UserPointsActivity'
  has_many :social_networks, dependent: :destroy, class_name: 'UserSocialNetwork'
  accepts_nested_attributes_for :social_networks, allow_destroy: true

  belongs_to :affiliate, optional: true

  before_validation :set_password, on: :create

  has_and_belongs_to_many :tags
  attr_accessor :current_academy
  attr_accessor :ip_address
  attr_accessor :email_changed

  around_update :set_current_academy
  after_update :sync_affiliate_data, if: :basic_info_changed?
  after_commit :trigger_hubspot_sync, on: %i[create destroy]

  def basic_info_changed?
    saved_change_to_email? || saved_change_to_encrypted_password? || saved_change_to_first_name? || saved_change_to_last_name? || saved_change_to_bio?
  end

  def sync_affiliate_data
    without_tenant_protection do
      return unless affiliate_user.present? && affiliate.present?

      affiliate.update_columns(
        email: self.email,
        encrypted_password: self.encrypted_password,
        first_name: self.first_name,
        last_name: self.last_name,
        bio: self.bio
      )
    end
  end

  def set_current_academy
    yield
    if current_academy.present? && MultiTenantSupport::Current.tenant_account.nil?
      MultiTenantSupport::Current.tenant_account = current_academy
    end
  end

  def self.from_omniauth(provider_data, type)
    data = provider_data.info
    where(email: data[:email]).first_or_create do |user|
      user.provider = provider_data[:provider]
      user.uid = provider_data[:uid]
      user.email = data[:email]
      user.type = type
      user.default_avatar_url = data[:image]
      user.first_name = data[:first_name]
      user.last_name = data[:last_name]
      user.password = Devise.friendly_token[0, 20]
    end
  end
  
  def set_password
    self.password ||= Devise.friendly_token
  end

  def affiliate?
    type == 'Affiliate'
  end

  # TODO: Country, timezone and language validations
  before_validation :set_locale, on: :create
  before_validation :set_theme, on: :create
  validates :type, presence: true, on: :create

  validate :restrict_user_limits, on: :create, unless: -> { type == 'Affiliate' }
  validate :type_not_changed, on: :update

  def type_not_changed
    if type_changed? && persisted?
      errors.add(:type, "No se puede cambiar el tipo de usuario después de creado")
    end
  end

  scope :search_by_string, -> (search_string) {
    where(
      "first_name ilike :search_string or last_name ilike :search_string or email ilike :search_string",
      search_string: "%#{search_string}%"
    ) unless search_string.nil?
  }

  def full_name
    "#{first_name} #{last_name}"
  end

  def avatar_url
    avatar&.url || default_avatar_url || "#{tenant&.academy_url}/img/avatars/pr#{(id % 6) + 1}.webp"
  end

  def set_locale
    self.country = tenant.country unless country.present?
    self.language = tenant.language unless language.present?
    self.timezone = tenant.timezone unless timezone.present?
  end

  def set_theme
    self.dark_mode = tenant.dark_mode
  end

  def restrict_user_limits
    return if instance_of? SuperAdmin # These are created exactly once
    plan_restrictions = tenant.feature_plan.user_limits
    current_type_limit = plan_restrictions[type.downcase.pluralize]

    if current_type_limit.present?
      current_type_count = tenant.users.where(type: type).count
      if current_type_limit <= current_type_count
        errors.add :base, "The #{type} user limit of #{current_type_limit} has been reached"
        return
      end
    end
    total = plan_restrictions["total"]
    return if total.nil?
    users_count = tenant.users_count
    if total < users_count
      errors.add :base, "The total user limit of #{total} has been reached"
    end
  end

  def notifications
    NotificationsManager.get_notifications(guid)
  end

  def read_notifications!
    pp guid
    NotificationsManager.read_notifications(guid)
  end

  def send_new_account_email
    # UserMailer.with(user: self, password: password).new_user.deliver_later
    automation = Emails::Automation.find_by(when: 'new_user')
    if automation&.email&.status.eql? 'active'
      postmark_template = 'new_user'
      postmark_template = automation.email.template.alias if automation&.email&.template
        
      UserMailer.with(user: self, password: password, automation: automation).public_send(postmark_template).deliver_later
    end
  end

  def password_reset!(new_password = nil, send_email: true)
    self.password = new_password || Devise.friendly_token
    save!
    # UserMailer.with(user: self, password: password).password_recover.deliver_now if send_email
    automation = Emails::Automation.find_by(when: 'password_recover')
    if automation&.email&.status.eql? 'active'
      postmark_template = 'password_recover'
      postmark_template = automation.email.template.alias if automation&.email&.template
        
      UserMailer.with(user: self, password: password, automation: automation).public_send(postmark_template).deliver_now if send_email
    end

    ::Triggers::Trigger.apply_triggers('password_recover', self) if self.class == Student
  end

  # CubeJS data

  def cube_js_token
    # For Affiliate use safe navigation for tenant.
    if cube_js_token_expiration_date.nil? || cube_js_token_expiration_date < Time.zone.now
      new_expiration_date = 7.days.from_now
      encoded_object = {
        user_type: type,
        user_id: id,
        tenant_id: tenant&.id,
        tenant_subdomain: tenant&.subdomain,
        exp: new_expiration_date.to_i
      }
      self.cube_js_token_jwt = JWT.encode(encoded_object, Rails.application.credentials.cube_js.api_secret, 'HS256')
      self.cube_js_token_expiration_date = new_expiration_date
      without_tenant_protection do
        save!
      end
    end
    cube_js_token_jwt
  end

  def hubspot_identification_token
    nil
  end

  def exceeds_session_limit?
    user_sessions.count > tenant.max_login_sessions || tokens.length > tenant.max_login_sessions
  end

  def remove_old_session
    max_sessions = tenant.max_login_sessions
  
    if user_sessions.count > max_sessions
      excess_sessions = user_sessions.order(:created_at).limit(user_sessions.count - max_sessions)
      excess_sessions&.destroy_all
    end
  
    if tokens.size > max_sessions
      sorted_tokens = tokens.sort_by { |_key, value| value["created_at"] }
      self.tokens = sorted_tokens.last(max_sessions).to_h
      self.save!
    end
  end

  def send_verification_email
    verification_code = rand(100000..999999).to_s
    self.update!(login_verification_code: verification_code)
    VerificationMailer.login_verification_email(self.id, verification_code).deliver_later
  end

  # Subscribe user after creation
  def enqueue_mailchimp_subscription
    MailchimpJob.perform_later("subscribe", email, tenant_id, user_mailchimp_data) if tenant&.mailchimp_configured?
  end

  # Update user in Mailchimp when important fields change
  def enqueue_mailchimp_update
    MailchimpJob.perform_later("update", email, tenant_id, user_mailchimp_data) if mailchimp_id.present?
  end

  # Delete user from Mailchimp before destroying record
  def enqueue_mailchimp_deletion
    MailchimpJob.perform_later("delete", email, tenant_id, mailchimp_id:) if mailchimp_id.present?
  end

  # Check if any relevant Mailchimp field has changed
  def mailchimp_fields_changed?
    saved_change_to_email? || saved_change_to_first_name? || saved_change_to_last_name? || saved_change_to_phone?
  end

  # User data to be sent to Mailchimp
  def user_mailchimp_data
    {
      id: s.id,
      email: s.email,
      fname: s.first_name.to_s,
      lname: s.last_name.to_s,
      phone: s.phone.to_s,
      company: s.tenant&.school_name.to_s,
      new_email: s.saved_change_to_email? ? email : "",
      mailchimp_id: s.mailchimp_id,
      type: "User"
    }
  end

  def trigger_hubspot_sync
    return if type == 'SuperAdmin' || type === 'Affiliate' || MultiTenantSupport::Current.tenant_account.nil?

    Hs::SyncContactJob.perform_later(tenant&.super_admin&.id) if tenant_id.present?
  rescue => e
  end
end
