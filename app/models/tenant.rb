class Tenant < ApplicationRecord
  encrypts :mercado_pago_secret_access_token
  encrypts :paypal_client_secret
  encrypts :stripe_api_key

  belongs_to :feature_plan
  has_many :websites, dependent: :destroy, autosave: true
  has_many :website_buttons, dependent: :destroy, class_name: "Websites::Style"
  has_many :signatures, dependent: :destroy, class_name: "Emails::Signature"
  has_many :website_footer_columns, dependent: :destroy, class_name: "WebsiteFooterColumn"
  # has_one :super_admin, dependent: :destroy

  has_many :users, dependent: :destroy
  has_many :admins, dependent: :destroy
  has_many :instructors, dependent: :destroy
  has_many :students, dependent: :destroy

  has_many :categories, dependent: :destroy, class_name: "Courses::Category"
  has_many :courses, -> { order(position: :asc) }, dependent: :destroy, class_name: "Courses::Course"
  has_many :communities, -> { order(position: :asc) }, dependent: :destroy, class_name: "Communities::Community"
  has_many :community_posts, through: :communities, dependent: :destroy, class_name: "Communities::CommunityPost"
  has_many :lessons, dependent: :destroy, class_name: "Courses::Lesson"
  has_many :packs, dependent: :destroy, class_name: "Courses::Pack"
  has_many :admin_roles, dependent: :destroy
  has_many :subscriptions, dependent: :destroy, class_name: "Subscriptions::Subscription"
  has_many :community_subscriptions, -> { unscope(where: :course_id).enabled.where(course_id: nil) }, class_name: "Subscriptions::Subscription", dependent: :destroy
  has_many :progresses, dependent: :destroy, class_name: "Subscriptions::Progress"
  has_many :exam_attempts, dependent: :destroy, class_name: "Subscriptions::ExamAttempt"
  has_many :surveys, dependent: :destroy, class_name: "Courses::LessonSurvey"
  has_many :survey_responses, dependent: :destroy, class_name: "Subscriptions::SurveyResponse"

  has_many :groups, dependent: :destroy, class_name: "Groups::Group"

  has_many :transactions, dependent: :destroy, class_name: "Purchases::ProductPurchase"
  has_many :payment_methods, class_name: "Purchases::PaymentMethod"

  has_many :licenses, dependent: :destroy, class_name: "Marketplaces::License"
  has_many :audit_versions, dependent: :destroy
  has_many :media_lesson_contents, dependent: :destroy, class_name: '::Media::LessonContent'
  has_many :email_leads_form_sections, dependent: :destroy, class_name: '::Websites::BodySectionEmailLeadsForm'
  has_many :purchase_coupons, dependent: :destroy, class_name: 'Purchases::Coupon'
  has_many :invoices, dependent: :destroy, class_name: 'Invoices::Invoice'
  has_many :notices, dependent: :destroy
  has_many :my_modules, -> { order(position: :asc) }, dependent: :destroy, class_name: 'Courses::MyModule'
  has_many :group_courses, dependent: :destroy, class_name: 'Groups::GroupCourse'
  has_many :group_instructors, dependent: :destroy, class_name: 'Groups::GroupInstructor', foreign_key: :instructor_id
  has_many :group_packs, dependent: :destroy, class_name: 'Groups::GroupPack'
  has_many :group_students, dependent: :destroy, class_name: 'Groups::GroupStudent'
  has_many :network_rest_webhooks, dependent: :destroy, class_name: 'Network::RestWebhook'
  has_many :coupon_products, dependent: :destroy, class_name: 'Purchases::CouponProduct'
  has_many :body_sections, dependent: :destroy, class_name: 'Websites::BodySection'
  has_many :lead_form_contacts, dependent: :destroy, class_name: 'Websites::LeadsFormContact'
  has_many :custom_domains, dependent: :destroy, class_name: 'CustomDomain'
  has_many :triggers, dependent: :destroy, class_name: 'Triggers::Trigger'
  has_many :tags, dependent: :destroy
  has_many :reports, dependent: :destroy
  has_many :report_tabs, dependent: :destroy
  has_many :user_activities, dependent: :destroy
  has_many :affiliate_invitations, dependent: :destroy

  has_many :users_tenants, dependent: :destroy
  has_one :super_admin_relationship, -> { joins(:user).where(users: { type: 'SuperAdmin' }) }, class_name: 'UsersTenant'
  has_one :super_admin, through: :super_admin_relationship, source: :user

  has_many :affiliates_courses, through: :courses, source: :affiliates_courses, dependent: :destroy
  has_many :affiliates, -> { joins(:affiliates_courses).where( affiliates_courses: { status: :approved }) }, through: :affiliates_courses, source: :affiliate
  has_many :affiliates_tenants, dependent: :destroy
  has_one :gamification_setting, dependent: :destroy, class_name: 'Gamification::Setting'
  has_one :tenant_metric, dependent: :destroy
  has_many :emails_broadcasts, dependent: :destroy, class_name: 'Emails::Broadcast'
  has_many :user_filters, dependent: :destroy
  has_many :email_activities, dependent: :destroy, class_name: 'Emails::Activity'
  scope :trials, -> { where(is_trial: true) }
  scope :expired_trials, -> { trials.where('created_at < ?', 180.days.ago) }

  validates :subdomain, presence: true, format: { with: /\A[A-Za-z0-9](?:[A-Za-z0-9\-]{0,61}[A-Za-z0-9])?\z/ }, exclusion: { in: %w(www api dashboard admin centralized onboarding affiiates) }, uniqueness: true
  validates :domain, format: { with: URI::DEFAULT_PARSER.make_regexp(['https']), message: "Solo se permiten dominios https" }, if: -> { domain.present? }

  validate :valid_pay_pal_credentials, :valid_mercado_pago_credentials, :valid_plan_status

  before_validation :set_defaults, on: :create

  validate :validate_feature_plan, if: :video_transcoding_changed?

  after_destroy :destroy_academy_storage
  before_destroy :destroy_bunny_library

  after_create :set_bunny_cdn_video_library
  after_commit :create_default_metrics, on: :create

  after_update :check_feature_plan_change
  after_commit :update_customer_status, :update_subscription_type, if: :saved_change_to_is_trial?

  after_commit :import_students_to_mailchimp, if: :mailchimp_settings_changed?
  after_commit :sync_hubspot_contact_job, on: :update, if: :hubspot_sync_needed?
  after_update :sync_hubspot_contact_job, if: :logo_updated?
  after_commit :create_default_gamification_setting, if: :should_create_gamification_setting?

  include ImageUploader::Attachment.new(:logo_light, store: :public_store)
  include ImageUploader::Attachment.new(:logo_dark, store: :public_store)
  include ImageUploader::Attachment.new(:favicon, store: :public_store)
  include ImageUploader::Attachment.new(:email_logo, store: :public_store)

  attr_accessor :ip_address, :utm_gl_data, :apply_to_past_data

  def should_create_gamification_setting?
    saved_change_to_gamification_enabled? && gamification_enabled?
  end

  def hubspot_sync_needed?
    saved_changes.keys & %w[subdomain contact_number courses_count feature_plan_id feature_plan_expiration_date is_trial bunny_storage_used_mib private_storage_used_mib public_storage_used_mib] != []
  end

  def logo_updated?
    saved_changes.keys & %w[logo_light_data logo_dark_data] != []
  end

  def pages_count_without_home
    websites.where.not(title: 'Home').count
  end

  def sync_hubspot_contact_job
    Hs::SyncContactJob.perform_later(super_admin&.id)
  end

  def create_default_gamification_setting
    if gamification_setting.present?
      gamification_setting.create_default_settings
    else
      create_gamification_setting(points_enabled: true, badges_enabled: true, apply_to_past_data: apply_to_past_data)
    end
  end

  def check_feature_plan_change
    if saved_change_to_feature_plan_id? && feature_plan.feature_plan == 'basic'
      groups.destroy_all
      courses.where(has_certificate: true).update_all(has_certificate: false)
    end
  end

  def mailchimp_settings_changed?
    saved_change_to_mailchimp_api_key? || saved_change_to_mailchimp_list_id?
  end

  def import_students_to_mailchimp
    MailchimpJob.perform_later("import_all", "", id)
  end

  def mailchimp_configured?
    mailchimp_api_key.present? && mailchimp_list_id.present?
  end

  # Validators
  def valid_mercado_pago_credentials
    return if mercado_pago_public_key.nil? && mercado_pago_secret_access_token.nil?
    errors.add(:mercado_pago_public_key, "Credenciales de mercadopago inválidas") unless mercado_pago_public_key.present? && mercado_pago_secret_access_token.present?
  end

  def valid_pay_pal_credentials
    return if pay_pal_client_id.nil? && pay_pal_client_secret.nil?
    errors.add(:mercado_pago_public_key, "Credenciales de mercadopago inválidas") unless pay_pal_client_id.present? && pay_pal_client_secret.present?
  end

  def valid_plan_status
    errors.add(:is_trial, "Un plan gratis no puede ser trial") if is_trial && feature_plan_id == "free"
    errors.add(:feature_plan_expiration_date, "Un plan gratis no puede tener una fecha de expiración") if feature_plan_id == "free" && feature_plan_expiration_date.present?
    errors.add(:feature_plan_expiration_date, "Un plan pago debe tener una fecha de expiración") if feature_plan_id != "free" && feature_plan_expiration_date.nil?
  end

  def validate_feature_plan
    unless self.feature_plan.video_transcoding
      errors.add(:video_transcoding, "Este plan no soporta renderizado de video")
    end
  end

  # Calculated columns

  def academy_url
    domain || "https://#{subdomain}.#{Rails.application.config.root_domain}"
  end

  def users_count
    students_count + admins_count + instructors_count
  end

  def contacts_count
    lead_form_contacts.count
  end

  def default_signature
    "#{subdomain.capitalize()} <#{subdomain}@sabionet.com>"
  end

  def create_models
    website = websites.create(title: 'Home', url: '/', state: :published)
    btn_primary = Websites::Style.create(style: 'primary')
    btn_secondary = Websites::Style.create(style: 'secondary')
    lang = website_lang(language)
    Websites::BodySectionBanner.create!(
      body_section: Websites::BodySection.new(website:),
      header: lang[:banner_title],
      header_active: true,
      header_size: "36px",
      header_color: "#2D3954",
      subheader_color: "#2D3954",
      subheader_active: true,
      subheader_size: "21px",
      subheader: lang[:banner_subheader],
      website_button_attributes: {
        align: "center",
        button_styles_attributes: [
          { style_id: btn_primary.id, active:true },
          { style_id: btn_secondary.id },
          { style_attributes: {style: 'custom'}},
        ]
      }
    )

    Websites::BodySectionTextMedia.create!(
      body_section: Websites::BodySection.new(website:),
      header: lang[:section_header],
      header_active: true,
      header_size: "36px",
      line_height_header: "32px",
      line_height_subheader: "32px",
      subheader_active: true,
      subheader_size: "21px",
      header_color: "#2D3954",
      subheader_color: "#2D3954",
      subheader: lang[:section_subheader],
      description: lang[:section_description],
      description_active: true,
      media_align: "right",
      website_button_attributes: {
        active: false,
        align: "center",
        button_styles_attributes: [
          { style_id: btn_primary.id, active:true },
          { style_id: btn_secondary.id },
          { style_attributes: {style: 'custom'}},
        ]
      }
    )
    Websites::BodySectionTextMedia.create!(
      body_section: Websites::BodySection.new(website:),
      header: lang[:section2_header],
      header_active: true,
      header_size: "36px",
      line_height_header: "32px",
      line_height_subheader: "32px",
      subheader_active: true,
      header_color: "#2D3954",
      subheader_color: "#2D3954",
      subheader_size: "21px",
      subheader: lang[:section2_subheader],
      media_align: "left",
      description_active: true,
      description: lang[:section2_description],
      website_button_attributes: {
        active: false,
        align: "center",
        button_styles_attributes: [
          { style_id: btn_primary.id, active:true },
          { style_id: btn_secondary.id },
          { style_attributes: {style: 'custom'}},
        ]
      }
    )
    Websites::BodySectionTextMedia.create!(
      body_section: Websites::BodySection.new(website:),
      header: lang['section3_header'],
      header_active: true,
      header_size: "36px",
      line_height_header: "32px",
      line_height_subheader: "32px",
      subheader_active: true,
      subheader_size: "21px",
      header_color: "#2D3954",
      subheader_color: "#2D3954",
      subheader: lang[:section3_subheader],
      media_align: "right",
      description_active: true,
      description: lang[:section3_description],
      website_button_attributes: {
        active: false,
        align: "center",
        button_styles_attributes: [
          { style_id: btn_primary.id, active:true },
          { style_id: btn_secondary.id },
          { style_attributes: {style: 'custom'}},
        ]
      }
    )
    Websites::BodySectionTestimonial.create!(
      body_section: Websites::BodySection.new(website:),
      header: lang[:testimonial],
      header_active: true,
      header_size: "36px",
      subheader_active: true,
      subheader_size: "16px",
      header_color: "#2D3954",
      subheader_color: "#2D3954",
      subheader: lang[:testimonial_subheader],
    )

    # automation = Emails::Automation.find_by(when: 'user_added_to_group')
    # if automation.nil?
    #   Emails::Automation.create(when: 'user_added_to_group', variables: {
    #     'school name': '@school_name',
    #     'user name': '@user_name',
    #     'user email': '@user_email',
    #     'group name': '@group_name',
    #   })
    # end

    automation2 = Emails::Automation.find_by(when: 'certificate_generated')
    if automation2.nil?
      Emails::Automation.create(when: 'certificate_generated',
        variables: {
          'school name': '@school_name',
          'user name': '@user_name',
          'course name': '@course_name'
        }
      )
    end

    automation3 = Emails::Automation.find_by(when: 'student_payment_failed')
    if automation3.nil?
      Emails::Automation.create(when: 'student_payment_failed',
        variables: {
          'school name': '@school_name',
          'user name': '@user_name',
          'product name': '@product_name',
          'provider': '@provider',
          'payment type':'@payment_type'
        }
      )
    end

    automation4 = Emails::Automation.find_by(when: 'student_passed_exam')
    if automation4.nil?
      Emails::Automation.create(when: 'student_passed_exam',
      variables: {
        'school name': '@school_name',
        'user name': '@user_name',
        'course name': '@course_name',
        'exam name': '@exam_name'
      })
    end

    automation5 = Emails::Automation.find_by(when: 'student_failed_exam')
    if automation5.nil?
      Emails::Automation.create(when: 'student_failed_exam', variables: {
        'school name': '@school_name',
        'user name': '@user_name',
        'course name': '@course_name',
        'exam name': '@exam_name'
      })
    end

    automation6 = Emails::Automation.find_by(when: 'user_not_logged_7_days')
    if automation6.nil?
      Emails::Automation.create(when: 'user_not_logged_7_days', variables: {
        'school name': '@school_name',
        'user name': '@user_name',
        'user email': '@user_email'
      })
    end

    automation7 = Emails::Automation.find_by(when: 'user_not_logged_14_days')
    if automation7.nil?
      Emails::Automation.create(when: 'user_not_logged_14_days', variables: {
        'school name': '@school_name',
        'user name': '@user_name',
        'user email': '@user_email'
      })
    end

    automation8 = Emails::Automation.find_by(when: 'user_not_logged_30_days')
    if automation8.nil?
      Emails::Automation.create(when: 'user_not_logged_30_days', variables: {
        'school name': '@school_name',
        'user name': '@user_name',
        'user email': '@user_email'
      })
    end

    # automation9 = Emails::Automation.find_by(when: 'user_finished_module')
    # if automation9.nil?
    #   Emails::Automation.create(when: 'user_finished_module', variables: {
    #     'school name': '@school_name',
    #     'user name': '@user_name',
    #     'user email': '@user_email',
    #     'course name': '@course_name',
    #     'module name': '@module_name',
    #   })
    # end
    tag_automation = Emails::Automation.find_by(when: 'added_tag')
    if tag_automation.nil?
      Emails::Automation.create(when: 'added_tag', variables: {
        'school name': '@school_name',
        'user name': '@user_name',
        'user email': '@user_email',
      })
    end

    email = Emails::Email.find_by(title: 'Inscripción a curso/pack gratis')
    if email.nil?
      email = Emails::Email.create(subject: '¡De maravilla! Se ha registrado una nueva inscripción a @product_name!',
                                   title: 'Inscripción a curso/pack gratis',
                                   body: '<html> <table style="width: 100%"> <tr style="width: 100px; height: 5px; border-radius: 33px;"> <td> </td> </tr> <tr> <td class="email-body" width="570" cellpadding="0" cellspacing="0" align="center"> <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation"> <!-- Body title --> <tr class="email-title"> <td class="content-cell"> <h1 class="f-fallback"> Inscripción a curso/pack gratis </h1> </td> </tr> <!-- Body content --> <tr> <td class="content-cell"> <div class="f-fallback"> <div style="background-color: #EBF8FE; width: 100%"><p style="text-align: center; color: #002A8D;"> @student_name se ha inscrito al curso/pack <strong>@product_name</strong>. </p><br><table class="email-content" width="100%"> <tr> <td style="text-align-last: center;"> <img width="360" src="https://cdn.dribbble.com/users/146798/screenshots/2933118/media/3a0d5c55702604d706c636a94b5c1afd.gif" /> <br> <br> </td> </tr></table><p style="text-align: center; color: #002A8D;">Ver más en Academia:</p><!-- Action --><table class="body-action" align="center" width="100%" cellpadding="0" cellspacing="0"> <tr> <td align="center"> <!-- Border based button https://litmus.com/blog/a-guide-to-bulletproof-buttons-in-email-design --> <table width="100%" border="0" cellspacing="0" cellpadding="0"> <tr> <td align="center"> <table border="0" cellspacing="0" cellpadding="0"> <tr style="color: #FFFFFF; text-align: center;"> <td style="color: #FFFFFF;"> <a href="@school_url" class="button button--" target="_blank"><strong>Visitar Academia</strong> </a> </td> </tr> </table> </td> </tr> </table> </td> </tr></table></div> </div> </td> </tr> </table> </td> </tr> </table> <style scoped type="text/css" rel="stylesheet" media="all"> .email-title { font-size: 40px; text-align: center; } @import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,700&display=swap"); body { width: 100% !important; height: 100%; margin: 0; -webkit-text-size-adjust: none; } a { color: #3869D4; } a img { border: none; } td { word-break: break-word; } body, td, th { font-family: "Nunito Sans", Helvetica, Arial, sans-serif; } h1 { margin-top: 0; margin-bottom: 0; font-size: 25px; font-weight: bold; text-align: center; } h2 { margin-top: 0; color: #333333; font-size: 16px; font-weight: bold; text-align: left; } h3 { margin-top: 0; color: #333333; font-size: 14px; font-weight: bold; text-align: left; } td, th { font-size: 16px; } p, ul, ol, blockquote { margin: .4em 0 1.1875em; font-size: 16px; line-height: 1.625; } .align-right { text-align: right; } .align-left { text-align: left; } .align-center { text-align: center; } .button { background-color: #004899; border-top: 10px solid #004899; border-right: 18px solid #004899; border-bottom: 10px solid #004899; border-left: 18px solid #004899; display: inline-block; color: #FFF; text-decoration: none; border-radius: 3px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); -webkit-text-size-adjust: none; box-sizing: border-box; background: #004899; border-radius: 18px; width: 260px; height: 50px; } @media only screen and (max-width: 500px) { .button { width: 100% !important; text-align: center !important; } } .attributes { margin: 0 0 21px; } .attributes_content { background-color: #F4F4F7; padding: 16px; } .attributes_item { padding: 0; } body { background-color: #F2F4F6; color: #51545E; } p { color: #51545E; } .email-wrapper { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #F2F4F6; } .email-content { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body_inner { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; } .email-footer { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .email-footer p { color: #A8AAAF; } .body-action { width: 100%; margin: 30px auto; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .body-sub { margin-top: 25px; padding-top: 25px; border-top: 1px solid #EAEAEC; } .content-cell { padding: 12px 24px; } @media only screen and (max-width: 600px) { .email-body_inner, .email-footer { width: 100% !important; } } </style></html>')

      Emails::Automation.create(when: 'admin_new_free_product', email_id: email.id, is_default: true, variables: {
        'school name': '@school_name',
        'student name': '@student_name',
        'product name': '@product_name'
      }, to_admin: true)
    end

    email2 = Emails::Email.find_by(title: 'Nuevo usuario')
    if email2.nil?
      email2 = Emails::Email.create(subject: 'Bienvenido a @school_name, @user_firstname!',
                                    title: 'Nuevo usuario',
                                    body: '<html> <table style="width: 100%"> <tr style="width: 100px; height: 5px; border-radius: 33px;"> <td> </td> </tr> <tr> <td class="email-body" width="570" cellpadding="0" cellspacing="0" align="center"> <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation"> <!-- Body title --> <tr class="email-title"> <td class="content-cell"> <h1 class="f-fallback"> Nuevo usuario </h1> </td> </tr> <!-- Body content --> <tr> <td class="content-cell"> <div class="f-fallback"> <p style="text-align: center; color: #002A8D;">Le damos la bienvenida. Nos sentimos muy felices de acompañarte en este proceso tan apasionante</p><br><table class="email-content" width="100%"> <tr> <td style="text-align-last: center;"> <img width="360" src="https://cdn.dribbble.com/users/1162077/screenshots/3960993/media/e6455944eb8e811157039efe95acb22f.gif" /> <br> <br> </td> </tr></table><p style="text-align: center; color: #002A8D;">Sus datos de acceso:</p><table class="attributes" width="100%" cellpadding="0" cellspacing="0" style="text-align: center; color: #002A8D;"> <tr> <td class="attributes_content" style="background: #F0F7FF; height: 60px; margin-top: 10px;"> <table width="100%" cellpadding="0" cellspacing="0"> <tr> <td class="attributes_item"><strong>Nombre de usuario:</strong> @user_email</td> </tr> <tr> <td class="attributes_item"><strong>Contraseña:</strong> @user_password</td> </tr> </table> </td> </tr></table><!-- Action --><table class="body-action" align="center" width="100%" cellpadding="0" cellspacing="0"> <tr> <td align="center"> <!-- Border based button https://litmus.com/blog/a-guide-to-bulletproof-buttons-in-email-design --> <table width="100%" border="0" cellspacing="0" cellpadding="0"> <tr> <td align="center"> <table border="0" cellspacing="0" cellpadding="0"> <tr style="color: #FFFFFF; text-align: center;"> <td style="color: #FFFFFF;"> <a href="@school_url" class="button button--" target="_blank"><strong>Visitar Academia</strong> </a> </td> </tr> </table> </td> </tr> </table> </td> </tr></table> </div> </td> </tr> </table> </td> </tr> </table> <style scoped type="text/css" rel="stylesheet" media="all"> .email-title { font-size: 40px; text-align: center;} @import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,700&display=swap"); body { width: 100% !important; height: 100%; margin: 0; -webkit-text-size-adjust: none; } a { color: #3869D4; } a img { border: none; } td { word-break: break-word; } body, td, th { font-family: "Nunito Sans", Helvetica, Arial, sans-serif; } h1 { margin-top: 0; margin-bottom: 0; font-size: 25px; font-weight: bold; text-align: center; } h2 { margin-top: 0; color: #333333; font-size: 16px; font-weight: bold; text-align: left; } h3 { margin-top: 0; color: #333333; font-size: 14px; font-weight: bold; text-align: left; } td, th { font-size: 16px; } p, ul, ol, blockquote { margin: .4em 0 1.1875em; font-size: 16px; line-height: 1.625; } .align-right { text-align: right; } .align-left { text-align: left; } .align-center { text-align: center; } .button { background-color: #004899; border-top: 10px solid #004899; border-right: 18px solid #004899; border-bottom: 10px solid #004899; border-left: 18px solid #004899; display: inline-block; color: #FFF; text-decoration: none; border-radius: 3px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); -webkit-text-size-adjust: none; box-sizing: border-box; background: #004899; border-radius: 18px; width: 260px; height: 50px; } @media only screen and (max-width: 500px) { .button { width: 100% !important; text-align: center !important; } } .attributes { margin: 0 0 21px; } .attributes_content { background-color: #F4F4F7; padding: 16px; } .attributes_item { padding: 0; } body { background-color: #F2F4F6; color: #51545E; } p { color: #51545E; } .email-wrapper { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #F2F4F6; } .email-content { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body_inner { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; } .email-footer { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .email-footer p { color: #A8AAAF; } .body-action { width: 100%; margin: 30px auto; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .body-sub { margin-top: 25px; padding-top: 25px; border-top: 1px solid #EAEAEC; } .content-cell { padding: 12px 24px; } @media only screen and (max-width: 600px) { .email-body_inner, .email-footer { width: 100% !important; } } </style></html>')

      Emails::Automation.create(when: 'new_user', email_id: email2.id, is_default: true,
        variables: {
          'school name': '@school_name',
          'user name': '@user_name',
          'user firstname': '@user_firstname',
          'user email': '@user_email',
          'user password': '@user_password'
        }
      )
    end

    email3 = Emails::Email.find_by(title: 'Nueva reseña')
    if email3.nil?
      email3 = Emails::Email.create(subject: '@course_name tiene una nueva reseña',
                                    title: 'Nueva reseña',
                                    body: '<html> <table style="width:100%"> <tr style="width: 100px; height: 5px;  border-radius: 33px;"> <td> </td> </tr> <tr> <td class="email-body" width="570" cellpadding="0" cellspacing="0" align="center"> <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation"> <!-- Body title --> <tr class="email-title"> <td class="content-cell"> <h1 class="f-fallback"> Nueva reseña </h1> </td> </tr> <!-- Body content --> <tr> <td class="content-cell"> <div class="f-fallback"> <p style="text-align: center; color: #002A8D;"> El curso <strong>@course_name</strong> ha recibido una reseña nueva. </p><br><!-- Action --><table class="body-action" align="center" width="100%" cellpadding="0" cellspacing="0"> <tr> <td align="center"> <!-- Border based button https://litmus.com/blog/a-guide-to-bulletproof-buttons-in-email-design --> <table width="100%" border="0" cellspacing="0" cellpadding="0"> <tr> <td align="center"> <table border="0" cellspacing="0" cellpadding="0"> <tr style="color: #FFFFFF; text-align: center;"> <td style="color: #FFFFFF;"> <a href="@school_url" class="button button--" target="_blank"><strong>Visitar Academia</strong> </a> </td> </tr> </table> </td> </tr> </table> </td> </tr></table> </div> </td> </tr> </table> </td> </tr> </table> <style scoped type="text/css" rel="stylesheet" media="all"> .email-title { font-size: 40px; text-align: center; } @import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,700&display=swap"); body { width: 100% !important; height: 100%; margin: 0; -webkit-text-size-adjust: none; } a { color: #3869D4; } a img { border: none; } td { word-break: break-word; } body, td, th { font-family: "Nunito Sans", Helvetica, Arial, sans-serif; } h1 { margin-top: 0; margin-bottom: 0; font-size: 25px; font-weight: bold; text-align: center; } h2 { margin-top: 0; color: #333333; font-size: 16px; font-weight: bold; text-align: left; } h3 { margin-top: 0; color: #333333; font-size: 14px; font-weight: bold; text-align: left; } td, th { font-size: 16px; } p, ul, ol, blockquote { margin: .4em 0 1.1875em; font-size: 16px; line-height: 1.625; } .align-right { text-align: right; } .align-left { text-align: left; } .align-center { text-align: center; } .button { background-color: #004899; border-top: 10px solid #004899; border-right: 18px solid #004899; border-bottom: 10px solid #004899; border-left: 18px solid #004899; display: inline-block; color: #FFF; text-decoration: none; border-radius: 3px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); -webkit-text-size-adjust: none; box-sizing: border-box; background: #004899; border-radius: 18px; width: 260px; height: 50px; } @media only screen and (max-width: 500px) { .button { width: 100% !important; text-align: center !important; } } .attributes { margin: 0 0 21px; } .attributes_content { background-color: #F4F4F7; padding: 16px; } .attributes_item { padding: 0; } body { background-color: #F2F4F6; color: #51545E; } p { color: #51545E; } .email-wrapper { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #F2F4F6; } .email-content { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body_inner { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; } .email-footer { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .email-footer p { color: #A8AAAF; } .body-action { width: 100%; margin: 30px auto; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .body-sub { margin-top: 25px; padding-top: 25px; border-top: 1px solid #EAEAEC; } .content-cell { padding: 12px 24px; } @media only screen and (max-width: 600px) { .email-body_inner, .email-footer { width: 100% !important; } } </style></html>')

      Emails::Automation.create(when: 'admin_course_review', email_id: email3.id, is_default: true,
        variables: {
          'school name': '@school_name',
          'user name': '@user_name',
          'course name': '@course_name'
        }, to_admin: true
      )
    end

    email4 = Emails::Email.find_by(title: 'Asignacion a curso/pack')
    if email4.nil?
      email4 = Emails::Email.create(subject: '@school_name - Te asignaron a @product_name',
                                    title: 'Asignacion a curso/pack',
                                    body: '<html> <table style="width:100%"> <tr style="width: 100px; height: 5px; border-radius: 33px;"> <td> </td> </tr> <tr> <td class="email-body" width="570" cellpadding="0" cellspacing="0" align="center"> <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation"> <!-- Body title --> <tr class="email-title"> <td class="content-cell"> <h1 class="f-fallback"> Asignacion a curso/pack </h1> </td> </tr> <!-- Body content --> <tr> <td class="content-cell"> <div class="f-fallback"> <p style="text-align: center; color: #002A8D;">El administrador @admin_name te asignó a @product_name</p><br><table class="email-content" width="100%"> <tr> <td style="text-align-last: center;"> <img width="360" src="https://cdn.dribbble.com/users/1162077/screenshots/3695625/media/77f42e5839ded066a9511028baa00b9e.png" /> <br> <br> </td> </tr></table><p style="text-align: center; color: #002A8D;">Accede ahora al nuevo curso:</p><!-- Action --><table class="body-action" align="center" width="100%" cellpadding="0" cellspacing="0" style="color: #FFFFFF; "> <tr style="color: #FFFFFF; "> <td align="center"> <table width="100%" border="0" cellspacing="0" cellpadding="0" style="color: #FFFFFF;"> <tr style="color: #FFFFFF;"> <td align="center" style="color: #FFFFFF;"> <table border="0" cellspacing="0" cellpadding="0" style=""> <tr style="color: #FFFFFF; text-align: center;"> <td style="color: #FFFFFF;"> <a href="@school_url" class="button button--" target="_blank"><strong>Visitar Academia</strong> </a> </td> </tr> </table> </td> </tr> </table> </td> </tr></table> </div> </td> </tr> </table> </td> </tr> </table> <style scoped type="text/css" rel="stylesheet" media="all"> .email-title { font-size: 40px; text-align: center;} @import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,700&display=swap"); body { width: 100% !important; height: 100%; margin: 0; -webkit-text-size-adjust: none; } a { color: #3869D4; } a img { border: none; } td { word-break: break-word; } body, td, th { font-family: "Nunito Sans", Helvetica, Arial, sans-serif; } h1 { margin-top: 0; margin-bottom: 0; font-size: 25px; font-weight: bold; text-align: center; } h2 { margin-top: 0; color: #333333; font-size: 16px; font-weight: bold; text-align: left; } h3 { margin-top: 0; color: #333333; font-size: 14px; font-weight: bold; text-align: left; } td, th { font-size: 16px; } p, ul, ol, blockquote { margin: .4em 0 1.1875em; font-size: 16px; line-height: 1.625; } .align-right { text-align: right; } .align-left { text-align: left; } .align-center { text-align: center; } .button { background-color: #004899; border-top: 10px solid #004899; border-right: 18px solid #004899; border-bottom: 10px solid #004899; border-left: 18px solid #004899; display: inline-block; color: #FFF; text-decoration: none; border-radius: 3px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); -webkit-text-size-adjust: none; box-sizing: border-box; background: #004899; border-radius: 18px; width: 260px; height: 50px; } @media only screen and (max-width: 500px) { .button { width: 100% !important; text-align: center !important; } } .attributes { margin: 0 0 21px; } .attributes_content { background-color: #F4F4F7; padding: 16px; } .attributes_item { padding: 0; } body { background-color: #F2F4F6; color: #51545E; } p { color: #51545E; } .email-wrapper { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #F2F4F6; } .email-content { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body_inner { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; } .email-footer { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .email-footer p { color: #A8AAAF; } .body-action { width: 100%; margin: 30px auto; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .body-sub { margin-top: 25px; padding-top: 25px; border-top: 1px solid #EAEAEC; } .content-cell { padding: 12px 24px; } @media only screen and (max-width: 600px) { .email-body_inner, .email-footer { width: 100% !important; } } </style></html>')

        Emails::Automation.create(when: 'admin_added_student', email_id: email4.id, is_default: true,
        variables: {
          'school name': '@school_name',
          'user name': '@user_name',
          'product name': '@product_name',
          'admin name': '@admin_name'
        }
      )
    end

    email5 = Emails::Email.find_by(title: 'Corrección de examen')
    if email5.nil?
      email5 = Emails::Email.create(subject: 'Tienes una nueva corrección para @exam_name',
                                    title: 'Corrección de examen',
                                    body: '<html> <table style="width: 100%"> <tr style="width: 100px; height: 5px; border-radius: 33px;"> <td> </td> </tr> <tr> <td class="email-body" width="570" cellpadding="0" cellspacing="0" align="center"> <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation"> <!-- Body title --> <tr class="email-title"> <td class="content-cell"> <h1 class="f-fallback"> Corrección de examen </h1> </td> </tr> <!-- Body content --> <tr> <td class="content-cell"> <div class="f-fallback"> <p style="text-align: center; color: #002A8D;"> Tienes una nueva corrección en el examen @exam_name del curso @course_name</p><br><table class="email-content" width="100%"> <tr> <td style="text-align-last: center;"> <img width="320" src="https://cdn.dribbble.com/users/1162077/screenshots/4905387/media/918504469091cf07085268f51d9b3f65.png" /> <br> <br> </td> </tr></table><!-- Action --><table class="body-action" align="center" width="100%" cellpadding="0" cellspacing="0" style="color: #FFFFFF; "> <tr style="color: #FFFFFF; "> <td align="center"> <table width="100%" border="0" cellspacing="0" cellpadding="0" style="color: #FFFFFF;"> <tr style="color: #FFFFFF;"> <td align="center" style="color: #FFFFFF;"> <table border="0" cellspacing="0" cellpadding="0" style=""> <tr style="color: #FFFFFF; text-align: center;"> <td style="color: #FFFFFF;"> <a href="@school_url" class="button button--" target="_blank"><strong>Visitar Academia</strong> </a> </td> </tr> </table> </td> </tr> </table> </td> </tr></table> </div> </td> </tr> </table> </td> </tr> </table> <style scoped type="text/css" rel="stylesheet" media="all"> .email-title { font-size: 40px; text-align: center;} @import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,700&display=swap"); body { width: 100% !important; height: 100%; margin: 0; -webkit-text-size-adjust: none; } a { color: #3869D4; } a img { border: none; } td { word-break: break-word; } body, td, th { font-family: "Nunito Sans", Helvetica, Arial, sans-serif; } h1 { margin-top: 0; margin-bottom: 0; font-size: 25px; font-weight: bold; text-align: center; } h2 { margin-top: 0; color: #333333; font-size: 16px; font-weight: bold; text-align: left; } h3 { margin-top: 0; color: #333333; font-size: 14px; font-weight: bold; text-align: left; } td, th { font-size: 16px; } p, ul, ol, blockquote { margin: .4em 0 1.1875em; font-size: 16px; line-height: 1.625; } .align-right { text-align: right; } .align-left { text-align: left; } .align-center { text-align: center; } .button { background-color: #004899; border-top: 10px solid #004899; border-right: 18px solid #004899; border-bottom: 10px solid #004899; border-left: 18px solid #004899; display: inline-block; color: #FFF; text-decoration: none; border-radius: 3px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); -webkit-text-size-adjust: none; box-sizing: border-box; background: #004899; border-radius: 18px; width: 260px; height: 50px; } @media only screen and (max-width: 500px) { .button { width: 100% !important; text-align: center !important; } } .attributes { margin: 0 0 21px; } .attributes_content { background-color: #F4F4F7; padding: 16px; } .attributes_item { padding: 0; } body { background-color: #F2F4F6; color: #51545E; } p { color: #51545E; } .email-wrapper { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #F2F4F6; } .email-content { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body_inner { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; } .email-footer { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .email-footer p { color: #A8AAAF; } .body-action { width: 100%; margin: 30px auto; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .body-sub { margin-top: 25px; padding-top: 25px; border-top: 1px solid #EAEAEC; } .content-cell { padding: 12px 24px; } @media only screen and (max-width: 600px) { .email-body_inner, .email-footer { width: 100% !important; } } </style></html>')

      Emails::Automation.create(when: 'student_exam_attempt_scored', email_id: email5.id, is_default: true,
       variables: {
        'school name': '@school_name',
        'user name': '@user_name',
        'exam name':  '@exam_name',
        'course name': '@course_name'})
    end

    email6 = Emails::Email.find_by(title: 'Nueva respuesta a comentario')
    if email6.nil?
      email6 = Emails::Email.create(subject: 'Tienes una respuesta a tu comentario en el curso @course_name',
                                    title: 'Nueva respuesta a comentario',
                                    body: '<html> <table style="width: 100%"> <tr style="width: 100px; height: 5px; border-radius: 33px;"> <td> </td> </tr> <tr> <td class="email-body" width="570" cellpadding="0" cellspacing="0" align="center"> <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation"> <!-- Body title --> <tr class="email-title"> <td class="content-cell"> <h1 class="f-fallback"> Nueva respuesta a comentario </h1> </td> </tr> <!-- Body content --> <tr> <td class="content-cell"> <div class="f-fallback"> <p style="text-align: center; color: #002A8D;">Tienes una nueva respuesta de @author_name a tu comentario en la lección @lesson_name del curso @course_name</p><br><table class="email-content" width="100%"> <tr> <td style="text-align-last: center;"> <img width="320" src="https://cdn.dribbble.com/users/1162077/screenshots/4890789/media/ddf7945e4e19314bc79f4022df778672.png" /> <br> <br> </td> </tr></table><!-- Action --><table class="body-action" align="center" width="100%" cellpadding="0" cellspacing="0" style="color: #FFFFFF; "> <tr style="color: #FFFFFF; "> <td align="center"> <table width="100%" border="0" cellspacing="0" cellpadding="0" style="color: #FFFFFF;"> <tr style="color: #FFFFFF;"> <td align="center" style="color: #FFFFFF;"> <table border="0" cellspacing="0" cellpadding="0" style=""> <tr style="color: #FFFFFF; text-align: center;"> <td style="color: #FFFFFF;"> <a href="@school_url" class="button button--" target="_blank"><strong>Visitar Academia</strong> </a> </td> </tr> </table> </td> </tr> </table> </td> </tr></table> </div> </td> </tr> </table> </td> </tr> </table> <style scoped type="text/css" rel="stylesheet" media="all"> .email-title { font-size: 40px; text-align: center; } @import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,700&display=swap"); body { width: 100% !important; height: 100%; margin: 0; -webkit-text-size-adjust: none; } a { color: #3869D4; } a img { border: none; } td { word-break: break-word; } body, td, th { font-family: "Nunito Sans", Helvetica, Arial, sans-serif; } h1 { margin-top: 0; margin-bottom: 0; font-size: 25px; font-weight: bold; text-align: center; } h2 { margin-top: 0; color: #333333; font-size: 16px; font-weight: bold; text-align: left; } h3 { margin-top: 0; color: #333333; font-size: 14px; font-weight: bold; text-align: left; } td, th { font-size: 16px; } p, ul, ol, blockquote { margin: .4em 0 1.1875em; font-size: 16px; line-height: 1.625; } .align-right { text-align: right; } .align-left { text-align: left; } .align-center { text-align: center; } .button { background-color: #004899; border-top: 10px solid #004899; border-right: 18px solid #004899; border-bottom: 10px solid #004899; border-left: 18px solid #004899; display: inline-block; color: #FFF; text-decoration: none; border-radius: 3px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); -webkit-text-size-adjust: none; box-sizing: border-box; background: #004899; border-radius: 18px; width: 260px; height: 50px; } @media only screen and (max-width: 500px) { .button { width: 100% !important; text-align: center !important; } } .attributes { margin: 0 0 21px; } .attributes_content { background-color: #F4F4F7; padding: 16px; } .attributes_item { padding: 0; } body { background-color: #F2F4F6; color: #51545E; } p { color: #51545E; } .email-wrapper { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #F2F4F6; } .email-content { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body_inner { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; } .email-footer { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .email-footer p { color: #A8AAAF; } .body-action { width: 100%; margin: 30px auto; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .body-sub { margin-top: 25px; padding-top: 25px; border-top: 1px solid #EAEAEC; } .content-cell { padding: 12px 24px; } @media only screen and (max-width: 600px) { .email-body_inner, .email-footer { width: 100% !important; } } </style></html>')

      Emails::Automation.create(when: 'user_comment_answer', email_id: email6.id, is_default: true,
        variables: {
          'school name': '@school_name',
          'user name': '@user_name',
          'author name':  '@author_name',
          'lesson name': '@lesson_name',
          'course name': '@course_name'
        }
      )
    end

    email7 = Emails::Email.find_by(title: 'Nuevo comentario')
    if email7.nil?
      email7 = Emails::Email.create(subject: 'Nuevo comentario en el curso @course_name',
                                    title: 'Nuevo comentario',
                                    body: '<html> <table style="width: 100%"> <tr style="width: 100px; height: 5px; border-radius: 33px;"> <td> </td> </tr> <tr> <td class="email-body" width="570" cellpadding="0" cellspacing="0" align="center"> <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation"> <!-- Body title --> <tr class="email-title"> <td class="content-cell"> <h1 class="f-fallback"> Nuevo comentario </h1> </td> </tr> <!-- Body content --> <tr> <td class="content-cell"> <div class="f-fallback"> <p style="text-align: center; color: #002A8D;">Tienes una nuevo comentario realizado por @author_name en la lección @lesson_name del curso @course_name</p><br><!-- Action --><table class="body-action" align="center" width="100%" cellpadding="0" cellspacing="0" style="color: #FFFFFF; "> <tr style="color: #FFFFFF; "> <td align="center"> <table width="100%" border="0" cellspacing="0" cellpadding="0" style="color: #FFFFFF;"> <tr style="color: #FFFFFF;"> <td align="center" style="color: #FFFFFF;"> <table border="0" cellspacing="0" cellpadding="0" style=""> <tr style="color: #FFFFFF; text-align: center;"> <td style="color: #FFFFFF;"> <a href="@school_url" class="button button--" target="_blank"><strong>Visitar Academia</strong> </a> </td> </tr> </table> </td> </tr> </table> </td> </tr></table> </div> </td> </tr> </table> </td> </tr> </table> <style type="text/css" scoped rel="stylesheet" media="all"> .email-title { font-size: 40px; text-align: center; } @import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,700&display=swap"); body { width: 100% !important; height: 100%; margin: 0; -webkit-text-size-adjust: none; } a { color: #3869D4; } a img { border: none; } td { word-break: break-word; } body, td, th { font-family: "Nunito Sans", Helvetica, Arial, sans-serif; } h1 { margin-top: 0; margin-bottom: 0;  font-size: 25px; font-weight: bold; text-align: center; } h2 { margin-top: 0; color: #333333; font-size: 16px; font-weight: bold; text-align: left; } h3 { margin-top: 0; color: #333333; font-size: 14px; font-weight: bold; text-align: left; } td, th { font-size: 16px; } p, ul, ol, blockquote { margin: .4em 0 1.1875em; font-size: 16px; line-height: 1.625; } .align-right { text-align: right; } .align-left { text-align: left; } .align-center { text-align: center; } .button { background-color: #004899; border-top: 10px solid #004899; border-right: 18px solid #004899; border-bottom: 10px solid #004899; border-left: 18px solid #004899; display: inline-block; color: #FFF; text-decoration: none; border-radius: 3px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); -webkit-text-size-adjust: none; box-sizing: border-box; background: #004899; border-radius: 18px; width: 260px; height: 50px; } @media only screen and (max-width: 500px) { .button { width: 100% !important; text-align: center !important; } } .attributes { margin: 0 0 21px; } .attributes_content { background-color: #F4F4F7; padding: 16px; } .attributes_item { padding: 0; } body { background-color: #F2F4F6; color: #51545E; } p { color: #51545E; } .email-wrapper { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #F2F4F6; } .email-content { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body_inner { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; } .email-footer { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .email-footer p { color: #A8AAAF; } .body-action { width: 100%; margin: 30px auto; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .body-sub { margin-top: 25px; padding-top: 25px; border-top: 1px solid #EAEAEC; } .content-cell { padding: 12px 24px; } @media only screen and (max-width: 600px) { .email-body_inner, .email-footer { width: 100% !important; } } </style></html>')

      Emails::Automation.create(when: 'instructor_comment_lesson', email_id: email7.id, is_default: true,
        variables: {
          'school name': '@school_name',
          'user name': '@user_name',
          'author name':  '@author_name',
          'lesson name': '@lesson_name',
          'course name': '@course_name'
        }, to_admin: true
      )
    end

    email8 = Emails::Email.find_by(title: 'Nueva venta de curso/pack')
    if email8.nil?
      email8 = Emails::Email.create( subject: '¡De maravilla! Se ha registrado una nueva venta de un curso',
               title: 'Nueva venta de curso/pack',
               body: '<html> <table style="width: 100%"> <tr style="width: 100px; height: 5px; border-radius: 33px;"> <td> </td> </tr> <tr> <td class="email-body" width="570" cellpadding="0" cellspacing="0" align="center"> <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation"> <!-- Body title --> <tr class="email-title"> <td class="content-cell"> <h1 class="f-fallback"> Nueva venta de curso/pack </h1> </td> </tr> <!-- Body content --> <tr> <td class="content-cell"> <div class="f-fallback"> <div style="background-color: #EBF8FE; width: 100%"><p style="text-align: center; color: #002A8D;"> Se tuvo una nueva venta del producto <strong>@product_name</strong>. </p><br><table class="email-content" width="100%"> <tr> <td style="text-align-last: center;"> <img width="360" src="https://cdn.dribbble.com/users/146798/screenshots/2933118/media/3a0d5c55702604d706c636a94b5c1afd.gif" /> <br> <br> </td> </tr></table><p style="text-align: center; color: #002A8D;">Ver más en Academia:</p><!-- Action --><table class="body-action" align="center" width="100%" cellpadding="0" cellspacing="0"> <tr> <td align="center"> <!-- Border based button https://litmus.com/blog/a-guide-to-bulletproof-buttons-in-email-design --> <table width="100%" border="0" cellspacing="0" cellpadding="0"> <tr> <td align="center"> <table border="0" cellspacing="0" cellpadding="0"> <tr style="color: #FFFFFF; text-align: center;"> <td style="color: #FFFFFF;"> <a href="@school_url" class="button button--" target="_blank"><strong>Visitar Academia</strong> </a> </td> </tr> </table> </td> </tr> </table> </td> </tr></table></div> </div> </td> </tr> </table> </td> </tr> </table> <style scoped type="text/css" rel="stylesheet" media="all"> .email-title { font-size: 40px; text-align: center; } @import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,700&display=swap"); body { width: 100% !important; height: 100%; margin: 0; -webkit-text-size-adjust: none; } a { color: #3869D4; } a img { border: none; } td { word-break: break-word; } body, td, th { font-family: "Nunito Sans", Helvetica, Arial, sans-serif; } h1 { margin-top: 0; margin-bottom: 0; font-size: 25px; font-weight: bold; text-align: center; } h2 { margin-top: 0; color: #333333; font-size: 16px; font-weight: bold; text-align: left; } h3 { margin-top: 0; color: #333333; font-size: 14px; font-weight: bold; text-align: left; } td, th { font-size: 16px; } p, ul, ol, blockquote { margin: .4em 0 1.1875em; font-size: 16px; line-height: 1.625; } .align-right { text-align: right; } .align-left { text-align: left; } .align-center { text-align: center; } .button { background-color: #004899; border-top: 10px solid #004899; border-right: 18px solid #004899; border-bottom: 10px solid #004899; border-left: 18px solid #004899; display: inline-block; color: #FFF; text-decoration: none; border-radius: 3px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); -webkit-text-size-adjust: none; box-sizing: border-box; background: #004899; border-radius: 18px; width: 260px; height: 50px; } @media only screen and (max-width: 500px) { .button { width: 100% !important; text-align: center !important; } } .attributes { margin: 0 0 21px; } .attributes_content { background-color: #F4F4F7; padding: 16px; } .attributes_item { padding: 0; } body { background-color: #F2F4F6; color: #51545E; } p { color: #51545E; } .email-wrapper { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #F2F4F6; } .email-content { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body_inner { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; } .email-footer { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .email-footer p { color: #A8AAAF; } .body-action { width: 100%; margin: 30px auto; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .body-sub { margin-top: 25px; padding-top: 25px; border-top: 1px solid #EAEAEC; } .content-cell { padding: 12px 24px; } @media only screen and (max-width: 600px) { .email-body_inner, .email-footer { width: 100% !important; } } </style></html>')

      Emails::Automation.create(when: 'admin_new_purchase', email_id: email8.id, is_default: true,
        variables: {
          'school name': '@school_name',
          'user name': '@user_name',
          'product name': '@product_name'
        }, to_admin: true
      )
    end

    # recuperacion de contraseña
    email10 = Emails::Email.find_by(title: 'Recuperar contraseña')
    if email10.nil?
      email10 = Emails::Email.create( subject: '[@school_name] Nueva contraseña, @user_firstname',
                                      title: 'Recuperar contraseña',
                                      body: '<html> <table width="100%" > <tr style="width: 100px; height: 5px;  border-radius: 33px;"> <td> </td> </tr> <tr> <td class="email-body" width="570" cellpadding="0" cellspacing="0" align="center"> <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation"> <!-- Body title --> <tr class="email-title"> <td class="content-cell"> <h1 class="f-fallback"> Recuperación de contraseña </h1> </td> </tr> <!-- Body content --> <tr> <td class="content-cell"> <div class="f-fallback"> <p style="text-align: center; color: #002A8D;">Estamos comprometidos en brindarte el mejor servicio. Por motivos de seguridad, se creó, de forma aleatoria, una nueva contraseña.</p><p>Aquí están sus nuevos datos de acceso:</p><br><table class="" width="100%" cellpadding="0" cellspacing="0"> <tr> <td style="background: #F0F7FF; height: 60px; margin-top: 10px;"> <table width="100%" cellpadding="0" cellspacing="0" style="text-align: center; color: #002A8D;"> <tr> <td class="attributes_item">Usuario: <strong> @user_email </strong></td> </tr> <tr> <td class="attributes_item">Contraseña: <strong> @user_password </strong></td> </tr> </table> </td> </tr></table><br><br><p style="text-align: center; color: #002A8D;">Una vez que inicie sesión, cambie esta contraseña para mantener su Academia segura.</p><br><br><!-- Action --><table class="body-action" align="center" width="100%" cellpadding="0" cellspacing="0" style="color: #FFFFFF; "> <tr style="color: #FFFFFF; "> <td align="center"> <table width="100%" border="0" cellspacing="0" cellpadding="0" style="color: #FFFFFF;"> <tr style="color: #FFFFFF;"> <td align="center" style="color: #FFFFFF;"> <table border="0" cellspacing="0" cellpadding="0" style=""> <tr style="color: #FFFFFF; text-align: center;"> <td style="color: #FFFFFF;"> <a href="@school_url" target="_blank" class="button button--">Iniciar Sesión </a> </td> </tr> </table> </td> </tr> </table> </td> </tr></table> </div> </td> </tr> </table> </td> </tr> </table><style scoped type="text/css" rel="stylesheet" media="all"> .email-title { font-size: 40px; text-align: center } @import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,700&display=swap"); body { width: 100% !important; height: 100%; margin: 0; -webkit-text-size-adjust: none; } a { color: #3869D4; } a img { border: none; } td { word-break: break-word; } body, td, th { font-family: "Nunito Sans", Helvetica, Arial, sans-serif; } h1 { margin-top: 0; margin-bottom: 0; font-size: 25px; font-weight: bold; text-align: center; } h2 { margin-top: 0; color: #333333; font-size: 16px; font-weight: bold; text-align: left; } h3 { margin-top: 0; color: #333333; font-size: 14px; font-weight: bold; text-align: left; } td, th { font-size: 16px; } p, ul, ol, blockquote { margin: .4em 0 1.1875em; font-size: 16px; line-height: 1.625; } .align-right { text-align: right; } .align-left { text-align: left; } .align-center { text-align: center; } .button { background-color: #004899; border-top: 10px solid #004899; border-right: 18px solid #004899; border-bottom: 10px solid #004899; border-left: 18px solid #004899; display: inline-block; color: #FFF; text-decoration: none; border-radius: 3px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); -webkit-text-size-adjust: none; box-sizing: border-box; background: #004899; border-radius: 18px; width: 260px; height: 50px; } @media only screen and (max-width: 500px) { .button { width: 100% !important; text-align: center !important; } } .attributes { margin: 0 0 21px; } .attributes_content { background-color: #F4F4F7; padding: 16px; } .attributes_item { padding: 0; } body { background-color: #F2F4F6; color: #51545E; } p { color: #51545E; } .email-wrapper { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #F2F4F6; } .email-content { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body_inner { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; } .email-footer { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .email-footer p { color: #A8AAAF; } .body-action { width: 100%; margin: 30px auto; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .body-sub { margin-top: 25px; padding-top: 25px; border-top: 1px solid #EAEAEC; } .content-cell { padding: 12px 24px; } @media only screen and (max-width: 600px) { .email-body_inner, .email-footer { width: 100% !important; } } </style> </html>')

        Emails::Automation.create(when: 'password_recover', email_id: email10.id, is_default: true,
        variables: {
          'school name': '@school_name',
          'user name': '@user_name',
          'user firstname': '@user_firstname',
          'user email': '@user_email',
          'user password': '@user_password'
        }
      )
    end

    email11 = Emails::Email.find_by(title: 'Lección completada')
    if email11.nil?
      email11 = Emails::Email.create( subject: '[@school_name] ¡Felicidades por completar la lección, @user_name!',
                                      title: 'Lección completada',
                                      body: '<html> <table width="100%" > <tr style="width: 100px; height: 5px;  border-radius: 33px;"> <td> </td> </tr> <tr> <td class="email-body" width="570" cellpadding="0" cellspacing="0" align="center"> <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation"> <!-- Body title --> <tr class="email-title"> <td class="content-cell"> <h1 class="f-fallback">¡Lección completada con éxito!</h1> </td> </tr> <!-- Body content --> <tr> <td class="content-cell"> <div class="f-fallback"> <p style="text-align: center; color: #002A8D;"> ¡Felicidades @user_name, has completado la lección <strong>@lesson_name</strong> en el curso <strong>@course_name</strong>! </p> <br> <p style="text-align: center; color: #002A8D;"> Continúa con el siguiente módulo para seguir avanzando en tu aprendizaje. </p> <br><br> </div> </td> </tr> </table> </td> </tr> </table> <style scoped type="text/css" rel="stylesheet" media="all"> .email-title { font-size: 40px; text-align: center } @import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,700&display=swap"); body { width: 100% !important; height: 100%; margin: 0; -webkit-text-size-adjust: none; } a { color: #3869D4; } a img { border: none; } td { word-break: break-word; } body, td, th { font-family: "Nunito Sans", Helvetica, Arial, sans-serif; } h1 { margin-top: 0; margin-bottom: 0; font-size: 25px; font-weight: bold; text-align: center; } h2 { margin-top: 0; color: #333333; font-size: 16px; font-weight: bold; text-align: left; } h3 { margin-top: 0; color: #333333; font-size: 14px; font-weight: bold; text-align: left; } td, th { font-size: 16px; } p, ul, ol, blockquote { margin: .4em 0 1.1875em; font-size: 16px; line-height: 1.625; } .align-right { text-align: right; } .align-left { text-align: left; } .align-center { text-align: center; } .button { background-color: #004899; border-top: 10px solid #004899; border-right: 18px solid #004899; border-bottom: 10px solid #004899; border-left: 18px solid #004899; display: inline-block; color: #FFF; text-decoration: none; border-radius: 3px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); -webkit-text-size-adjust: none; box-sizing: border-box; background: #004899; border-radius: 18px; width: 260px; height: 50px; } @media only screen and (max-width: 500px) { .button { width: 100% !important; text-align: center !important; } } .attributes { margin: 0 0 21px; } .attributes_content { background-color: #F4F4F7; padding: 16px; } .attributes_item { padding: 0; } body { background-color: #F2F4F6; color: #51545E; } p { color: #51545E; } .email-wrapper { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #F2F4F6; } .email-content { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body_inner { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; } .email-footer { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .email-footer p { color: #A8AAAF; } .body-action { width: 100%; margin: 30px auto; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .body-sub { margin-top: 25px; padding-top: 25px; border-top: 1px solid #EAEAEC; } .content-cell { padding: 12px 24px; } @media only screen and (max-width: 600px) { .email-body_inner, .email-footer { width: 100% !important; } } </style> </html>')
      automation11 = Emails::Automation.find_by(when: 'user_finished_lesson')
      if automation11.nil?
        Emails::Automation.create(when: 'user_finished_lesson', email_id: email11.id, is_default: true, variables: {
          'school name': '@school_name',
          'user name': '@user_name',
          'user email': '@user_email',
          'course name': '@course_name',
          'lesson name': '@lesson_name',
        })
      end
    end

    email12 = Emails::Email.find_by(title: 'Curso completado')
    if email12.nil?
      email12 = Emails::Email.create( subject:'[@school_name] Finalización del curso, @user_firstname',
                                      title: 'Curso completado',
                                      body: '<html> <table width="100%"> <tr style="width: 100px; height: 5px; border-radius: 33px;"> <td></td> </tr> <tr> <td class="email-body" width="570" cellpadding="0" cellspacing="0" align="center"> <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation"> <!-- Body title --> <tr class="email-title"> <td class="content-cell"> <h1 class="f-fallback"> ¡Curso finalizado! </h1> </td> </tr> <!-- Body content --> <tr> <td class="content-cell"> <div class="f-fallback"> <p style="text-align: center; color: #002A8D;"> ¡Felicitaciones @user_name! Has completado el curso "@course_name". Estamos orgullosos de tu logro. </p> <p>Para continuar aprendiendo, inicia sesión en tu cuenta:</p> <br> <table class="" width="100%" cellpadding="0" cellspacing="0"> <tr> <td style="background: #F0F7FF; height: 60px; margin-top: 10px;"> <table width="100%" cellpadding="0" cellspacing="0" style="text-align: center; color: #002A8D;"> <tr> <td class="attributes_item">Usuario: <strong>@user_email</strong></td> </tr> </table> </td> </tr> </table> <br><br> <p style="text-align: center; color: #002A8D;"> ¡Sigue aprendiendo y desarrollando nuevas habilidades con nosotros! </p> <br><br> <!-- Action --> <table class="body-action" align="center" width="100%" cellpadding="0" cellspacing="0" style="color: #FFFFFF;"> <tr style="color: #FFFFFF;"> <td align="center"> <table width="100%" border="0" cellspacing="0" cellpadding="0" style="color: #FFFFFF;"> <tr style="color: #FFFFFF;"> <td align="center" style="color: #FFFFFF;"> <table border="0" cellspacing="0" cellpadding="0" style=""> <tr style="color: #FFFFFF; text-align: center;"> <td style="color: #FFFFFF;"> <a href="@school_url" target="_blank" class="button button--">Iniciar Sesión</a> </td> </tr> </table> </td> </tr> </table> </td> </tr> </table> </div> </td> </tr> </table> </td> </tr> </table> <style scoped type="text/css" rel="stylesheet" media="all"> .email-title { font-size: 40px; text-align: center } @import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,700&display=swap"); body { width: 100% !important; height: 100%; margin: 0; -webkit-text-size-adjust: none; } a { color: #3869D4; } a img { border: none; } td { word-break: break-word; } body, td, th { font-family: "Nunito Sans", Helvetica, Arial, sans-serif; } h1 { margin-top: 0; margin-bottom: 0; font-size: 25px; font-weight: bold; text-align: center; } h2 { margin-top: 0; color: #333333; font-size: 16px; font-weight: bold; text-align: left; } h3 { margin-top: 0; color: #333333; font-size: 14px; font-weight: bold; text-align: left; } td, th { font-size: 16px; } p, ul, ol, blockquote { margin: .4em 0 1.1875em; font-size: 16px; line-height: 1.625; } .align-right { text-align: right; } .align-left { text-align: left; } .align-center { text-align: center; } .button { background-color: #004899; border-top: 10px solid #004899; border-right: 18px solid #004899; border-bottom: 10px solid #004899; border-left: 18px solid #004899; display: inline-block; color: #FFF; text-decoration: none; border-radius: 3px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); -webkit-text-size-adjust: none; box-sizing: border-box; background: #004899; border-radius: 18px; width: 260px; height: 50px; } @media only screen and (max-width: 500px) { .button { width: 100% !important; text-align: center !important; } } .attributes { margin: 0 0 21px; } .attributes_content { background-color: #F4F4F7; padding: 16px; } .attributes_item { padding: 0; } body { background-color: #F2F4F6; color: #51545E; } p { color: #51545E; } .email-wrapper { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #F2F4F6; } .email-content { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body_inner { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; } .email-footer { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .email-footer p { color: #A8AAAF; } .body-action { width: 100%; margin: 30px auto; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .body-sub { margin-top: 25px; padding-top: 25px; border-top: 1px solid #EAEAEC; } .content-cell { padding: 12px 24px; } @media only screen and (max-width: 600px) { .email-body_inner, .email-footer { width: 100% !important; } } </style> </html>')
      automation12 = Emails::Automation.find_by(when: 'user_finished_course')
      if automation12.nil?
        Emails::Automation.create(when: 'user_finished_course', email_id: email12.id, is_default: true, variables: {
          'school name': '@school_name',
          'user name': '@user_name',
          'user email': '@user_email',
          'course name': '@course_name',
        })
      end
    end

    email13 = Emails::Email.find_by(title: 'Módulo completado')
    if email13.nil?
      email13 = Emails::Email.create( subject: '[@school_name] Completed your module, @user_firstname',
                                      title: 'Módulo completado',
                                      body: '<html> <table width="100%"> <tr style="width: 100px; height: 5px; border-radius: 33px;"> <td></td> </tr> <tr> <td class="email-body" width="570" cellpadding="0" cellspacing="0" align="center"> <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation"> <!-- Body title --> <tr class="email-title"> <td class="content-cell"> <h1 class="f-fallback">¡Has completado un módulo!</h1> </td> </tr> <!-- Body content --> <tr> <td class="content-cell"> <div class="f-fallback"> <p style="text-align: center; color: #002A8D;">¡Felicidades, @user_firstname! Has completado el módulo @module_name del curso @course_name.</p> <p>Estamos emocionados por tu progreso y queremos seguir acompañándote en tu aprendizaje.</p> <br> <p style="text-align: center; color: #002A8D;">Visita nuestra plataforma para continuar con el siguiente módulo o explorar otros cursos disponibles.</p> <br><br> <!-- Action --> <table class="body-action" align="center" width="100%" cellpadding="0" cellspacing="0" style="color: #FFFFFF;"> <tr style="color: #FFFFFF;"> <td align="center"> <table width="100%" border="0" cellspacing="0" cellpadding="0" style="color: #FFFFFF;"> <tr style="color: #FFFFFF;"> <td align="center" style="color: #FFFFFF;"> <table border="0" cellspacing="0" cellpadding="0" style=""> <tr style="color: #FFFFFF; text-align: center;"> <td style="color: #FFFFFF;"> <a href="@school_url" target="_blank" class="button button--">Continuar Aprendiendo</a> </td> </tr> </table> </td> </tr> </table> </td> </tr> </table> </div> </td> </tr> </table> </td> </tr> </table> <style scoped type="text/css" rel="stylesheet" media="all"> .email-title { font-size: 40px; text-align: center; } @import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,700&display=swap"); body { width: 100% !important; height: 100%; margin: 0; -webkit-text-size-adjust: none; } a { color: #3869D4; } a img { border: none; } td { word-break: break-word; } body, td, th { font-family: "Nunito Sans", Helvetica, Arial, sans-serif; } h1 { margin-top: 0; margin-bottom: 0; font-size: 25px; font-weight: bold; text-align: center; } h2 { margin-top: 0; color: #333333; font-size: 16px; font-weight: bold; text-align: left; } h3 { margin-top: 0; color: #333333; font-size: 14px; font-weight: bold; text-align: left; } td, th { font-size: 16px; } p, ul, ol, blockquote { margin: .4em 0 1.1875em; font-size: 16px; line-height: 1.625; } .align-right { text-align: right; } .align-left { text-align: left; } .align-center { text-align: center; } .button { background-color: #004899; border-top: 10px solid #004899; border-right: 18px solid #004899; border-bottom: 10px solid #004899; border-left: 18px solid #004899; display: inline-block; color: #FFF; text-decoration: none; border-radius: 3px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); -webkit-text-size-adjust: none; box-sizing: border-box; background: #004899; border-radius: 18px; width: 260px; height: 50px; } @media only screen and (max-width: 500px) { .button { width: 100% !important; text-align: center !important; } } .attributes { margin: 0 0 21px; } .attributes_content { background-color: #F4F4F7; padding: 16px; } .attributes_item { padding: 0; } body { background-color: #F2F4F6; color: #51545E; } p { color: #51545E; } .email-wrapper { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #F2F4F6; } .email-content { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body_inner { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; } .email-footer { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .email-footer p { color: #A8AAAF; } .body-action { width: 100%; margin: 30px auto; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .body-sub { margin-top: 25px; padding-top: 25px; border-top: 1px solid #EAEAEC; } .content-cell { padding: 12px 24px; } @media only screen and (max-width: 600px) { .email-body_inner, .email-footer { width: 100% !important; } } </style> </html>')

      automation13 = Emails::Automation.find_by(when: 'user_finished_module')
      if automation13.nil?
        Emails::Automation.create(when: 'user_finished_module', email_id: email13.id, is_default: true, variables: {
          'school name': '@school_name',
          'user name': '@user_name',
          'user email': '@user_email',
          'course name': '@course_name',
          'module name': '@module_name',
        })
      end
    end

    email14 = Emails::Email.find_by(title: 'Agregado al grupo')
    if email14.nil?
      email14 = Emails::Email.create( subject: '[@school_name] ¡Bienvenido al grupo, @user_firstname!',
                                      title: 'Agregado al grupo',
                                      body: '<html> <table width="100%"> <tr style="width: 100px; height: 5px; border-radius: 33px;"> <td></td> </tr> <tr> <td class="email-body" width="570" cellpadding="0" cellspacing="0" align="center"> <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation"> <!-- Body title --> <tr class="email-title"> <td class="content-cell"> <h1 class="f-fallback">¡Bienvenido al grupo!</h1> </td> </tr> <!-- Body content --> <tr> <td class="content-cell"> <div class="f-fallback"> <p style="text-align: center; color: #002A8D;">Hola @user_firstname, has sido agregado al grupo @group_name.</p> <p>Estamos emocionados de que te unas a este grupo.</p> <br> <p style="text-align: center; color: #002A8D;">Visita nuestra plataforma para ver los contenidos del grupo y participar en las actividades.</p> <br><br> <!-- Action --> <table class="body-action" align="center" width="100%" cellpadding="0" cellspacing="0" style="color: #FFFFFF;"> <tr style="color: #FFFFFF;"> <td align="center"> <table width="100%" border="0" cellspacing="0" cellpadding="0" style="color: #FFFFFF;"> <tr style="color: #FFFFFF;"> <td align="center" style="color: #FFFFFF;"> <table border="0" cellspacing="0" cellpadding="0" style=""> <tr style="color: #FFFFFF; text-align: center;"> <td style="color: #FFFFFF;"> <a href="@school_url" target="_blank" class="button button--">Ver Grupo</a> </td> </tr> </table> </td> </tr> </table> </td> </tr> </table> </div> </td> </tr> </table> </td> </tr> </table> <style scoped type="text/css" rel="stylesheet" media="all"> .email-title { font-size: 40px; text-align: center; } @import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,700&display=swap"); body { width: 100% !important; height: 100%; margin: 0; -webkit-text-size-adjust: none; } a { color: #3869D4; } a img { border: none; } td { word-break: break-word; } body, td, th { font-family: "Nunito Sans", Helvetica, Arial, sans-serif; } h1 { margin-top: 0; margin-bottom: 0; font-size: 25px; font-weight: bold; text-align: center; } h2 { margin-top: 0; color: #333333; font-size: 16px; font-weight: bold; text-align: left; } h3 { margin-top: 0; color: #333333; font-size: 14px; font-weight: bold; text-align: left; } td, th { font-size: 16px; } p, ul, ol, blockquote { margin: .4em 0 1.1875em; font-size: 16px; line-height: 1.625; } .align-right { text-align: right; } .align-left { text-align: left; } .align-center { text-align: center; } .button { background-color: #004899; border-top: 10px solid #004899; border-right: 18px solid #004899; border-bottom: 10px solid #004899; border-left: 18px solid #004899; display: inline-block; color: #FFF; text-decoration: none; border-radius: 3px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); -webkit-text-size-adjust: none; box-sizing: border-box; background: #004899; border-radius: 18px; width: 260px; height: 50px; } @media only screen and (max-width: 500px) { .button { width: 100% !important; text-align: center !important; } } .attributes { margin: 0 0 21px; } .attributes_content { background-color: #F4F4F7; padding: 16px; } .attributes_item { padding: 0; } body { background-color: #F2F4F6; color: #51545E; } p { color: #51545E; } .email-wrapper { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #F2F4F6; } .email-content { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body_inner { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; } .email-footer { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .email-footer p { color: #A8AAAF; } .body-action { width: 100%; margin: 30px auto; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .body-sub { margin-top: 25px; padding-top: 25px; border-top: 1px solid #EAEAEC; } .content-cell { padding: 12px 24px; } @media only screen and (max-width: 600px) { .email-body_inner, .email-footer { width: 100% !important; } } </style> </html>')

      automation14 = Emails::Automation.find_by(when: 'user_added_to_group')
      if automation14.nil?
        Emails::Automation.create(when: 'user_added_to_group', is_default: true, email_id: email14.id, variables: {
          'school name': '@school_name',
          'user name': '@user_name',
          'user email': '@user_email',
          'group name': '@group_name',
        })
      end
    end

    email15 = Emails::Email.find_by(title: 'Agregado al comunidad')
    if email15.nil?
      email15 = Emails::Email.create( subject: '[@school_name] ¡Bienvenido al comunidad, @user_firstname',
                                      title: 'Agregado al comunidad',
                                      body: '<html> <table width="100%"> <tr style="width: 100px; height: 5px; border-radius: 33px;"> <td></td> </tr> <tr> <td class="email-body" width="570" cellpadding="0" cellspacing="0" align="center"> <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation"> <!-- Body title --> <tr class="email-title"> <td class="content-cell"> <h1 class="f-fallback">¡Bienvenido al comunidad!</h1> </td> </tr> <!-- Body content --> <tr> <td class="content-cell"> <div class="f-fallback"> <p style="text-align: center; color: #002A8D;">Hola @user_firstname, has sido agregado al comunidad @community_name.</p> <p>Estamos emocionados de que te unas a este comunidad.</p> <br> <p style="text-align: center; color: #002A8D;">Visita nuestra plataforma para ver los contenidos del comunidad y participar en las actividades.</p> <br><br> <!-- Action --> <table class="body-action" align="center" width="100%" cellpadding="0" cellspacing="0" style="color: #FFFFFF;"> <tr style="color: #FFFFFF;"> <td align="center"> <table width="100%" border="0" cellspacing="0" cellpadding="0" style="color: #FFFFFF;"> <tr style="color: #FFFFFF;"> <td align="center" style="color: #FFFFFF;"> <table border="0" cellspacing="0" cellpadding="0" style=""> <tr style="color: #FFFFFF; text-align: center;"> <td style="color: #FFFFFF;"> <a href="@school_url" target="_blank" class="button button--">Ver comunidad</a> </td> </tr> </table> </td> </tr> </table> </td> </tr> </table> </div> </td> </tr> </table> </td> </tr> </table> <style scoped type="text/css" rel="stylesheet" media="all"> .email-title { font-size: 40px; text-align: center; } @import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,700&display=swap"); body { width: 100% !important; height: 100%; margin: 0; -webkit-text-size-adjust: none; } a { color: #3869D4; } a img { border: none; } td { word-break: break-word; } body, td, th { font-family: "Nunito Sans", Helvetica, Arial, sans-serif; } h1 { margin-top: 0; margin-bottom: 0; font-size: 25px; font-weight: bold; text-align: center; } h2 { margin-top: 0; color: #333333; font-size: 16px; font-weight: bold; text-align: left; } h3 { margin-top: 0; color: #333333; font-size: 14px; font-weight: bold; text-align: left; } td, th { font-size: 16px; } p, ul, ol, blockquote { margin: .4em 0 1.1875em; font-size: 16px; line-height: 1.625; } .align-right { text-align: right; } .align-left { text-align: left; } .align-center { text-align: center; } .button { background-color: #004899; border-top: 10px solid #004899; border-right: 18px solid #004899; border-bottom: 10px solid #004899; border-left: 18px solid #004899; display: inline-block; color: #FFF; text-decoration: none; border-radius: 3px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); -webkit-text-size-adjust: none; box-sizing: border-box; background: #004899; border-radius: 18px; width: 260px; height: 50px; } @media only screen and (max-width: 500px) { .button { width: 100% !important; text-align: center !important; } } .attributes { margin: 0 0 21px; } .attributes_content { background-color: #F4F4F7; padding: 16px; } .attributes_item { padding: 0; } body { background-color: #F2F4F6; color: #51545E; } p { color: #51545E; } .email-wrapper { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #F2F4F6; } .email-content { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body_inner { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; } .email-footer { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .email-footer p { color: #A8AAAF; } .body-action { width: 100%; margin: 30px auto; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .body-sub { margin-top: 25px; padding-top: 25px; border-top: 1px solid #EAEAEC; } .content-cell { padding: 12px 24px; } @media only screen and (max-width: 600px) { .email-body_inner, .email-footer { width: 100% !important; } } </style> </html>')

      automation15 = Emails::Automation.find_by(when: 'user_added_to_community')
      if automation15.nil?
        Emails::Automation.create(when: 'user_added_to_community', is_default: true, email_id: email15.id, variables: {
          'school name': '@school_name',
          'user name': '@user_name',
          'user email': '@user_email',
          'community name': '@community_name',
        })
      end
    end

    email16 = Emails::Email.find_by(title: 'pago completado')
    if email16.nil?
      email16 = Emails::Email.create( subject: '[@school_name] ¡pago completado for, @product_name',
                                      title: 'pago completado',
                                      body: '<html> <table width="100%"> <tr style="width: 100px; height: 5px; border-radius: 33px;"> <td></td> </tr> <tr> <td class="email-body" width="570" cellpadding="0" cellspacing="0" align="center"> <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation"> <!-- Body title --> <tr class="email-title"> <td class="content-cell"> <h1 class="f-fallback">¡pago hecho!</h1> </td> </tr> <!-- Body content --> <tr> <td class="content-cell"> <div class="f-fallback"> <p style="text-align: center; color: #002A8D;">Hola @user_firstname, Se le ha pagado con éxito por @product_name.</p> <br> </div> </td> </tr> </table> </td> </tr> </table> <style scoped type="text/css" rel="stylesheet" media="all"> .email-title { font-size: 40px; text-align: center; } @import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,700&display=swap"); body { width: 100% !important; height: 100%; margin: 0; -webkit-text-size-adjust: none; } a { color: #3869D4; } a img { border: none; } td { word-break: break-word; } body, td, th { font-family: "Nunito Sans", Helvetica, Arial, sans-serif; } h1 { margin-top: 0; margin-bottom: 0; font-size: 25px; font-weight: bold; text-align: center; } h2 { margin-top: 0; color: #333333; font-size: 16px; font-weight: bold; text-align: left; } h3 { margin-top: 0; color: #333333; font-size: 14px; font-weight: bold; text-align: left; } td, th { font-size: 16px; } p, ul, ol, blockquote { margin: .4em 0 1.1875em; font-size: 16px; line-height: 1.625; } .align-right { text-align: right; } .align-left { text-align: left; } .align-center { text-align: center; } .button { background-color: #004899; border-top: 10px solid #004899; border-right: 18px solid #004899; border-bottom: 10px solid #004899; border-left: 18px solid #004899; display: inline-block; color: #FFF; text-decoration: none; border-radius: 3px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); -webkit-text-size-adjust: none; box-sizing: border-box; background: #004899; border-radius: 18px; width: 260px; height: 50px; } @media only screen and (max-width: 500px) { .button { width: 100% !important; text-align: center !important; } } .attributes { margin: 0 0 21px; } .attributes_content { background-color: #F4F4F7; padding: 16px; } .attributes_item { padding: 0; } body { background-color: #F2F4F6; color: #51545E; } p { color: #51545E; } .email-wrapper { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #F2F4F6; } .email-content { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body_inner { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; } .email-footer { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .email-footer p { color: #A8AAAF; } .body-action { width: 100%; margin: 30px auto; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .body-sub { margin-top: 25px; padding-top: 25px; border-top: 1px solid #EAEAEC; } .content-cell { padding: 12px 24px; } @media only screen and (max-width: 600px) { .email-body_inner, .email-footer { width: 100% !important; } } </style> </html>')

      automation16 = Emails::Automation.find_by(when: 'purchase_payment_completed')
      if automation16.nil?
        Emails::Automation.create(when: 'purchase_payment_completed', is_default: true, email_id: email16.id, variables: {
          'school name': '@school_name',
          'user name': '@user_name',
          'user email': '@user_email',
          'product name': '@product_name',
        })
      end
    end

    email17 = Emails::Email.find_by(title: 'cancelar suscripción')
    if email17.nil?
      email17 = Emails::Email.create( subject: '[@school_name] ¡suscripción cancelado for, @product_name',
                                      title: 'cancelar suscripción',
                                      body: '<html> <table width="100%"> <tr style="width: 100px; height: 5px; border-radius: 33px;"> <td></td> </tr> <tr> <td class="email-body" width="570" cellpadding="0" cellspacing="0" align="center"> <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation"> <!-- Body title --> <tr class="email-title"> <td class="content-cell"> <h1 class="f-fallback">¡Tu Plan de Pago Ha Sido Cancelado!</h1> </td> </tr> <!-- Body content --> <tr> <td class="content-cell"> <div class="f-fallback"> <p style="text-align: center; color: #002A8D;">Hola @user_firstname, Tu plan de pago para @product_name ha sido cancelado, lo que significa que ya no tendrás acceso a él.</p> <p style="text-align: center; color: #002A8D;">sLsssamentamos verte partir. 😞</p> <p style="text-align: center; color: #002A8D;">Si esto fue un error o deseas restaurar el acceso, contáctanos. ¡Estaremos encantados de ayudarte!</p> <br> </div> </td> </tr> </table> </td> </tr> </table> <style scoped type="text/css" rel="stylesheet" media="all"> .email-title { font-size: 40px; text-align: center; } @import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,700&display=swap"); body { width: 100% !important; height: 100%; margin: 0; -webkit-text-size-adjust: none; } a { color: #3869D4; } a img { border: none; } td { word-break: break-word; } body, td, th { font-family: "Nunito Sans", Helvetica, Arial, sans-serif; } h1 { margin-top: 0; margin-bottom: 0; font-size: 25px; font-weight: bold; text-align: center; } h2 { margin-top: 0; color: #333333; font-size: 16px; font-weight: bold; text-align: left; } h3 { margin-top: 0; color: #333333; font-size: 14px; font-weight: bold; text-align: left; } td, th { font-size: 16px; } p, ul, ol, blockquote { margin: .4em 0 1.1875em; font-size: 16px; line-height: 1.625; } .align-right { text-align: right; } .align-left { text-align: left; } .align-center { text-align: center; } .button { background-color: #004899; border-top: 10px solid #004899; border-right: 18px solid #004899; border-bottom: 10px solid #004899; border-left: 18px solid #004899; display: inline-block; color: #FFF; text-decoration: none; border-radius: 3px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); -webkit-text-size-adjust: none; box-sizing: border-box; background: #004899; border-radius: 18px; width: 260px; height: 50px; } @media only screen and (max-width: 500px) { .button { width: 100% !important; text-align: center !important; } } .attributes { margin: 0 0 21px; } .attributes_content { background-color: #F4F4F7; padding: 16px; } .attributes_item { padding: 0; } body { background-color: #F2F4F6; color: #51545E; } p { color: #51545E; } .email-wrapper { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #F2F4F6; } .email-content { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body_inner { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; } .email-footer { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .email-footer p { color: #A8AAAF; } .body-action { width: 100%; margin: 30px auto; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .body-sub { margin-top: 25px; padding-top: 25px; border-top: 1px solid #EAEAEC; } .content-cell { padding: 12px 24px; } @media only screen and (max-width: 600px) { .email-body_inner, .email-footer { width: 100% !important; } } </style> </html>')

      automation17 = Emails::Automation.find_by(when: 'student_subscription_cancel')
      if automation17.nil?
        Emails::Automation.create(when: 'student_subscription_cancel', is_default: true, email_id: email17.id, variables: {
          'school name': '@school_name',
          'user name': '@user_name',
          'user email': '@user_email',
          'product name': '@product_name',
        })
      end
    end

    email18 = Emails::Email.find_by(title: 'suscripción de nuevo producto')
    if email18.nil?
      email18 = Emails::Email.create( subject: '[@school_name] ¡Nueva suscripción para @product_name',
                                      title: 'suscripción de nuevo producto',
                                      body: '<html> <table width="100%"> <tr style="width: 100px; height: 5px; border-radius: 33px;"> <td></td> </tr> <tr> <td class="email-body" width="570" cellpadding="0" cellspacing="0" align="center"> <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation"> <!-- Body title --> <tr class="email-title"> <td class="content-cell"> <h1 class="f-fallback">¡Suscrito al nuevo Curso!</h1> </td> </tr> <!-- Body content --> <tr> <td class="content-cell"> <div class="f-fallback"> <p style="text-align: center; color: #002A8D;">Hola @user_firstname, te suscribiste a @product_name.</p> <p style="text-align: center; color: #002A8D;">acias por suscribirte!</p></p> <br> </div> </td> </tr> </table> </td> </tr> </table> <style scoped type="text/css" rel="stylesheet" media="all"> .email-title { font-size: 40px; text-align: center; } @import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,700&display=swap"); body { width: 100% !important; height: 100%; margin: 0; -webkit-text-size-adjust: none; } a { color: #3869D4; } a img { border: none; } td { word-break: break-word; } body, td, th { font-family: "Nunito Sans", Helvetica, Arial, sans-serif; } h1 { margin-top: 0; margin-bottom: 0; font-size: 25px; font-weight: bold; text-align: center; } h2 { margin-top: 0; color: #333333; font-size: 16px; font-weight: bold; text-align: left; } h3 { margin-top: 0; color: #333333; font-size: 14px; font-weight: bold; text-align: left; } td, th { font-size: 16px; } p, ul, ol, blockquote { margin: .4em 0 1.1875em; font-size: 16px; line-height: 1.625; } .align-right { text-align: right; } .align-left { text-align: left; } .align-center { text-align: center; } .button { background-color: #004899; border-top: 10px solid #004899; border-right: 18px solid #004899; border-bottom: 10px solid #004899; border-left: 18px solid #004899; display: inline-block; color: #FFF; text-decoration: none; border-radius: 3px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); -webkit-text-size-adjust: none; box-sizing: border-box; background: #004899; border-radius: 18px; width: 260px; height: 50px; } @media only screen and (max-width: 500px) { .button { width: 100% !important; text-align: center !important; } } .attributes { margin: 0 0 21px; } .attributes_content { background-color: #F4F4F7; padding: 16px; } .attributes_item { padding: 0; } body { background-color: #F2F4F6; color: #51545E; } p { color: #51545E; } .email-wrapper { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #F2F4F6; } .email-content { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body_inner { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; } .email-footer { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .email-footer p { color: #A8AAAF; } .body-action { width: 100%; margin: 30px auto; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .body-sub { margin-top: 25px; padding-top: 25px; border-top: 1px solid #EAEAEC; } .content-cell { padding: 12px 24px; } @media only screen and (max-width: 600px) { .email-body_inner, .email-footer { width: 100% !important; } } </style> </html>')

      automation18 = Emails::Automation.find_by(when: 'new_subscription_product')
      if automation18.nil?
        Emails::Automation.create(when: 'new_subscription_product', is_default: true, email_id: email18.id, variables: {
          'school name': '@school_name',
          'user name': '@user_name',
          'user email': '@user_email',
          'product name': '@product_name',
        })
      end
    end

    email_webinar = Emails::Email.find_by(title: 'recordatorio de conference')

    if email_webinar.nil?
      email_webinar = Emails::Email.create(
        subject: '[@school_name] ¡Recordatorio! conference de @course_name mañana',
        title: 'recordatorio de conference',
        body: '<html> <body> <table width="100%"> <tr><td class="email-body" width="570" align="center"> <table class="email-body_inner" align="center" width="570" role="presentation"> <tr class="email-title"> <td class="content-cell"> <h1 class="f-fallback">¡Recordatorio de conference!</h1> </td> </tr> <tr> <td class="content-cell"> <div class="f-fallback"> <p style="text-align: center; color: #002A8D;"> Hola @user_firstname, </p> <p style="text-align: center; color: #002A8D;"> Te recordamos que tienes una conferencia programada para la lección <strong>@lesson_name</strong> del curso <strong>@course_name</strong> mañana, @event_time. </p> <p style="text-align: center; color: #002A8D;"> Puedes unirte usando el siguiente enlace: </p> <p style="text-align: center;"> <a href="@event_url" class="button" target="_blank">Unirse al conference</a> </p> <p style="text-align: center; color: #002A8D;"> ¡Te esperamos! </p> <br> </div> </td> </tr> </table> </td></tr> </table> </body> </html>'
      )

      automation_webinar = Emails::Automation.find_by(when: 'webinar_or_conference_reminder')

      if automation_webinar.nil?
        Emails::Automation.create(
          when: 'webinar_or_conference_reminder',
          is_default: true,
          email_id: email_webinar.id,
          variables: {
            'school name': '@school_name',
            'user firstname': '@user_firstname',
            'course name': '@course_name',
            'event_url': '@event_url',
            'event_time': '@event_time',
          }
        )
      end
    end

    admin_roles.create!

    website = websites.home_page
    if website.present?
      # update this if you are adding more default sections in create_models
      body_sections = website.body_sections.where(content_type: ['Websites::BodySectionProductList', 'Websites::BodySectionBanner', 'Websites::BodySectionTestimonial', 'Websites::BodySectionTextMedia'])
      body_sections.each do |section|
        create_margin_data(section)
      end
    end
  end

  def create_margin_data(section)
    margin_types = ['Subheader', 'Header', 'Content', 'Media']
    margin_types.each do|type|
      next if type == 'Media' && section.content_type != 'Websites::BodySectionTextMedia'
      next if section.content_type.in?(['Websites::BodySectionProductList', 'Websites::BodySectionTestimonial']) && type == 'Content'
  
      margin_data = {
        margin_type: type,
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
      }
      section.margins.find_or_create_by(margin_data)
    end
  end

  def set_defaults
    # If there is no country, use the IP to find the country
    if self.country.nil?
      require 'ipinfo'
      handler = IPinfo.create(Rails.application.credentials.ipinfo.access_token)
      details = handler.details(self.ip_address)
      self.country = details.all[:bogon] ? "PE" : details.country
    end
    # Country based defaults
    c = ISO3166::Country.new(self.country)
    self.timezone = c.timezones.zone_identifiers[0]
    self.default_currency = c.currency.iso_code
    self.language = c.languages_official[0] unless language.present?

    self.language = 'pt-br' if language.present? && language == 'pt'
    # Plan defaults
    if feature_plan_id == 'free'
      self.is_trial = false
    else
      self.feature_plan_expiration_date = 15.days.from_now
    end
    # Other defaults
    self.school_name = subdomain.titleize
    self.api_key = SecureRandom.hex(16)
  end

  # Google Cloud Storage methods

  # def calculate_used_storage!
  #   storage_fn = lambda do |bucket|
  #     gsutil_string = `gsutil du -s gs://#{bucket}/uploads/#{subdomain}`
  #     match = gsutil_string.match /^(?<size>\d+)/
  #     size = match[:size].to_i
  #     size / (1024 * 1024.0)
  #   end
  #   self.private_storage_used_mib = storage_fn.call Shrine.storages[:store].bucket
  #   self.public_storage_used_mib = storage_fn.call Shrine.storages[:public_store].bucket
  #   self.storage_last_calculated_at = Time.zone.now
  #   save!
  # end

  def calculate_used_storage!
    calculate_bucket_storage = lambda do |storage|
      bucket = storage.bucket.name
      client = storage.client
      prefix = "uploads/#{subdomain}/"
  
      total_bytes = 0
      continuation_token = nil
  
      loop do
        response = client.list_objects_v2(
          bucket: bucket,
          prefix: prefix,
          continuation_token: continuation_token
        )
  
        response.contents.each do |object|
          total_bytes += object.size
        end
  
        break unless response.is_truncated
        continuation_token = response.next_continuation_token
      end
  
      # Convert bytes to MiB
      total_bytes / (1024.0 * 1024.0)
    end
  
    self.private_storage_used_mib = calculate_bucket_storage.call(Shrine.storages[:store])
    self.public_storage_used_mib  = calculate_bucket_storage.call(Shrine.storages[:public_store])
    self.storage_last_calculated_at = Time.zone.now
    save!
  end

  def change_subdomain(new_subdomain)
    raise Sabiorealm::Error, "El subdominio ya existe" if Tenant.where(subdomain: new_subdomain).exists?
    current_subdomain = subdomain
    updated_files_info = nil
    ActiveRecord::Base.transaction do
      update!(subdomain: new_subdomain)
      pp "updating db paths"
      fields = {
        'User' => ['avatar'],
        'Website' => ['header_logo', 'header_logo_sticky', 'school_logo', 'school_favicon'],
        'Courses::Course' => ['promo_picture', 'promo_video', 'cover_picture', 'video_preview_picture', 'certificate_picture'],
        'Courses::Pack' => ['promo_picture', 'promo_video', 'cover_picture', 'video_preview_picture'],
        'Courses::ExamQuestion' => ['picture'],
        'Courses::LessonLegacyHtml' => ['html_file', 'css_file', 'js_file'],
        'Media::LessonContent' => ['file'],
        'Social::Comment' => ['attachment'],
        'Subscriptions::ExamAnswerFile' => ['answer'],
        'Subscriptions::Subscription' => ['completion_certificate'],
        'Websites::BodySectionBanner' => ['image'],
        'Websites::BodySectionTextMedia' => ['media'],
        'Websites::PersonalTestimonial' => ['picture'],
      }
      updated_files_info = fields.map do |model_string, fields|
        model = model_string.constantize
        updated_columns = fields.map do |field|
          updated_amount = model.update_all("#{field}_data = #{field}_data || jsonb_build_object('id', replace(#{field}_data->>'id', '/#{current_subdomain}/', '/#{new_subdomain}/'))")
          { field => updated_amount }
        end
        { model_string => updated_columns}
      end
      pp "moving transcoded videos"
      change_subdomain_transcoded_videos(new_subdomain)
    end
    pp "updating google buckets"
    storage_buckets = [
      Shrine.storages[:store],
      Shrine.storages[:public_store],
    ]
    move_results = storage_buckets.map do |storage|
      bucket_name = storage.bucket.name
      client = storage.client
      source_prefix = "uploads/#{current_subdomain}/"
      destination_prefix = "uploads/#{new_subdomain}/"
  
      # List all files with old prefix
      response = client.list_objects_v2(bucket: bucket_name, prefix: source_prefix)
      files_moved = 0
  
      response.contents.each do |object|
        source_key = object.key
        new_key = source_key.sub(source_prefix, destination_prefix)
  
        # Copy to new key
        client.copy_object(
          copy_source: "#{bucket_name}/#{source_key}",
          bucket: bucket_name,
          key: new_key
        )
  
        # Delete original
        client.delete_object(bucket: bucket_name, key: source_key)
        files_moved += 1
      end

      { bucket: bucket_name, files_moved: }
    end

    { updated_files_info:, r2_results: move_results }
  end

  def change_subdomain_transcoded_videos(new_subdomain)
    current_subdomain = subdomain
    Media::LessonContent.where(file_type: 'video').map do |lv|
      derivatives = lv.file_data["derivatives"]
      derivatives&.map do |name, data|
        data["id"] = data["id"].gsub("/#{current_subdomain}/", "/#{new_subdomain}/")
        lv.file_data["derivatives"][name] = data
      end
      lv.save!
      lv.file_data["derivatives"]
    end
  end

  # def destroy_academy_storage
  #   storages = [
  #     Shrine.storages[:store].bucket,
  #     Shrine.storages[:public_store].bucket,
  #   ]
  #   storages.map do |bucket|
  #     `gsutil -m rm -r "gs://#{bucket}/uploads/#{subdomain}"`
  #   end

  #   # Deletes all GCS data from cache and store, public and private, for this tenant

  # end

  def destroy_academy_storage
    return unless subdomain.present?

    storages = [
      Shrine.storages[:store],
      Shrine.storages[:public_store]
    ]
  
    storages.each do |storage|
      bucket = storage.bucket.name
      prefix = "uploads/#{subdomain}"
  
      response = storage.client.list_objects_v2(
        bucket: bucket,
        prefix: prefix
      )
  
      keys = response.contents.map { |obj| { key: obj.key } }
  
      if keys.any?
        storage.client.delete_objects(
          bucket: bucket,
          delete: {
            objects: keys,
            quiet: true
          }
        )
      end
    end
  end

  def destroy_bunny_library
    ::BunnyCdn::BunnyApi.delete_video_library(self) unless is_bunny_shared_library?
  end

  # Plan methods

  def set_plan!(plan, feature_plan_expiration_date: nil, is_trial: nil)
    # Si el plan no es gratuito, lo asigna con la fecha de expiración y el status de trial
    # Si el plan es gratuito, automáticamente asigna la expiración y el trial
    self.feature_plan_id = plan
    if plan == 'free'
      self.feature_plan_expiration_date = nil
      self.is_trial = false
    else
      self.feature_plan_expiration_date = feature_plan_expiration_date
      self.is_trial = is_trial
    end
    save!
  end

  def exceeds_bunny_storage_limit
    return false unless feature_plan_id == 'free'

    bunny_storage_used_mib > 5000
  end

  def website_lang(lang)
    case lang
    when 'es'
      return {
        banner_title: 'Propuesta de Valor Principal',
        banner_subheader: '¡Y comienza la mágica aventura! Encuentra todo lo que necesitas para diseñar, producir y lanzar tu proyecto de E-Learning en Latinoamérica.',

        section_header: 'Propuesta de Valor 1',
        section_subheader: 'En este bloque te recomendamos poner algo que genere valor en tus potenciales clientes',
        section_description: 'El valor es lo que tus clientes potenciales consideran valioso, por ejemplo, si les das más información respecto a tu curso o capacitación, si les hablas de posibles soluciones a problemas a los que se enfrentan, también podrías hablarles de las posibilidades que tendrán luego de llevar uno de tus cursos o capacitaciones.',

        section2_header: 'Propuesta de Valor 2',
        section2_subheader: 'En este bloque te recomendamos colocar otra propuesta de valor',
        section2_description: 'Para poder armar una buena propuesta de valor debes de conocer bien a tu potencial cliente, piensa como él y pregúntate: a qué problemas se enfrenta, cuáles son sus sueños, cuáles son sus retos, qué lo motiva, qué libros lee, qué páginas visita, cuál sería el motivo por el que te compraría un curso.',

        section3_header: 'Título de alguno de tus cursos estrella',
        section3_subheader: 'En este bloque, además de colocar una propuesta de valor, puedes también colocar información breve de uno de tus cursos',
        section3_description: 'En este párrafo, puedes colocar información de tu curso, recuerda el poder de las palabras, puedes comenzar con una pregunta relacionada a problemas que tienen las personas interesadas en llevar este curso, o una historia resumen de tu curso, incluso, puedes hablar brevemente de los beneficios de llevar este curso. Recuerda que no solo se vende hablando, sino también escribiendo.',

        testimonial: 'Prueba Social',
        testimonial_subheader: 'La prueba social es uno de los factores mas importantes que las personas consideran para tomar una decisión, los testimonios, los años de experiencias que tienes, las organizaciones reconocidas con las que trabajas, forman parte de esta prueba social.'
      }
    when 'pt-br'
      return {
        banner_title: 'Proposta de valor central',
        banner_subheader: 'E a aventura mágica começa! Encontre tudo o que você precisa para criar, produzir e lançar seu projeto de E-Learning na América Latina.',

        section_header: 'Proposta de valor 1',
        section_subheader: 'Neste bloco, recomendamos que coloque algo que gere valor para os seus potenciais clientes.',
        section_description: 'Valor é o que os seus potenciais clientes consideram valioso, por exemplo, se lhes der mais informações sobre o seu curso ou formação, se lhes falar de possíveis soluções para os problemas que enfrentam, pode também falar-lhes das possibilidades que terão depois de frequentarem um dos seus cursos ou formações.',

        section2_header: 'Proposta de valor 2',
        section2_subheader: 'Neste bloco, recomendamos que coloque outra proposta de valor',
        section2_description: 'Para elaborar uma boa proposta de valor, deve conhecer bem o seu potencial cliente, pensar como ele e perguntar a si próprio: que problemas enfrenta, quais são os seus sonhos, quais são os seus desafios, o que o motiva, que livros lê, que sítios Web visita, qual seria a razão para comprar um curso seu.',

        section3_header: 'Título de um dos seus cursos emblemáticos',
        section3_subheader: 'Neste bloco, para além de colocar uma proposta de valor, pode também colocar uma breve informação sobre um dos seus cursos.',
        section3_description: 'Neste parágrafo, pode colocar informações sobre o seu curso, lembre-se do poder das palavras, pode começar com uma pergunta relacionada com problemas que as pessoas interessadas em fazer este curso têm, ou com uma história resumida do seu curso, pode até falar brevemente sobre os benefícios de fazer este curso. Lembre-se que não se vende apenas a falar, mas também a escrever.',

        testimonial: 'Prova social',
        testimonial_subheader: 'A prova social é um dos factores mais importantes que as pessoas consideram quando tomam uma decisão. Os testemunhos, os anos de experiência que tem, as organizações reconhecidas com as quais trabalha, fazem todos parte desta prova social.'
      }
    else
      return {
        banner_title: 'Main Value Proposition',
        banner_subheader: 'And the magical adventure begins! Find everything you need to design, produce and launch your E-Learning project in Latin America.',

        section_header: 'Value Proposition 1',
        section_subheader: 'In this block we recommend you put something that generates value in your potential customers',
        section_description: 'Value is what your prospects consider valuable, for example, if you give them more information about your course or training, if you tell them about possible solutions to problems they face, You could also tell them about the possibilities they will have after taking one of your courses or trainings.',

        section2_header: 'Value Proposition 2',
        section2_subheader: 'In this block we recommend placing another value proposition',
        section2_description: 'In order to build a good value proposition you must know your potential customer well, think like him and ask yourself: what problems he faces, what are his dreams, what are his challenges, what motivates him, what books he reads, what pages you visit, what would be the reason why I would buy you a course.',

        section3_header: 'Title of one of your star courses',
        section3_subheader: 'In this block, in addition to placing a value proposition, you can also place brief information of one of your courses',
        section3_description: 'In this paragraph, you can place information about your course, remember the power of words, you can start with a question related to problems that people interested in taking this course have, or a summary history of your course, You can even talk briefly about the benefits of taking this course. Remember that you not only sell talking, but also writing.',

        testimonial: 'Social proof',
        testimonial_subheader: 'Social proof is one of the most important factors that people consider to make a decision, the testimonies, the years of experiences you have, the recognized organizations with which you work, are part of this social proof'
      }
    end
  end

  def default_stripe_payment_method
      pm = self.stripe_payment_methods.find{|payment_method| payment_method["default"] == true }
      pm["id"]
  end

  def set_default_payment_method(payment_method_id)
      Stripe::Customer.update(self.stripe_customer_id, {invoice_settings: {default_payment_method: payment_method_id}})
      get_stripe_payment_methods(payment_method_id)
  end

  def get_default_payment_method
      Stripe.api_key = Rails.application.credentials.stripe.api_secret
      customer_id = self.stripe_customer_id
      customer = Stripe::Customer.retrieve(customer_id)
      unless customer[:deleted]
        default_payment_method_id = customer.invoice_settings.default_payment_method
        get_stripe_payment_methods(default_payment_method_id)
      end
  end

  def get_stripe_payment_methods(default_payment_method_id)
      customer_id = self.stripe_customer_id
      payment_methods = Stripe::Customer.list_payment_methods(customer_id, {type: 'card'}).data
      return self.update(stripe_payment_methods: []) unless payment_methods.present?
      if default_payment_method_id.nil?
        default_payment_method_id = payment_methods.first.id
        Stripe::Customer.update(customer_id, {invoice_settings: {default_payment_method: default_payment_method_id}})
      end
      cards = []
      payment_methods.each do |payment_method|
      cards.push(
        {
          id: payment_method.id,
          last4: payment_method.card.last4,
          brand: payment_method.card.brand,
          default:  payment_method.id == default_payment_method_id ? true : false
        })
      end
      self.update(stripe_payment_methods: cards)
  end

  def create_invoice(invoice)
    invoice_subscription = Invoices::Subscription.find_by(invoice_id: invoice[:id])
    status = invoice[:status]
    date = invoice[:status_transitions][:paid_at]

    args = {
      invoice_id: invoice[:id],
      charge_id: invoice[:charge],
      amount_cents:  invoice[:amount_due],
      amount_currency: invoice[:currency],
      status: status,
      description: invoice[:billing_reason],
      payment_date: date.nil? ? nil : Time.zone.at(date).to_datetime,
      hosted_invoice_url: invoice[:hosted_invoice_url]
    }
    if invoice_subscription.nil?
      Invoices::Subscription.create!(**args)
    else
      invoice_subscription.update!(**args)
    end
  end

  def get_stripe_invoices
    Stripe.api_key = Rails.application.credentials.stripe.api_secret
    response = Stripe::Invoice.list(customer: self.stripe_customer_id, limit: 20)
    invoices = response[:data]

    if invoices.present?
      invoices.each do |invoice|
        create_invoice(invoice)
      end
    end
  end

  def past_due_invoice_url
    return "" if stripe_customer_id.blank?
    return "" if !is_trial && feature_plan_expiration_date.present? && feature_plan_expiration_date > Time.current

    Stripe.api_key = Rails.application.credentials.stripe.api_secret

    db_invoice = invoices
        .where(status: ['open', 'failed'])
        .order(payment_date: :desc)
        .find do |inv|
      overdue = inv.payment_date.present? && inv.payment_date < Time.current
      no_due_date_but_unpaid = inv.payment_date.nil?
      overdue || no_due_date_but_unpaid
    end

    if db_invoice&.invoice_id.present?
      begin
        stripe_invoice = Stripe::Invoice.retrieve(db_invoice.invoice_id)
        if stripe_invoice.status == 'open' && stripe_invoice.hosted_invoice_url.present?
          return stripe_invoice.hosted_invoice_url
        end
      rescue Stripe::StripeError => e
        Rails.logger.error("Stripe error when retrieving invoice #{db_invoice.invoice_id}: #{e.message}")
      end
    end

    invoices = Stripe::Invoice.list(customer: stripe_customer_id, status: 'open')
  
    past_due_invoice = invoices.data.find do |invoice|
      overdue = invoice.due_date.present? && Time.at(invoice.due_date) < Time.current
      no_due_date_but_unpaid = invoice.due_date.nil? && invoice.paid == false
      overdue || no_due_date_but_unpaid
    end
  
    return "" unless past_due_invoice
  
    past_due_invoice.hosted_invoice_url || ""
  rescue Stripe::StripeError => e
    Rails.logger.error("Stripe error when fetching invoices: #{e.message}")
    ""
  end  

  # Payment methods
  def available_payment_platforms
    payment_platforms = []
    payment_platforms << "pay_pal" if pay_pal_client_id.present? && pay_pal_client_secret.present?
    payment_platforms << "mercado_pago" if mercado_pago_public_key.present? && mercado_pago_secret_access_token.present?
    payment_platforms << "stripe" if stripe_api_key.present?
    payment_platforms
  end

  def reset_all_passwords!
    User.where.not(type: "SuperAdmin").each do |user|
      user.password_reset!
    end
  end

  def migrate_bunny_data
    BunnyCdn::MigrateBunnyVideoJob.perform_later(id)
  end

  def set_bunny_cdn_video_library
    if bunny_video_library_id.nil? && (is_trial || feature_plan_id == 'free')
      self.bunny_video_library_id = ENV['BUNNY_SHARED_LIBRARY_ID']
      self.bunny_api_key = ENV['BUNNY_SHARED_LIBRARY_API_KEY']
      self.bunny_token_security_key = ENV['BUNNY_SHARED_TOKEN_KEY']
      self.save
    end
  end

  def is_bunny_shared_library?
    bunny_video_library_id.present? && bunny_video_library_id.to_s == ENV['BUNNY_SHARED_LIBRARY_ID']
  end

  def exceeded_limit_features
    exceeded_features = []
  
    if admins.count > feature_plan.user_limits['admins']
      exceeded_features << :admin_limit_exceeded
    end
  
    if instructors.count > feature_plan.user_limits['instructors']
      exceeded_features << :instructor_limit_exceeded
    end
  
    if used_storage_mib > feature_plan.max_upload_size
      exceeded_features << :max_upload_size_exceeded
    end
  
    if lead_form_contacts.count > feature_plan.contact_limits
      exceeded_features << :contact_limit_exceeded
    end
  
    if communities.count > feature_plan.community_limits
      exceeded_features << :community_limit_exceeded
    end
  
    if websites.count > feature_plan.website_limits
      exceeded_features << :website_limit_exceeded
    end
  
    if feature_plan_id == 'basic' && courses.where(has_certificate: true).present?
      exceeded_features << :certification_courses_setup
    end
  
    exceeded_features
  end

  def create_default_metrics
    under_tenant_context(self) do
      return if tenant_metric.present?

      create_tenant_metric(subscription_type: :not_subscribed, pages_created: websites.count, tenant_id: self.id, utm_gl_data: utm_gl_data)
    end
  end

  def is_customer
    tenant_metric&.is_customer
  end

  def subscription_type
    tenant_metric&.subscription_type
  end

  def update_customer_status
    under_tenant_context(self) do
      tenant_metric&.update(is_customer: !is_trial && feature_plan_id != "free")
    end
  end

  def update_subscription_type
    under_tenant_context(self) do
      # tenant_metric&.update(subscription_type: :not_subscribed) if is_trial_previously_was == false
    end
  end

  def mark_as_customer!
    tenant_metric&.update(is_customer: true)
  end

  class << self
    def calculate_all_used_storage
      MultiTenantSupport.turn_off_protection
      Tenant.all.each do |t|
        t.calculate_used_storage!
      rescue StandardError => e
        pp e
      end
    end
  end

  store_accessor :data_protection, :available_option, :link
end
