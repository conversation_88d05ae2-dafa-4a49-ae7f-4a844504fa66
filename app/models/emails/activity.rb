class Emails::Activity < ApplicationRecord

  belongs_to_tenant :tenant
  belongs_to :broadcast, class_name: 'Emails::Broadcast'
  belongs_to :user, optional: true

  enum status: {
    pending: 'pending',
    success: 'success',
    failed: 'failed'
  }

  validates :email_address, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :status, presence: true
  validates :occurred_at, presence: true
end
