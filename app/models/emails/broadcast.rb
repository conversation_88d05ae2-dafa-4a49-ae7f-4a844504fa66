class Emails::Broadcast < ApplicationRecord
  belongs_to_tenant :tenant
  belongs_to :signature, class_name: 'Emails::Signature', optional: true
  belongs_to :template, class_name: 'Emails::Template', optional: true
  belongs_to :filter, class_name: 'UserFilter', optional: true
  has_many :activities, class_name: 'Emails::Activity', dependent: :destroy
  belongs_to :sender, class_name: 'User', optional: true
  before_commit :set_the_sender_id, if: :saved_change_to_send_copy_to_sender?

  after_commit :schedule_broadcast_delivery, on: :create
  after_commit :reschedule_the_job, if: :schedule_related_changes?
  before_destroy :cancel_scheduled_job

  def schedule_related_changes?
    saved_change_to_status? || saved_change_to_scheduled_type? || saved_change_to_scheduled_at?
  end
  
  enum status: {
    draft: 'draft',
    ready: 'ready',
    sending: 'sending',
    incomplete:'incomplete',
    delivered: 'delivered',
    failed: 'failed'
  }

  enum sent_to: {
    all_members: 'all_members',
    list_with_filters: 'list_with_filters',
  }

  enum scheduled_type: {
    send_now: 'send_now',
    scheduled: 'scheduled'
  }

  validates :title, presence: true, uniqueness: { scope: :tenant_id }
  validates :subject, presence: true
  validates :body, presence: true
  validates :status, presence: true
  validates :sent_to, presence: true

  validates :filter, presence: true, if: :list_with_filters?

  scope :by_status, ->(status) { where(status: status) }

  private

  def schedule_broadcast_delivery
    return unless status != 'ready'

    scheduler = Emails::BroadcastSchedulerService.new(self)
    scheduler.schedule_broadcast
  end

  def reschedule_the_job
    return unless status != 'ready'
    cancel_scheduled_job
    schedule_broadcast_delivery
  end

  def cancel_scheduled_job
    return unless sidekiq_id.present?

    Sidekiq::ScheduledSet.new.find_job(sidekiq_id)&.delete
  end

  def set_the_sender_id
    self.sender_id = context[:current_user] if send_copy_to_sender
  end
end
