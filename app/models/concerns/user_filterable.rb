# app/models/concerns/user_filterable.rb

module UserFilterable
  extend ActiveSupport::Concern

  included do
  end

  def apply_filters(users, filters)
    # users = apply_date_range_filter(users, filters) if filters.date_range.present?
    users = apply_filter_groups(users, filters) if filters.filter_groups.present?
    users
  end

  private

  def apply_date_range_filter(users, filters)
    case filters.date_range
    when 'today'
      users.where("DATE(users.created_at) = ?", Date.today)
    when 'this_week'
      users.where(created_at: Time.now.beginning_of_week..Time.now.end_of_week)
    when 'this_month'
      users.where(created_at: Time.now.beginning_of_month..Time.now.end_of_month)
    when 'last_7_days'
      users.where(created_at: 7.days.ago.beginning_of_day.utc..Time.now.end_of_day.utc)
    when 'last_30_days'
      users.where(created_at: 30.days.ago.beginning_of_day.utc..Time.now.end_of_day.utc)
    when 'last_60_days'
      users.where(created_at: 60.days.ago.beginning_of_day.utc..Time.now.end_of_day.utc)
    when 'last_365_days'
      users.where(created_at: 365.days.ago.beginning_of_day.utc..Time.now.end_of_day.utc)
    when 'custom'
      if filters.custom_date_range.present? && filters.custom_date_range['start'].present? && filters.custom_date_range['end'].present?
        start_date = Date.parse(filters.custom_date_range['start']).beginning_of_day.utc
        end_date = Date.parse(filters.custom_date_range['end']).end_of_day.utc
        users = users.where(created_at: start_date..end_date)
      end
      users
    else
      users
    end
  end

  def apply_filter_groups(users, filters)
    initial_users = users
    filters.filter_groups.each_with_index do |filter_group, index|
      filter_group['conditions'].reject! { |condition| condition['field'].blank? }

      next unless filter_group['conditions'].present?

      if index == 0
        users = apply_conditions(users, filter_group['conditions'])
      else
        filter_users = apply_conditions(initial_users, filter_group['conditions'])
        combined_ids = users.ids + filter_users.ids
        users = User.where(id: combined_ids)
      end
    end
    users
  end

  def apply_conditions(users, conditions)
    conditions.each do |condition|
      users = apply_condition(users, condition)
    end
    users
  end

  # Apply individual condition filters
  def apply_condition(users, condition)
    case condition['field']
    when 'tag'
      apply_tag_filter(users, condition)
    when 'courses'
      apply_courses_filter(users, condition)
    when 'course_status'
      apply_course_status(users, condition)
    when 'group'
      apply_group_filter(users, condition)
    when 'exam_score'
      apply_exam_score_filter(users, condition)
    when 'last_activity'
      apply_last_activity_filter(users, condition)
    else
      users
    end
  end

   # Apply tag filter
   def apply_tag_filter(users, condition)
    return users unless condition['value'].present?

    tag_ids = condition['value'].map { |tag| tag['id'] }
    users.joins(:tags).where(tags: { id: tag_ids })
  end

  # Apply courses filter
  def apply_courses_filter(users, condition)
    return users unless condition['value'].present?

    course_ids = condition['value'].map { |course| course['id'] }
    users = Student.where(id: users.ids).joins(:subscriptions).where(subscriptions_subscriptions: { course_id: course_ids })
    # users = apply_course_status(users, condition['status']) if condition['status'].present?
    users
  end

  def apply_course_status(users, condition)
    return users unless condition['value'].present?
    
    users = Student.where(id: users.ids)
    course_status = case condition['value']
                    when 'in_progress'
                      'IN_PROGRESS'
                    when 'not_begin'
                      'NOT_STARTED'
                    when 'finished'
                      'FINISHED'
                    else
                      ''
                    end
    users = users.joins(:subscriptions).where(subscriptions_subscriptions: { completion_status: course_status }) if course_status.present?
    users
  end

  # Apply group filter
  def apply_group_filter(users, condition)
    return users unless condition['value'].present?

    group_ids = condition['value'].map { |group| group['id'] }
    Student.where(id: users.ids).joins(:groups).where(groups: { id: group_ids })
  end

  # Apply exam score filter
  def apply_exam_score_filter(users, condition)
    return users unless condition['exam_condition'].present? && condition['exam_score'].present?

    exam_ids = condition['value'].map { |exam| exam['id'] }
    score = (condition['exam_score'].to_i / 100.0)

    s_users = Student.where(id: users.ids) 
    apply_exam_score_condition(s_users, condition['exam_condition'], exam_ids, score)
  end

  def apply_exam_score_condition(users, exam_condition, exam_ids, score)
    case exam_condition
    when 'less_than'
      users.joins(:lessons, :exam_attempts).where(exam_attempts: { exam_id: exam_ids }).where('exam_attempts.total_score < ?', score)
    when 'equal_to'
      users.joins(:lessons, :exam_attempts).where(exam_attempts: { exam_id: exam_ids, total_score: score })
    when 'more_than'
      users.joins(:lessons, :exam_attempts).where(exam_attempts: { exam_id: exam_ids }).where('exam_attempts.total_score > ?', score)
    else
      users
    end
  end

  # Apply last activity filter
  def apply_last_activity_filter(users, condition)
    return users unless condition['value'].present?
    users.where('last_day_activity = ?', condition['value'].to_date)
  end
end
