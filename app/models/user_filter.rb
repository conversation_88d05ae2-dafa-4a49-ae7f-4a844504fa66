class UserFilter < ApplicationRecord
  belongs_to_tenant :tenant

  has_many :broadcasts, class_name: 'Emails::Broadcast', foreign_key: :filter_id, dependent: :nullify

  enum list_type: {
    email_filter: 'email_filter',
    user_list_filter: 'user_list_filter'
  }

  validates :name, presence: true, uniqueness: { scope: [:tenant_id, :list_type] }
  validates :list_type, presence: true
  validates :filters, presence: true

  scope :email_filters, -> { where(list_type: 'email_filter') }
  scope :user_list_filters, -> { where(list_type: 'user_list_filter') }
  scope :recent, -> { order(created_at: :desc) }
end
