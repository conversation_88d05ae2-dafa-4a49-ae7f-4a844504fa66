class Gamification::RewardSetting < ApplicationRecord
  belongs_to_tenant :tenant
  belongs_to :gamification_setting, class_name: 'Gamification::Setting'

  belongs_to :rewardable, polymorphic: true, optional: true

  enum condition_type: {
    points: 'points',
    level: 'level',
    badge: 'badge'
  }

  enum reward_type: {
    course: 'course',
    coupon: 'coupon'
  }

  validates :condition_type, presence: true, inclusion: { in: condition_types.keys }
  validates :condition_value, presence: true, numericality: { greater_than: 0 }
  validates :rewardable, presence: true

  scope :enabled, -> { where(enabled: true) }

  before_validation :set_rewardable_type

  def level_setting
    return nil unless condition_type == 'level'

    level = tenant.gamification_setting&.level_settings&.find_by(id: condition_value)
    if level.nil? && condition_value.present?
      errors.add(:condition_value, "must be a valid level setting ID")
    end

    @level_setting ||= level
  end

  def badge_setting
    return nil unless condition_type == 'badge'

    badge = tenant.gamification_setting&.badge_settings&.find_by(id: condition_value)
    if badge.nil? && condition_value.present?
      errors.add(:condition_value, "must be a valid badge setting ID")
    end

    @badge_setting ||= badge
  end

  private

  def set_rewardable_type
    if self.reward_type == 'course'
      self.rewardable_type = 'Courses::Course'
    elsif self.reward_type == 'coupon'
      self.rewardable_type = 'Purchases::Coupon'
    end
  end
end
