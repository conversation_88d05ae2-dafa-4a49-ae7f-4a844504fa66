class Gamification::Setting < ApplicationRecord
  belongs_to_tenant :tenant

  has_many :level_settings, dependent: :destroy, foreign_key: :gamification_setting_id
  has_many :point_settings, dependent: :destroy, foreign_key: :gamification_setting_id
  has_many :badge_settings, dependent: :destroy, foreign_key: :gamification_setting_id
  has_many :reward_settings, dependent: :destroy, foreign_key: :gamification_setting_id
  after_commit :create_default_settings, on: :create

  def create_default_settings
    level_settings.create_default_level_settings(self) unless level_settings.exists?
    point_settings.create_default_point_settings(self) unless point_settings.exists?
    badge_settings.create_default_badge_settings(self) unless badge_settings.exists?

    unless configured_settings?
      Gamification::CreateUserProfilesJob.perform_later(id)
      setting_apply_to_past_data
    end

    update!(configured_settings: true)
  end

  def initial_level_setting
    level_settings.enabled.order(:position).first
  end

  private

  def setting_apply_to_past_data
    return unless apply_to_past_data?

    Gamification::ApplyPastDataJob.perform_later(id)
  end
end
