class Gamification::UserProfile < ApplicationRecord
  belongs_to_tenant :tenant
  belongs_to :user

  belongs_to :level_setting, class_name: 'Gamification::LevelSetting'

  has_many :earned_badges, dependent: :destroy, class_name: 'Gamification::UserBadge', foreign_key: :user_id, primary_key: :user_id
  has_many :gamification_badges, through: :earned_badges, source: :badge_setting
  has_many :gamification_rewards, dependent: :destroy, class_name: 'Gamification::UserReward', foreign_key: :user_id, primary_key: :user_id
  has_many :points_activities, dependent: :destroy, class_name: 'Gamification::UserPointsActivity', foreign_key: :user_id, primary_key: :user_id

  validates :total_points, numericality: { greater_than_or_equal_to: 0 }
  validates :current_level_progress, numericality: { greater_than_or_equal_to: 0 }
  after_commit :evaluate_level_on_points_change, :sent_points_earned_notification, :record_points_activity, if: :saved_change_to_total_points?
  after_commit :sent_level_up_notification, if: :saved_change_to_level_setting_id?

  attr_accessor :current_action_type, :current_points_added

  def add_points(points_to_add, action_type = nil)
    self.total_points += points_to_add
    self.current_action_type = action_type
    self.current_points_added = points_to_add

    save
  end

  private

  def evaluate_level_on_points_change
    Gamification::LevelEvaluator.call(user: user, profile: self)
  end

  def record_points_activity
    return unless current_action_type.present? && current_points_added.present?
    
   user_points_activity =  user.gamification_points_activities.new(
      action_type: current_action_type,
      points_earned: current_points_added,
      earned_at: Time.now,
      activity_date: Date.current
    )
    user_points_activity.save!
  rescue StandardError => e
    Rails.logger.error("Error recording points activity: #{e.message}")
  end

  def sent_points_earned_notification
    return unless current_action_type.present? && current_points_added.present?

    action_message = I18n.t("gamification.points.actions.#{current_action_type}",
                           default: I18n.t("gamification.points.actions.default"))

    full_message = I18n.t("gamification.points.earned",
                         action: action_message,
                         points: current_points_added)

    NotificationsManager.push_notification(
      [user.guid],
      {
        type: "POINTS_EARNED",
        message: full_message,
        extras: {
          points_earned: current_points_added,
          action_type: current_action_type,
          total_points: total_points
        }
      }
    )

    # Real-time ActionCable broadcast
    ActionCable.server.broadcast(
      "notifications_#{user.guid}",
      {
        type: "points",
        title: I18n.t("gamification.toast.points_earned", default: "Points Earned!"),
        message: full_message,
        toast_class: "toast-points",
      }
    )
  rescue StandardError => e
    Rails.logger.error("Error sending points earned notification: #{e.message}")
  end

  def sent_level_up_notification
    return unless level_setting.present?

    message = I18n.t("gamification.level.level_up", level_name: level_setting.title)

    NotificationsManager.push_notification(
      [user.guid],
      {
        type: "LEVEL_UP",
        message: message,
        extras: {
          new_level: level_setting.title,
          new_level_id: level_setting.id
        }
      }
    )
    ActionCable.server.broadcast(
      "notifications_#{user.guid}",
      {
        type: "level_up",
        title: I18n.t("gamification.toast.level_up", default: "Level Up!"),
        message: message,
        icon: level_setting.icon&.url,
        toast_class: "toast-level"
      }
    )

  rescue StandardError => e
    Rails.logger.error("Error sending level up notification: #{e.message}")
  end
end
