class Gamification::UserBadge < ApplicationRecord
  belongs_to_tenant :tenant
  belongs_to :user
  belongs_to :badge_setting, class_name: 'Gamification::BadgeSetting'

  validates :earned_at, presence: true

  after_commit :sent_badge_earned_notification, on: :create

  private

  def sent_badge_earned_notification
    message = I18n.t("gamification.badge.unlocked", badge_name: badge_setting.title)

    NotificationsManager.push_notification(
      [user.guid],
      {
        type: "BADGE_UNLOCKED",
        message: message,
        extras: {
          badge_title: badge_setting.title,
          badge_id: badge_setting.id,
          points_earned: badge_setting.points
        }
      }
    )

    ActionCable.server.broadcast(
      "notifications_#{user.guid}",
      {
        type: "badge",
        title: I18n.t("gamification.toast.badge_unlocked", default: "Badge Unlocked!"),
        message: message,
        icon: badge_setting.icon.url,
        toast_class: "toast-badge"
      }
    )
  rescue StandardError => e
    Rails.logger.error("Error sending badge notification: #{e.message}")
  end
end
