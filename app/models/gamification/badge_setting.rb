class Gamification::BadgeSetting < ApplicationRecord
  belongs_to_tenant :tenant
  belongs_to :gamification_setting, class_name: 'Gamification::Setting'
  has_many :user_badges, class_name: 'Gamification::UserBadge', dependent: :destroy
  has_many :user_profiles, through: :user_badges
  
  include ImageUploader::Attachment.new(:icon)

  enum condition_type: {
    completed_1_course: "completed_1_course",
    completed_5_course: "completed_5_course",
    completed_12_course: "completed_12_course",
    completed_30_course: "completed_30_course",
    logged_10_consecutive_day: "logged_10_consecutive_day",
    scored_100_in_exam: "scored_100_in_exam",
    earned_first_certificate: "earned_first_certificate",
    earned_20_certificates: "earned_20_certificates",
    made_10_community_posts: "made_10_community_posts",
    received_10_reactions: "received_10_reactions",
    responded_to_question_or_post: "responded_to_question_or_post",
    commented_on_a_lesson: "commented_on_a_lesson",
    left_a_course_review: "left_a_course_review"
  }

  CONDITION_TITLES = {
    completed_1_course: "Apprentice of Knowledge",
    completed_5_course: "Orb Bearer",
    completed_12_course: "Archmage of Knowledge",
    completed_30_course: "Golden Sage",
    logged_10_consecutive_day: "Loyal to Learning",
    scored_100_in_exam: "Excellence",
    earned_first_certificate: "Initiator of the Runes",
    earned_20_certificates: "Legend of the Phoenix",
    made_10_community_posts: "Active Member",
    received_10_reactions: "Appreciated Wise Man",
    responded_to_question_or_post: "Wise and Solidarity",
    commented_on_a_lesson: "Oracle of the Forum",
    left_a_course_review: "Forest Guide"
  }.freeze

  validates :title, presence: true
  validates :condition_type, presence: true, inclusion: { in: condition_types.keys }
  validates :points, numericality: { greater_than_or_equal_to: 0 }

  scope :enabled, -> { where(enabled: true) }

  before_validation :set_defaults, on: :create

  def self.create_default_badge_settings(setting)
    condition_types.keys.each do |type|
      badge_data = {
        condition_type: type,
        title: CONDITION_TITLES[type.to_sym],
        points: 5
      }

      badge = setting.badge_settings.create!(badge_data)

      # Set default icon using the service
      icon_url = Gamification::DefaultIconService.badge_icon_url(type)
      Gamification::DefaultIconService.set_default_icon(badge, icon_url)
    end
  end

  def user_progress(user)
    return 100.0 if user.gamification_badges.include?(self)

    calculate_progress_percentage(user)&.round(2)
  end

  private

  def calculate_progress_percentage(user)
    case condition_type
    when 'completed_1_course'
      completed_courses = user.subscriptions.where.not(course: nil).where(completion_status: 'FINISHED').count
      [(completed_courses.to_f / 1 * 100), 100.0].min
    when 'completed_5_course'
      completed_courses = user.subscriptions.where.not(course: nil).where(completion_status: 'FINISHED').count
      [(completed_courses.to_f / 5 * 100), 100.0].min
    when 'completed_12_course'
      completed_courses = user.subscriptions.where.not(course: nil).where(completion_status: 'FINISHED').count
      [(completed_courses.to_f / 12 * 100), 100.0].min
    when 'completed_30_course'
      completed_courses = user.subscriptions.where.not(course: nil).where(completion_status: 'FINISHED').count
      [(completed_courses.to_f / 30 * 100), 100.0].min
    when 'scored_100_in_exam'
      has_perfect_score = user.exam_attempts.where(status: 'passed', total_score: 1.0).exists?
      has_perfect_score ? 100.0 : 0.0
    when 'earned_first_certificate'
      certificates_count = user.subscriptions.where.not(completion_certificate_data: nil).count
      [(certificates_count.to_f / 1 * 100), 100.0].min
    when 'earned_20_certificates'
      certificates_count = user.subscriptions.where.not(completion_certificate_data: nil).count
      [(certificates_count.to_f / 20 * 100), 100.0].min
    when 'commented_on_a_lesson'
      lesson_comments = user.social_comments.where(commentable_type: 'Courses::Lesson').count
      lesson_comments >= 1 ? 100.0 : 0.0
    when 'made_10_community_posts'
      community_posts = user.my_community_posts.count
      [(community_posts.to_f / 10 * 100), 100.0].min
    when 'responded_to_question_or_post'
      community_comments = user.social_comments.where(commentable_type: 'Communities::CommunityPost').count
      community_comments >= 1 ? 100.0 : 0.0
    when 'logged_10_consecutive_day'
      consecutive_days = UserActivity.consecutive_login_days_for(user, streak_days: 10)
      [(consecutive_days.to_f / 10 * 100), 100.0].min
    when 'received_10_reactions'
      reactions_count = user.social_comments.joins(:reactions).count
      [(reactions_count.to_f / 10 * 100), 100.0].min
    when 'left_a_course_review'
      reviews_count = user.subscriptions.joins(:student_review).count
      reviews_count >= 1 ? 100.0 : 0.0
    else
      0.0
    end
  end

  def set_defaults
    self.title ||= CONDITION_TITLES[condition_type&.to_sym]
  end
end
