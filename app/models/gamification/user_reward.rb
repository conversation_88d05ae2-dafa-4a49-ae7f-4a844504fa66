class Gamification::User<PERSON><PERSON>ard < ApplicationRecord
  belongs_to_tenant :tenant

  belongs_to :user
  belongs_to :reward_setting, class_name: 'Gamification::RewardSetting'

  enum claimed_status: {
    pending: 0,
    claimed: 1,
    expired: 2
  }

  validates :claimed_status, presence: true

  after_commit :send_reward_notification, on: :create

  private

  def send_reward_notification
    return unless user.is_a?(Student)

    reward_name = case reward_setting.reward_type
                  when 'course'
                    reward_setting.rewardable&.name
                  when 'coupon'
                    reward_setting.rewardable&.code
                  else
                    'None'
                  end

    message = I18n.t("gamification.reward.earned",
                     reward_type: I18n.t("gamification.reward.types.#{reward_setting.reward_type}"),
                     reward_name: reward_name)

    NotificationsManager.push_notification(
      [user.guid],
      {
        type: "REWARD_EARNED",
        message: message,
        extras: {
          reward_id: id
        }
      }
    )

    ActionCable.server.broadcast(
      "notifications_#{user.guid}",
      {
        type: "reward",
        title: I18n.t("gamification.toast.reward_earned", default: "<PERSON>ward Earned!"),
        message: message,
        toast_class: "toast-reward",
      }
    )
  rescue StandardError => e
    Rails.logger.error("Error sending reward notification: #{e.message}")
  end
end
