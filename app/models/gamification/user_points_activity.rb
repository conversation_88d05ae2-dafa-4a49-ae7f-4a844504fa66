class Gamification::UserPointsActivity < ApplicationRecord
  belongs_to_tenant :tenant
  belongs_to :user

  validates :action_type, presence: true
  validates :points_earned, presence: true, numericality: { greater_than: 0 }
  validates :activity_date, presence: true
  validates :earned_at, presence: true

  scope :for_user, ->(user) { where(user: user) }
  scope :for_date_range, ->(start_date, end_date) { where(activity_date: start_date..end_date) }
  scope :weekly, -> { where(activity_date: 1.week.ago.to_date..Date.current) }
  scope :monthly, -> { where(activity_date: 1.month.ago.to_date..Date.current) }
  scope :this_week, -> { where(activity_date: Date.current.beginning_of_week..Date.current.end_of_week) }
  scope :this_month, -> { where(activity_date: Date.current.beginning_of_month..Date.current.end_of_month) }

  def self.leaderboard(period: :weekly, limit: 10)
    case period.to_sym
    when :weekly
      date_range = Date.current.beginning_of_week..Date.current.end_of_week
    when :monthly
      date_range = Date.current.beginning_of_month..Date.current.end_of_month
    else
      raise ArgumentError, "Invalid period. Use :weekly or :monthly"
    end

    joins(:user)
      .where(activity_date: date_range)
      .where(users: { type: 'Student' })
      .group(:user_id, 'users.tenant_id')
      .select('user_id, users.tenant_id, SUM(points_earned) as total_points')
      .order('total_points DESC')
      .limit(limit)
  end

  def self.user_rank(user, period: :weekly)
    case period.to_sym
    when :weekly
      date_range = Date.current.beginning_of_week..Date.current.end_of_week
    when :monthly
      date_range = Date.current.beginning_of_month..Date.current.end_of_month
    else
      raise ArgumentError, "Invalid period. Use :weekly or :monthly"
    end

    user_points = where(user: user, activity_date: date_range).sum(:points_earned)
    
    users_above = joins(:user)
                    .where(activity_date: date_range)
                    .where(users: { type: 'Student' })
                    .group(:user_id)
                    .having('SUM(points_earned) > ?', user_points)
                    .count.size

    users_above + 1
  end

  def self.calculate_user_points_for_period(user, period)
    case period.to_sym
    when :weekly
      date_range = Date.current.beginning_of_week..Date.current.end_of_week
    when :monthly
      date_range = Date.current.beginning_of_month..Date.current.end_of_month
    else
      return 0
    end
    where(user: user, activity_date: date_range)
      .sum(:points_earned)
  end
end
