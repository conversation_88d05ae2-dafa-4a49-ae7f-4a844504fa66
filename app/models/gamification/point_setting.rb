class Gamification::PointSetting < ApplicationRecord
  belongs_to_tenant :tenant
  belongs_to :gamification_setting, class_name: 'Gamification::Setting'

  enum action_type: {
    module_completed: "module_completed",
    course_completed: "course_completed",
    exam_passed: "exam_passed",
    comment_on_lesson: "comment_on_lesson",
    comment_on_community: "comment_on_community",
    post_on_community: "post_on_community",
    certificate_earned: "certificate_earned"
  }

  validates :action_type, presence: true, inclusion: { in: action_types.keys }
  validates :points, numericality: { greater_than_or_equal_to: 0 }

  scope :enabled, -> { where(enabled: true) }

  def self.create_default_point_settings(setting)
    setting.point_settings.create!([
      {
        action_type: :module_completed,
        points: 5,
        description: "The student must complete a module to earn points. This encourages steady progress and reinforces commitment to incremental learning."
      },
      {
        action_type: :course_completed,
        points: 20,
        description: "Earn points by completing an entire course. This rewards deeper engagement and long-term learning goals."
      },
      {
        action_type: :exam_passed,
        points: 5,
        description: "Gain points by passing an exam. This reinforces mastery and validates knowledge acquisition."
      },
      {
        action_type: :comment_on_lesson,
        points: 2,
        description: "Commenting on a lesson promotes interaction and reflection, which helps reinforce learning outcomes."
      },
      {
        action_type: :comment_on_community,
        points: 3,
        description: "Engage in community discussions by commenting, fostering collaborative learning and peer support."
      },
      {
        action_type: :post_on_community,
        points: 10,
        description: "Start a conversation or share insights in the community to drive engagement and idea exchange."
      },
      {
        action_type: :certificate_earned,
        points: 20,
        description: "Receive points when earning a certificate, signifying recognized achievement in the platform."
      }
    ])
  end
end
