class Gamification::LevelSetting < ApplicationRecord
  belongs_to_tenant :tenant
  belongs_to :gamification_setting, class_name: 'Gamification::Setting'
  
  include ImageUploader::Attachment.new(:icon)

  validates :title, presence: true
  validates :min_points, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :max_points, presence: true, numericality: { greater_than: :min_points }
  validate :no_overlap_with_existing_levels
  scope :enabled, -> { where(enabled: true) }

  has_many :user_profiles, class_name: 'Gamification::UserProfile', dependent: :destroy

  def self.create_default_level_settings(setting)
    default_levels = [
      { title: "Novice of Knowledge", min_points: 0, max_points: 49, position: 1, level_number: 1 },
      { title: "Knowledge Explorer", min_points: 50, max_points: 149, position: 2, level_number: 2 },
      { title: "Archmage of thought", min_points: 150, max_points: 299, position: 3, level_number: 3 },
      { title: "Oracle of Knowledge", min_points: 300, max_points: 499, position: 4, level_number: 4 },
      { title: "Interdimensional Sage", min_points: 500, max_points: 9999, position: 5, level_number: 5 }
    ]

    default_levels.each do |level_data|
      level_number = level_data.delete(:level_number)
      level = setting.level_settings.create!(level_data)

      # Set default icon using the service
      icon_url = Gamification::DefaultIconService.level_icon_url(level_number)
      Gamification::DefaultIconService.set_default_icon(level, icon_url)
    end
  end

  private

  def no_overlap_with_existing_levels
    overlapping = gamification_setting.level_settings.where.not(id: id).where(
      "(min_points <= ? AND max_points >= ?) OR (min_points <= ? AND max_points >= ?) OR (min_points >= ? AND max_points <= ?)",
      min_points, min_points,
      max_points, max_points,
      min_points, max_points
    )

    if overlapping.exists?
      errors.add(:base, "Point range overlaps with an existing level of #{overlapping.first.title}")
    end
  end
end
