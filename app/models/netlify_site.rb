class NetlifySite < ApplicationRecord
  has_many :tenants, dependent: :nullify

  validates :site_id, presence: true, uniqueness: true
  validates :site_name, presence: true
  validates :domain_aliases_count, presence: true, numericality: { greater_than_or_equal_to: 0 }

  # Constants
  MAX_DOMAINS_PER_SITE = 100
  
  scope :active, -> { where(active: true) }
  scope :available_for_new_domain, -> { where('domain_aliases_count < ?', MAX_DOMAINS_PER_SITE) }
  scope :ordered_by_usage, -> { order(:domain_aliases_count) }

  def available_slots
    MAX_DOMAINS_PER_SITE - domain_aliases_count
  end

  def can_add_domain?
    active? && available_slots > 0
  end

  def full?
    domain_aliases_count >= MAX_DOMAINS_PER_SITE
  end

  def increment_domain_count!
    increment!(:domain_aliases_count)
  end

  def decrement_domain_count!
    decrement!(:domain_aliases_count) if domain_aliases_count > 0
  end

  def self.find_available_site
    available_for_new_domain.active.ordered_by_usage.first
  end

  def self.find_or_create_available_site(domain)
    available_site = find_available_site
    return available_site if available_site.present?

    site_creator = NetlifySiteCreator.new
    result = site_creator.create_new_site_and_record(domain)

    if result[:success]
      result[:netlify_site]
    end
  end
end
