class WebhooksController < ActionController::API
  include MultiTenantHelper

  def stripe_user
    payload = request.body.read
    event = nil

    event = Stripe::Event.construct_from(JSON.parse(payload, symbolize_names: true))

    MultiTenantSupport.turn_off_protection do
      case event.type
      when 'checkout.session.completed'
        session_id = event.data.object.id        
        stripe_subscription_id = event.data.object.subscription

        purchase = ::Purchases::ProductPurchase.find_by(provider_id: session_id)
        tenant = purchase.tenant
        MultiTenantSupport.under_tenant tenant do
          purchase.verify!
          subscriptions = purchase.subscriptions
          subscriptions.update_all(stripe_subscription_id:)
          purchase.handle_payment_completed
        end

      when 'invoice.paid'
        stripe_customer_id = event.data.object.customer
        stripe_subscription_id = event.data.object.subscription

        student = Student.find_by(stripe_customer_id:)
        tenant = student.tenant

        MultiTenantSupport.under_tenant tenant do
          payment_method = Purchases::PaymentMethod.find_by(name: 'Stripe')
          Stripe.api_key = payment_method.config['api_key']

          stripe_subscription = Stripe::Subscription.retrieve(stripe_subscription_id)
          expiration_date = Time.at(stripe_subscription.current_period_end).to_date
          
          subscriptions = Subscriptions::Subscription.where(stripe_subscription_id:)
          subscriptions.update_all(expiration_date:)
          student.update!(subscription_failed: nil)

          purchase = subscriptions.first.product_purchases.first
          # purchase.handle_payment_completed
          unless purchase.created_at > 1.day.ago
            new_purchase = purchase.dup
            new_purchase.amount_cents = event.data.object.amount_paid
            new_purchase.amount_currency = event.data.object.currency.upcase!
            new_purchase.save!
          end
        end
      when 'invoice.payment_failed'
        stripe_customer_id = event.data.object.customer
        stripe_subscription_id = event.data.object.subscription
        student = Student.find_by(stripe_customer_id:)
        tenant = student.tenant

        MultiTenantSupport.under_tenant tenant do
          payment_method = Purchases::PaymentMethod.find_by(name: 'Stripe')
          Stripe.api_key = payment_method.config['api_key']

          stripe_subscription = Stripe::Subscription.retrieve(stripe_subscription_id)
          stripe_date = Time.at(stripe_subscription.current_period_end).to_date

          subscriptions = Subscriptions::Subscription.where(stripe_subscription_id:)
          subscriptions.update_all(expiration_date: stripe_date + 2.days)

          purchase = subscriptions.first.product_purchases.first
          subscription_failed = { name: purchase.product.name, provider: purchase.provider}
          
          student.update(subscription_failed:)
        end
      else
        puts "Unhandled event type: #{event.type}"
      end

      render status: :ok
    end
  end

  # this endpoint is only for Stripe Connect account events
  def stripe_connect_user
    ::Stripe.api_key = ENV.fetch('STRIPE_CONNECT_API_KEY', '')
    payload = request.body.read
    event = nil

    event = Stripe::Event.construct_from(JSON.parse(payload, symbolize_names: true))

    MultiTenantSupport.turn_off_protection do
      case event.type
      when 'charge.dispute.created'

        binding.pry
        # dispute = event.data.object
        # charge_id = dispute.charge
        # # connected_account_id = event.account
        # disputed_amount_cents = dispute.amount
        # dispute_fee_cents = dispute.balance_transactions.first&.fee || 1500 # fallback if not found

        # # Find the purchase and tenant
        # charge = Stripe::Charge.retrieve({ id: charge_id }, { stripe_account: connected_account_id })
        # payment_intent_id = charge.payment_intent
        # purchase = ::Purchases::ProductPurchase.find_by(provider_id: dispute.payment_intent)

        # unless purchase
        #   Rails.logger.error("Dispute: Purchase not found for payment_intent #{payment_intent_id}")
        #   return
        # end

        # MultiTenantSupport.under_tenant purchase.tenant do
        #   # Cancel all related subscriptions
        #   subscription = purchase.subscriptions.first
        #   # subscriptions.each do |subscription|
        #     begin
        #       Stripe::Subscription.update(
        #         subscription.stripe_connect_subscription_id,
        #         { cancel_at_period_end: true },
        #         { stripe_account: connected_account_id }
        #       )
        #       subscription.update!(canceled: true)
        #     rescue => e
        #       Rails.logger.error("Dispute: Failed to cancel subscription: #{e.message}")
        #     end
        #   # end

        #   # Transfer the dispute fee back to platform
        #   begin
        #    response =  Stripe::Transfer.create({
        #       amount: dispute_fee_cents,
        #       currency: dispute.currency,
        #       destination: "sk_test_51PlTRgHZEJ07VaJ0qY2mHP9ZKnUFFWy54RFOQuZubZ6ndq0AtFlOoiXiAMzjTOpiaFDC9Ht8DjurwnBvo68gmsgG006l37O0gb",
        #       description: "Transfer dispute fee for dispute #{dispute.id}",
        #       transfer_group: "dispute_#{dispute.id}"
        #     }, {
        #       stripe_account: 'acct_1RpkQ1HYwayoogSE'
        #     })
        #   rescue => e
        #     Rails.logger.error("Dispute: Transfer of dispute fee failed: #{e.message}")
        #   end
        # end
      when 'charge.dispute.funds_withdrawn'
        binding.pry
      when 'charge.dispute.funds_reinstated'
        binding.pry

      when 'charge.dispute.closed'
        binding.pry
        dispute = event.data.object
        dispute_fee_cents = dispute.balance_transactions.first&.fee
        purchase = ::Purchases::ProductPurchase.find_by(provider_id: dispute.payment_intent)
        MultiTenantSupport.under_tenant purchase.tenant do

          subscription = purchase.subscriptions.first
          if dispute.status == 'lost'
            Stripe::Subscription.update(
                    subscription.stripe_connect_subscription_id,
                    { cancel_at_period_end: true },
                    { stripe_account: connected_account_id }
                  )
            subscription.update!(status: 'cancelled')
          end

          response =  Stripe::Transfer.create({
                amount: dispute_fee_cents,
                currency: dispute.currency,
                destination: Stripe::Account.retrieve.id,
                description: "Transfer dispute fee for dispute #{dispute.id}",
                transfer_group: "dispute_#{dispute.id}"
              }, {
                stripe_account: 'acct_1RpkQ1HYwayoogSE'
          })
        end

      when 'payment_intent.succeeded'
        payment_intent_id = event.data.object.id 
        invoice_id = event.data.object.invoice     
        purchase = ::Purchases::ProductPurchase.find_by(provider_id: payment_intent_id)

        MultiTenantSupport.under_tenant purchase.tenant do
          purchase.verify!
          if invoice_id.present?
            invoice = Stripe::Invoice.retrieve(invoice_id)
            stripe_connect_subscription_id = invoice.subscription
            subscriptions = purchase.subscriptions
            subscriptions.update_all(stripe_connect_subscription_id:)
          end
        end

        purchase.handle_payment_completed
        if purchase.product_type == 'Courses::Course' && purchase.product.is_available_on_affiliate
          purchase.distribute_commision_to_affilate
        end
      when 'setup_intent.succeeded'
        setup_intent_id = event.data.object.id
        purchase = ::Purchases::ProductPurchase.find_by(provider_id: setup_intent_id)
        stripe_connect_subscription_id = purchase.provider_data['initiate_response_data']['id'] 

        MultiTenantSupport.under_tenant purchase.tenant do
          purchase.verify!
          if stripe_connect_subscription_id.present?
            subscriptions = purchase.subscriptions
            subscriptions.update_all(stripe_connect_subscription_id:)
          end
        end

        purchase.handle_payment_completed
        # if purchase.product_type == 'Courses::Course' && purchase.product.is_available_on_affiliate
        #   purchase.distribute_commision_to_affilate
        # end
      when 'checkout.session.completed'
        session_id = event.data.object.id        
        stripe_connect_subscription_id = event.data.object.subscription

        purchase = ::Purchases::ProductPurchase.find_by(provider_id: session_id)
        tenant = purchase.tenant
        MultiTenantSupport.under_tenant tenant do
          purchase.verify!
          subscriptions = purchase.subscriptions
          subscriptions.update_all(stripe_connect_subscription_id:)
        end


        product = purchase.product
        purchase.handle_payment_completed
        if purchase.product_type == 'Courses::Course' && product.is_available_on_affiliate
          purchase.distribute_commision_to_affilate
        end

      when 'invoice.paid'
        stripe_connect_customer_id = event.data.object.customer
        stripe_connect_subscription_id = event.data.object.subscription

        student = Student.find_by(stripe_connect_customer_id:)
        tenant = student.tenant

        MultiTenantSupport.under_tenant tenant do
          subscriptions = Subscriptions::Subscription.where(stripe_connect_subscription_id:)
          if subscriptions.count.zero? && event.data.object.amount_paid.to_i <= 0
            return
          end

          payment_method = Purchases::PaymentMethodStripeConnect.last
          ::Stripe.api_key = ENV.fetch('STRIPE_CONNECT_API_KEY', '')

          stripe_subscription = Stripe::Subscription.retrieve(stripe_connect_subscription_id)
          expiration_date = Time.at(stripe_subscription.current_period_end).to_date

          subscriptions.update_all(expiration_date:)
          student.update!(subscription_failed: nil)

          purchase = subscriptions.first.product_purchases.first
          # purchase.handle_payment_completed
          unless purchase.created_at > 1.day.ago
            new_purchase = purchase.dup
            new_purchase.amount_cents = event.data.object.amount_paid
            new_purchase.amount_currency = event.data.object.currency.upcase!
            new_purchase.save!
          end
        end
      when 'invoice.payment_failed'
        stripe_connect_customer_id = event.data.object.customer
        stripe_connect_subscription_id = event.data.object.subscription
        student = Student.find_by(stripe_connect_customer_id:)
        tenant = student.tenant

        MultiTenantSupport.under_tenant tenant do
          payment_method = Purchases::PaymentMethodStripeConnect.last
          ::Stripe.api_key = ENV.fetch('STRIPE_CONNECT_API_KEY', '')

          options = {}
          options[:stripe_account] = payment_method&.account_id

          stripe_subscription = Stripe::Subscription.retrieve(stripe_connect_subscription_id)
          stripe_date = Time.at(stripe_subscription.current_period_end).to_date

          subscriptions = Subscriptions::Subscription.where(stripe_connect_subscription_id:)
          subscriptions.update_all(expiration_date: stripe_date + 2.days)

          purchase = subscriptions.first.product_purchases.first
          subscription_failed = { name: purchase.product.name, provider: purchase.provider}
          
          student.update(subscription_failed:)
        end
      when 'payout.paid'
        payout = event.data.object
        connected_account_id = event['account'] 
        payment_method = Affiliates::PaymentMethodStripeConnect.where("config->>'account_id' = ?", connected_account_id).first
        affiliate = Affiliate.find_by(id: payment_method.user_id)
        message = "Your payout of #{payout.currency} #{(payout.amount / 100.0).round(2)} has been successfully processed and sent to your bank account."

        NotificationsManager.push_notification(
          [affiliate.guid],
          {
            type: "AFFILIATE_PAYOUT",
            message: message,
            extras: {
              affiliate_id: affiliate.id,
            }
          }
        )
      when 'payout.failed'

      else
        puts "Unhandled event type: #{event.type}"
      end

      render status: :ok
    end
  end

  def stripe
    Stripe.api_key = Rails.application.credentials.stripe.api_secret
    endpoint_secret = ENV['STRIPE_WEBHOOKS_SECRET']

    payload = request.body.read
    sig_header = request.headers['Stripe-Signature']
    event = nil
    
    begin
        event = Stripe::Webhook.construct_event(payload, sig_header, endpoint_secret)
    rescue JSON::ParserError => e
        # Invalid payload
        render status: :bad_request
        return
    rescue Stripe::SignatureVerificationError => e
        # Invalid signature
        render status: :bad_request
        return
    end

    tenant = nil
    product = nil

    session = nil
    invoice = nil

    # Handle the event
    # For all events, we need to get the product and the customer

    pp event.type
    MultiTenantSupport.turn_off_protection do
      case event.type

      #cuando actualizamos el metodo de pago desde stripe
      when 'customer.updated'
        customer = event.data.object
        default_pm = customer.invoice_settings.default_payment_method
        tenant = Tenant.find_by(stripe_customer_id: customer.id)
        if tenant.nil?
          super_admin = SuperAdmin.joins(:tenant).includes(:tenant).find_by(email: event.data.object.email)
          tenant = super_admin.tenant
          tenant.update!(stripe_customer_id: customer.id)
        end
        if default_pm.present? && default_pm != tenant.default_stripe_payment_method
          tenant.get_stripe_payment_methods(default_pm)
        end 

      when 'payment_method.attached'
        customer_id = event.data.object.customer
        tenant = Tenant.find_by(stripe_customer_id: customer_id)
        if tenant.nil?
          super_admin = SuperAdmin.joins(:tenant).includes(:tenant).find_by(email: event.data.object.billing_details.email)
          tenant = super_admin.tenant
          tenant.update!(stripe_customer_id: event.data.object.customer)
        end
        tenant.set_default_payment_method(event.data.object.id)

      when 'payment_method.detached'
        customer_id = event.data.previous_attributes.customer
        tenant = Tenant.find_by(stripe_customer_id: customer_id)
        tenant.get_default_payment_method

      when 'invoice.paid'
        invoice = event.data.object
        tenant = Tenant.find_by(stripe_customer_id: invoice.customer)
        if tenant.nil?
          super_admin = SuperAdmin.joins(:tenant).includes(:tenant).find_by(email: invoice.customer_email)
          tenant = super_admin.tenant
          tenant.update!(stripe_customer_id: invoice.customer)
        end

        item = invoice.lines.data[0]
        subscription = Stripe::Subscription.retrieve(item.subscription)
        Rollbar.scope!(invoice:, subscription:)

        plan = subscription.plan.metadata.plan_id
        feature_plan_expiration_date = Time.at(subscription.current_period_end).to_date
        tenant.set_plan!(plan, feature_plan_expiration_date:, is_trial: false)

        MultiTenantSupport.under_tenant tenant do
          tenant.create_invoice(invoice)
          # if user subscribed to bunny then migrate data from shared library to specific library
          tenant.migrate_bunny_data if tenant.is_bunny_shared_library? || tenant.bunny_video_library_id.nil?
        end

        subscription_type = subscription.plan.interval == "year" ? "yearly" : "monthly"

        tenant.update(disabled: false)
        tenant&.tenant_metric&.update(first_subscription_date: Date.today) unless tenant.tenant_metric.first_subscription_date.present?
        tenant&.tenant_metric&.update(is_customer: true, subscription_type: subscription_type, subscription_date: Date.today)
      when 'invoice.payment_failed'
        invoice = event.data.object
        tenant = Tenant.find_by(stripe_customer_id: invoice.customer)
        if tenant.nil?
          super_admin = SuperAdmin.joins(:tenant).includes(:tenant).find_by(email: invoice.customer_email)
          tenant = super_admin.tenant
          tenant.update!(stripe_customer_id: invoice.customer)
        end

        MultiTenantSupport.under_tenant tenant do
          tenant.create_invoice(invoice)
          if invoice.payment_intent
            payment_intent = Stripe::PaymentIntent.retrieve(invoice.payment_intent)
            failure_reason = payment_intent.last_payment_error ? payment_intent.last_payment_error.message : "Unknown reason"
          else
            failure_reason = "Unknown reason"
          end
          PaymentMailer.payment_failed(tenant, failure_reason).deliver_later
        end
      when 'customer.subscription.updated'
        subscription = event.data.object
        tenant = Tenant.find_by(stripe_customer_id: subscription.customer)

        if tenant.nil?
          super_admin = SuperAdmin.joins(:tenant).includes(:tenant).find_by(email: subscription.customer_email)
          tenant = super_admin.tenant
          tenant.update!(stripe_customer_id: subscription.customer)
        end

        if subscription.cancel_at_period_end || subscription.status == 'unpaid'
          unless active_subscription_for_customer(subscription.customer).present?
            tenant&.tenant_metric.update(is_customer: false, subscription_type: :not_subscribed)
          end
        end
      when 'customer.subscription.deleted'
        subscription = event.data.object
        tenant = Tenant.find_by(stripe_customer_id: subscription.customer)

        if tenant.nil?
          super_admin = SuperAdmin.joins(:tenant).includes(:tenant).find_by(email: subscription.customer_email)
          tenant = super_admin.tenant
          tenant.update!(stripe_customer_id: subscription.customer)
        end

        unless active_subscription_for_customer(subscription.customer).present?
          tenant&.tenant_metric.update(is_customer: false, subscription_type: :not_subscribed)
        end
      when 'invoice.updated'
        invoice = event.data.object
        if invoice.status == 'void'
          tenant = fetch_tenant_by(invoice.customer, invoice.customer_email)
          MultiTenantSupport.under_tenant tenant do
            tenant.create_invoice(invoice)
          end
        end
      # ... handle other event types
      else
        puts "Unhandled event type: #{event.type}"
      end
    end

    render status: :ok
  end

  def zapier
    api_key = params[:api_key]
    pp api_key
    t = Tenant.find_by(api_key:)
    without_tenant_protection do
      render status: :ok, json: { api_key:, email: t.super_admin.email }
    end
  end

  def payu
    verify_data = params.permit(params.keys).to_h
    sub_domain = params[:subdomain]
    reference = verify_data[:reference_sale]
    t = Tenant.find_by(subdomain:)
    
    MultiTenantSupport.under_tenant t do
      purchase = ::Purchases::ProductPurchase.find_by(provider_id: reference)
      purchase.verify!(verify_data)
      purchase.handle_payment_completed
    end
  end

  def mercadopago
    mercado_pago_application_id = params["application_id"]
    t = Tenant.find_by(mercado_pago_application_id:)
    
    if t.nil?
      return render status: :ok, json: { data:  params }
    end

    MultiTenantSupport.under_tenant t do
      #token de mercado pago
      token = Purchases::PaymentMethod.find_by(name: 'MercadoPago').secret_access_token
      headers = {'Authorization' => "Bearer #{token}"}

      if params["type"] === "subscription_preapproval"
        preapproval_id = params["data"]["id"]
        purchase = ::Purchases::ProductPurchase.where(provider_id: preapproval_id).first

        # Luego de suscribirse, el pago de la la primera cuota se acreditará en 1 hora.
        if purchase.created_at > 4.hours.ago
          purchase.verify!
        end 
        purchase.handle_payment_completed
      end

      # pagos automaticos mensuales, se renueva la suscripcion 
      if params["type"] === "subscription_authorized_payment"
        authorized_payment_id = params["data"]["id"]
        subscription_authorized_payment = HTTParty.get("https://api.mercadopago.com/authorized_payments/#{authorized_payment_id}", 
                                                        :headers => headers)
        
        provider_id  = subscription_authorized_payment["preapproval_id"]
        payment = subscription_authorized_payment["payment"]
        debit_date = subscription_authorized_payment["debit_date"]
        
        # la primera compra de la suscripcion de mercadopago
        purchase = ::Purchases::ProductPurchase.where(provider_id:).first
        subscriptions = purchase.subscriptions
        student = subscriptions.student

        #pago aprobado
        if payment["status"] === "approved"
          #confirmar el primer cobro (despues de una hora)
          if purchase.created_at > 4.hours.ago
            purchase.update!(status: 'approved')
          else
            #este es pago mensual
            # crear nuevo registro de compra
            new_purchase = purchase.dup
            new_purchase.save! 
          end
          # agregar un mes de acceso al curso
          subscriptions.update_all(expiration_date: Time.zone.today + 1.months)
          student.update!(subscription_failed: nil)
          purchase.handle_payment_completed
        end

        # En el caso de que no se pueda cobrar la cuota en el cuarto reintento,
        # la cuota automáticamente quedará en el estado processed asociada a un pago rechazado.

        # Luego de 3 cuotas con pagos rechazados se da de baja automáticamente la suscripción 
        # y la cuenta del vendedor será notificada de la cancelación de la suscripción por e-mail.

        # pago rechazado o cancelado
        if payment["status"] === "rejected" || payment["status"] === "cancelled"
          if purchase.created_at > 4.hours.ago
            purchase.update!(status: 'rejected')
          end
          subscriptions.update_all(expiration_date: Time.zone.today + 2.days)
          subscription_failed = {
            name: purchase.product.name,
            provider: purchase.provider,
            url: "https://www.mercadopago.com.pe/subscriptions",
            subscription_id: provider_id,
            expiration: Time.zone.today + 2.days
          }
          student.update!(subscription_failed:)
        end
      end
    end
    render status: :ok, json: { data:  params }
  end

  def pay_pal
    data = JSON.parse(request.body.read)

    MultiTenantSupport.turn_off_protection do   
      if data["event_type"] == "PAYMENT.SALE.COMPLETED"
        subscription_id = data["resource"]["billing_agreement_id"] 
        status = data["resource"]["state"]
        amount = data["resource"]["amount"]["total"] #validamos que no sea el pago de costo de instalacion
  
        purchase = ::Purchases::ProductPurchase.where(provider_id: subscription_id).first
        subscriptions = purchase.subscriptions
        student = subscriptions.first.student
  
        if purchase.present? && status == "completed" && purchase.payment_type == "subscription" && amount != "2.00"
          if purchase.created_at < 4.hours.ago
            new_purchase = purchase.dup
            new_purchase.save! 
          end
          subscriptions.update_all( status: 'active', expiration_date: Time.zone.today + 1.months) 
          student.update!(subscription_failed: nil)
        end
        purchase.handle_payment_completed
      end

      if data["event_type"] == "BILLING.SUBSCRIPTION.PAYMENT.FAILED"
        subscription_id = data["resource"]["id"] 
  
        purchase = ::Purchases::ProductPurchase.where(provider_id: subscription_id).first
        subscriptions = purchase.subscriptions
        if purchase.created_at > 4.hours.ago
          purchase.update!(status: 'rejected')
        end
        subscriptions.update_all(expiration_date: Time.zone.today + 2.days)
        subscription_failed = {
          name: purchase.product.name,
          provider: purchase.provider,
          url: "https://www.paypal.com/myaccount/autopay/",
          subscription_id: subscription_id,
          expiration: Time.zone.today + 2.days
        }
        student.update!(subscription_failed:)
      end
    end 

    return render status: :ok
  end

  def postmark
    tag = params[:Tag]
    MultiTenantSupport.turn_off_protection do
      tenant = Tenant.find(tag)
      MultiTenantSupport.under_tenant tenant do
        email_id = params[:Metadata][:email_id]
        Emails::Email.increment_counter(:delivered, email_id) if params[:RecordType].eql? 'Delivery' # entregado
        Emails::Email.increment_counter(:bounced, email_id) if params[:RecordType].eql? 'Bounce'
      end
    end
    
    render status: :ok
  end

  def stripe_account
    Stripe.api_key = ENV.fetch('STRIPE_CONNECT_API_KEY', '')
    # endpoint_secret = 'we_1PlrFdHZEJ07VaJ0SzNDzUMB'
    payload = request.body.read
    # sig_header = request.headers['Stripe-Signature']
    event = nil
    
    begin
    event = Stripe::Event.construct_from(JSON.parse(payload, symbolize_names: true))
    # event = Stripe::Webhook.construct_event(payload, sig_header, endpoint_secret)

    account = event.data.object

    rescue Stripe::SignatureVerificationError => e
      return render status: :bad_request
    end

    case event.type
    when 'account.updated'
      if account.present? && account.charges_enabled && account.payouts_enabled
        tenant = Tenant.find_by(id: account.metadata&.tenant_id)
        MultiTenantSupport.under_tenant tenant do
          payment_method = Purchases::PaymentMethodStripeConnect.find_or_initialize_by(name: 'Sabionet Payments')
          payment_method.account_id = account.id
          payment_method.save
        end
      end
    else
      puts "Unhandled event type: #{event.type}"
    end
    render status: :ok
  end

  private

  def checkout_completed
    # Update the customer's plan and expiration date.
  end

  def recurring_payment_success
    # Update the customer's plan and expiration date.
  end

  def recurring_payment_failure
    # Notify the customer about the payment failure 
  end

  def active_subscription_for_customer(customer_id)
    Stripe::Subscription.list(customer: customer_id, status: 'active').data
  end

  def fetch_tenant_by(customer_id, customer_email)
    tenant = Tenant.find_by(stripe_customer_id: customer_id)

    if tenant.nil?
      super_admin = SuperAdmin.joins(:tenant).includes(:tenant).find_by(email: customer_email)
      tenant = super_admin.tenant
      tenant.update!(stripe_customer_id: customer_id)
    end
    tenant
  end
end