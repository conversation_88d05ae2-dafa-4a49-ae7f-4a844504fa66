class ApplicationController < ActionController::API
  include CustomSetUserByToken

  before_action :set_current_academy
  before_action :update_last_day_activity
  before_action :set_paper_trail_whodunnit
  before_action :configure_permitted_parameters, if: :devise_controller?
  around_action :switch_locale
  around_action :set_time_zone, if: :current_user

  def switch_locale(&action)
    locale = current_user&.language || current_tenant&.language || I18n.default_locale
    I18n.with_locale(locale, &action)
  rescue I18n::InvalidLocale => e
    I18n.with_locale('es', &action)
  end

  def info_for_paper_trail
    if current_tenant&.feature_plan&.audit_trail
      PaperTrail.request.enabled = true
    else
      PaperTrail.request.enabled = false
    end
    {
      tenant_id: current_tenant&.id,
      whodunnit_type: current_user&.type,
      whodunnit_name:  current_user&.full_name
    }
  end

  def set_current_academy
    if current_user.present? && current_tenant.present?
      current_user.current_academy = current_tenant
    end
  end

  def update_last_day_activity
    last_day_activity = Time.zone.now
    if current_user.present?
      if current_user.last_day_activity.nil? || current_user.last_day_activity < last_day_activity
        MultiTenantSupport.without_current_tenant do
          current_user.update(last_day_activity:)
        end

        if current_tenant.present?
          MultiTenantSupport.without_current_tenant do
            UserActivity.find_or_create_by(
              user_id: current_user.id,
              tenant_id: current_tenant.id,
              activity_date: last_day_activity.to_date
            )
          end
        end
      end
    end
  rescue ActiveRecord::RecordNotUnique, ActiveRecord::StaleObjectError
    p "Race condition detected. Skipping duplicate creation."
  end

  attr_reader :current_tenant

  # localhost:3000 is where the Rails server runs
  # localhost:8080 is where Node runs on development

  private
    def set_time_zone(&block)
      Time.use_zone(current_user.timezone, &block)
    end

    def configure_permitted_parameters
      devise_parameter_sanitizer.permit(:sign_up, keys: [:first_name, :last_name, :phone, custom_fields: {}])
    end

    def current_subdomain
      dev_subdomain = 'dipakdev'

      if request.headers['CubeJs-Origin'].present? 
        origin = request.headers['CubeJs-Origin']
      else
        origin = request.headers['Origin']
      end
      @current_subdomain ||=
        case origin
        when nil
        pp "No origin header was found, rejecting"
          render json: {
            errors: ["Missing required headers"],
          }, status: 400
        when 'http://centralized.sabionetlocal.com:8080', 'https://centralized.sabionet.com', 'https://centralized.sabiolms.com', 'http://onboarding.sabionetlocal.com:3001'
          ""
        when *Rails.application.config.custom_origin_mappings.keys
          Rails.application.config.custom_origin_mappings[origin]
        when 'http://localhost:8080', 'http://localhost:8081', 'http://localhost:3001','http://localhost:5173','http://localhost:9001', 'https://develop--lucid-wilson-0318f8.netlify.app/', /\Ahttps:\/\/deploy-preview-(\d+)--lucid-wilson-0318f8\.netlify\.app\z/
          pp "Test override for #{origin}. Will return #{dev_subdomain}"
          dev_subdomain
        when /\Ahttps:\/\/(.*?)\.sabionet\.com\z/, /\Ahttps:\/\/(.*?)\.sabiolms\.com\z/, 'https://demo.sabiotest.com'
          host = URI.parse(origin).host
          ActionDispatch::Http::URL.extract_subdomain(host, 1)
        when "https://sabiolms.com", 'http://localhost:3100', 'http://localhost:5000', "https://sabionet.com.br"
          'www'
        when /\Ahttp:\/\/(.*?)\.sabionetlocal\.com/
          host = URI.parse(origin).host
          ActionDispatch::Http::URL.extract_subdomain(host, 1)
        when "http://localhost:9000"
          'admin'
        else
          pp "Invalid origin #{origin}, rejecting"
          render json: {
            errors: ["Unknown origin #{origin}"],
          }, status: 400
        end
    end

    def find_current_tenant_account
      return if current_subdomain == 'www' || current_subdomain == 'admin'
      @current_tenant = Tenant.find_by(subdomain: current_subdomain)

      if @current_tenant.nil?
        host = URI.parse(request.headers['Origin']).host
        custom_domain = nil
        MultiTenantSupport.allow_read_across_tenant do
          custom_domain = CustomDomain.includes(:tenant).find_by(domain: host)
        end
        if custom_domain.present?
          @current_tenant = custom_domain.tenant
          Rails.application.config.allowed_origins.push("https://#{custom_domain.domain}")
          Rails.application.config.custom_origin_mappings.merge!({ "https://#{custom_domain.domain}" => @current_tenant.subdomain})
        end
      end
      @current_tenant # return current tenant
    end
end
