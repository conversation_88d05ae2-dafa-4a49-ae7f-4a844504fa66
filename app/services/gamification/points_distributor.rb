class Gamification::PointsDistributor

  def self.call(user:, action_type:)
    return unless user.is_a?(Student)

    setting = user.tenant.gamification_setting
    return unless user.tenant.gamification_enabled? && setting&.points_enabled?

    point_setting = setting.point_settings.find_by(action_type: action_type)
    return unless point_setting&.enabled?

    points = point_setting.points
    return if points.zero?
    
    user.create_gamification_user_profile(level_setting: setting.initial_level_setting) unless user.gamification_user_profile.present?
    user.reload.gamification_user_profile.add_points(points, action_type)
  rescue StandardError => e
    Rollbar.error(e)
  end
end
