class Gamification::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

  def self.call(user:, action_type:)
    return unless user.is_a?(Student)

    setting = user.tenant.gamification_setting
    return unless user.tenant.gamification_enabled? && setting&.badges_enabled?

    relevant_badge_conditions = get_relevant_badge_conditions(action_type)
    return if relevant_badge_conditions.empty?

    badge_settings = setting.badge_settings.enabled.where(condition_type: relevant_badge_conditions)

    badge_settings.each do |badge_setting|
      next if user.gamification_badges.include?(badge_setting)

      if badge_condition_met?(user, badge_setting, action_type)
        unlock_badge(user, badge_setting, setting)
      end
    end
  end

  private

  def self.get_relevant_badge_conditions(action_type)
    case action_type
    when 'course_completed'
      ['completed_1_course', 'completed_5_course', 'completed_12_course', 'completed_30_course']
    when 'exam_passed'
      ['scored_100_in_exam']
    when 'comment_on_lesson'
      ['commented_on_a_lesson']
    when 'comment_on_community'
      ['responded_to_question_or_post']
    when 'post_on_community'
      ['made_10_community_posts']
    when 'certificate_earned'
      ['earned_first_certificate','earned_20_certificates']
    when 'logged_10_consecutive_day'
      ['logged_10_consecutive_day']
    when 'course_reviewed'
      ['left_a_course_review']
    when 'recieved_reaction'
      ['received_10_reactions']
    else
      []
    end
  end

  def self.badge_condition_met?(user, badge_setting, action_type)
    case badge_setting.condition_type
    when 'completed_1_course'
      user.subscriptions.where.not(course: nil).where(completion_status: 'FINISHED').count >= 1
    when 'completed_5_course'
      user.subscriptions.where.not(course: nil).where(completion_status: 'FINISHED').count >= 5
    when 'completed_12_course'
      user.subscriptions.where.not(course: nil).where(completion_status: 'FINISHED').count >= 12
    when 'completed_30_course'
      user.subscriptions.where.not(course: nil).where(completion_status: 'FINISHED').count >= 30
    when 'scored_100_in_exam'
      action_type == 'exam_passed' && user.exam_attempts.where(status: 'passed', total_score: 1.0).exists?
    when 'earned_first_certificate'
      user.subscriptions.where.not(completion_certificate_data: nil).count >= 1
    when 'earned_20_certificates'
      user.subscriptions.where.not(completion_certificate_data: nil).count >= 20
    when 'commented_on_a_lesson'
      action_type == 'comment_on_lesson' && user.social_comments.where(commentable_type:'Courses::Lesson').count >=1
    when 'made_10_community_posts'
      user.my_community_posts.count >= 10
    when 'responded_to_question_or_post'
      action_type == 'comment_on_community' && user.social_comments.where(commentable_type:'Communities::CommunityPost').count >=1
    when 'logged_10_consecutive_day'
      check_consecutive_login_days(user, 10)
    when 'received_10_reactions'
      user.social_comments.joins(:reactions).count >= 10
    when 'left_a_course_review'
      user.subscriptions.joins(:student_review).count >= 1
    else
      false
    end
  end

  def self.unlock_badge(user, badge_setting, setting)
    badge = user.earned_badges.create!(
      badge_setting: badge_setting,
      earned_at: Time.current
    )

    user.create_gamification_user_profile(level_setting: setting.initial_level_setting) unless user.gamification_user_profile.present?

    if badge_setting.points > 0
      user.gamification_user_profile&.add_points(badge_setting.points, badge_setting.condition_type)
    end

  rescue StandardError => e
    Rails.logger.error("Error unlocking badge: #{e.message}")
  end

  def self.check_consecutive_login_days(user, required_days)
    end_date = Date.current
    start_date = end_date - (required_days - 1).days

    activities = user.user_activities
                     .where(activity_date: start_date..end_date)
                     .pluck(:activity_date)

    expected_dates = (start_date..end_date).to_a
    activities.sort == expected_dates
  end
end
