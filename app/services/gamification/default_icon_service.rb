module Gamification
  class DefaultIconService
    # Base URL
    BASE_ICON_URL = "/gamification"

    # Default level icons
    LEVEL_ICONS = {
      1 => "#{BASE_ICON_URL}/levels/level-1-novice.png",
      2 => "#{BASE_ICON_URL}/levels/level-2-explorer.png",
      3 => "#{BASE_ICON_URL}/levels/level-3-archmage.png",
      4 => "#{BASE_ICON_URL}/levels/level-4-oracle.png",
      5 => "#{BASE_ICON_URL}/levels/level-5-sage.png"
    }.freeze

    # Default badge icons
    BADGE_ICONS = {
      completed_1_course: "#{BASE_ICON_URL}/badges/completed-1-course.png",
      completed_5_course: "#{BASE_ICON_URL}/badges/completed-5-course.png",
      completed_12_course: "#{BASE_ICON_URL}/badges/completed-12-course.png",
      completed_30_course: "#{BASE_ICON_URL}/badges/completed-30-course.png",
      logged_10_consecutive_day: "#{BASE_ICON_URL}/badges/logged-10-consecutive-day.png",
      scored_100_in_exam: "#{BASE_ICON_URL}/badges/scored-100-in-exam.png",
      earned_first_certificate: "#{BASE_ICON_URL}/badges/earned-first-certificate.png",
      earned_20_certificates: "#{BASE_ICON_URL}/badges/earned-20-certificates.png",
      made_10_community_posts: "#{BASE_ICON_URL}/badges/made-10-community-posts.png",
      received_10_reactions: "#{BASE_ICON_URL}/badges/received-10-reactions.png",
      responded_to_question_or_post: "#{BASE_ICON_URL}/badges/responded-to-question-or-post.png",
      commented_on_a_lesson: "#{BASE_ICON_URL}/badges/commented-on-a-lesson.png",
      left_a_course_review: "#{BASE_ICON_URL}/badges/left-a-course-review.png"
    }.freeze

    # Fallback icons
    FALLBACK_LEVEL_ICON = "#{BASE_ICON_URL}/fallback/level-default.png"
    FALLBACK_BADGE_ICON = "#{BASE_ICON_URL}/fallback/badge-default.png"

    class << self
      def level_icon_url(level_number)
        icon_url = LEVEL_ICONS[level_number] || LEVEL_ICONS[1]
        # Check if the icon exists, fallback to placeholder if not
        icon_exists?(icon_url) ? icon_url : FALLBACK_LEVEL_ICON
      end

      def badge_icon_url(condition_type)
        icon_url = BADGE_ICONS[condition_type.to_sym] || BADGE_ICONS[:completed_1_course]
        # Check if the icon exists, fallback to placeholder if not
        icon_exists?(icon_url) ? icon_url : FALLBACK_BADGE_ICON
      end

      # Check if icon file exists in Rails public folder
      def icon_exists?(url)
        return false unless url.present?

        # Convert URL to local file path
        file_path = Rails.public_path.join(url.gsub(/^\//, ''))
        File.exist?(file_path)
      end

      def set_default_icon(record, icon_url)
        return unless icon_url.present?

        begin
          # Convert URL to local file path
          file_path = Rails.public_path.join(icon_url.gsub(/^\//, ''))

          if File.exist?(file_path)
            # Get file extension to determine content type
            file_extension = File.extname(file_path).downcase
            content_type = case file_extension
                          when '.png'
                            'image/png'
                          when '.jpg', '.jpeg'
                            'image/jpeg'
                          when '.gif'
                            'image/gif'
                          when '.svg'
                            'image/svg+xml'
                          else
                            'image/png' # default fallback
                          end

            # Create a temporary file with proper content type
            temp_file = Tempfile.new(['icon', file_extension])
            temp_file.binmode

            # Copy the file content
            File.open(file_path, 'rb') do |source_file|
              temp_file.write(source_file.read)
            end
            temp_file.rewind

            # Create an ActionDispatch::Http::UploadedFile with proper metadata
            uploaded_file = ActionDispatch::Http::UploadedFile.new(
              tempfile: temp_file,
              filename: File.basename(file_path),
              type: content_type,
              original_filename: File.basename(file_path)
            )

            # Assign to the record
            record.icon = uploaded_file
            record.save!

            # Clean up
            temp_file.close
            temp_file.unlink

            Rails.logger.info "Successfully set default icon for #{record.class.name} #{record.id} with content type #{content_type}"
          else
            Rails.logger.warn "Default icon file not found: #{file_path}"
          end
        rescue => e
          Rails.logger.error "Failed to set default icon for #{record.class.name} #{record.id}: #{e.message}"
        end
      end
    end
  end
end
