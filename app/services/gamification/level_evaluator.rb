class Gamification::LevelEvaluator
  def self.call(user:, profile:)
    return unless user.is_a?(Student)

    setting = user.tenant.gamification_setting
    return unless user.tenant.gamification_enabled?

    current_level = profile.level_setting
    available_levels = setting.level_settings.enabled.order(:position)

    new_level = available_levels.find do |level|
      profile.total_points >= level.min_points && profile.total_points <= level.max_points
    end

    if new_level.present? && current_level.present? && new_level.id != current_level.id
      old_level_position = current_level.position
      new_level_position = new_level.position

      if new_level_position > old_level_position
        profile.update!(level_setting: new_level)
      end
    elsif new_level.present? && current_level.blank?
      profile.update!(level_setting: new_level)
    end

    update_level_progress(profile)
  end

  private

  def self.update_level_progress(profile)
    return unless profile.level_setting.present?

    level = profile.level_setting
    level_range = level.max_points - level.min_points
    points_in_level = profile.total_points - level.min_points

    progress = if level_range > 0
                 ((points_in_level.to_f / level_range) * 100).round
               else
                 100
               end

    profile.update!(current_level_progress: [0, [progress, 100].min].max)
  end
end
