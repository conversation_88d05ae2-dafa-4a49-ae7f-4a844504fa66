class Gamification::RewardDistributor
  def self.call(user:)
    return unless user.is_a?(Student)

    setting = user.tenant.gamification_setting
    return unless user.tenant.gamification_enabled? && setting.present?

    reward_settings = setting.reward_settings.enabled

    reward_settings.each do |reward_setting|
      next if user_already_has_reward?(user, reward_setting)

      if reward_condition_met?(user, reward_setting)
        distribute_reward(user, reward_setting)
      end
    end
  end

  private

  def self.user_already_has_reward?(user, reward_setting)
    user.gamification_rewards.exists?(reward_setting: reward_setting)
  end

  def self.reward_condition_met?(user, reward_setting)
    case reward_setting.condition_type
    when 'points'
      check_points_condition(user, reward_setting)
    when 'level'
      check_level_condition(user, reward_setting)
    when 'badge'
      check_badge_condition(user, reward_setting)
    else
      false
    end
  end

  def self.check_points_condition(user, reward_setting)
    user_profile = user.gamification_user_profile
    user_profile.total_points >= reward_setting.condition_value
  end

  def self.check_level_condition(user, reward_setting)
    user_profile = user.gamification_user_profile
    user_profile.level_setting.position >= reward_setting.level_setting.position
  end

  def self.check_badge_condition(user, reward_setting)
    target_badge = reward_setting.badge_setting
    user.gamification_badges.include?(target_badge)
  end

  def self.distribute_reward(user, reward_setting)
    user_reward = user.gamification_rewards.create!(
      reward_setting: reward_setting,
      claimed_status: :pending
    )
  rescue StandardError => e
    Rails.logger.error("Error distributing reward: #{e.message}")
    nil
  end
end
