module Emails
  class BroadcastSchedulerService

    def initialize(broadcast:)
      @broadcast = broadcast
    end

    def schedule_broadcast
      case @broadcast.scheduled_type
      when 'send_now'
        send_immediately
      when 'send_later'
        schedule_for_later
      else
        raise ArgumentError, "Invalid scheduled_type: #{@broadcast.scheduled_type}"
      end
    end

    private

    def send_immediately
      Emails::SendBroadcastJob.perform_later(@broadcast.id)
    end

    def schedule_for_later
      return unless @broadcast.scheduled_at.present?

      if @broadcast.scheduled_at <= Time.current
        raise ArgumentError, "Scheduled time must be in the future"
      end
      job = Emails::SendBroadcastJob.set(wait_until: @broadcast.scheduled_at).perform_later(@broadcast.id)
      @broadcast.update!(sidekiq_id: job.provider_job_id) if job.provider_job_id
    end
  end
end
