class StripeConnectDisputeService
  def self.handle_dispute_created(dispute_data)
    ::Stripe.api_key = ENV.fetch('STRIPE_CONNECT_API_KEY', '')

    dispute_id = dispute_data['id']
    charge_id = dispute_data['charge']
    amount = dispute_data['amount']
    reason = dispute_data['reason']

    begin
      transaction = find_transaction_by_charge(charge_id)

      unless transaction
        Rails.logger.error "No transaction found for disputed charge #{charge_id}"
        return { success: false, message: "Transaction not found" }
      end

      store_dispute_info(transaction, dispute_data)

      reverse_result = attempt_transfer_reversal(transaction, dispute_data)

      record_dispute_fee_liability(transaction, dispute_data)

      {
        success: true,
        message: "Dispute handled successfully",
        transaction_id: transaction.id,
        reversal_attempted: reverse_result[:attempted],
        reversal_success: reverse_result[:success]
      }

    rescue => e
      Rails.logger.error "Error handling dispute #{dispute_id}: #{e.message}"
      { success: false, message: e.message }
    end
  end

  def self.handle_dispute_updated(dispute_data)
    ::Stripe.api_key = ENV.fetch('STRIPE_CONNECT_API_KEY', '')

    dispute_id = dispute_data['id']
    status = dispute_data['status']

    transaction = find_transaction_by_dispute_id(dispute_id)
    return unless transaction

    case status
    when 'won'
      handle_dispute_won(transaction, dispute_data)
    when 'lost'
      handle_dispute_lost(transaction, dispute_data)
    when 'warning_closed'
      handle_dispute_warning_closed(transaction, dispute_data)
    end
  end

  private

  def self.find_transaction_by_charge(charge_id)
    charge = Stripe::Charge.retrieve(charge_id)
    payment_intent_id = charge.payment_intent

    transaction = Purchases::ProductPurchase.find_by(provider_id: payment_intent_id)

    if !transaction && charge.metadata['transaction_id']
      transaction = Purchases::ProductPurchase.find_by(id: charge.metadata['transaction_id'])
    end

    # Try finding by subscription metadata
    if !transaction && charge.invoice
      invoice = Stripe::Invoice.retrieve(charge.invoice)
      if invoice.subscription
        subscription = Stripe::Subscription.retrieve(invoice.subscription)
        if subscription.metadata['transaction_id']
          transaction = Purchases::ProductPurchase.find_by(id: subscription.metadata['transaction_id'])
        end
      end
    end
    transaction
  end

  def self.find_transaction_by_dispute_id(dispute_id)
    Purchases::ProductPurchase.where("provider_data->'dispute'->>'dispute_id' = ?", dispute_id).first
  end

  def self.store_dispute_info(transaction, dispute_data)
    transaction.provider_data = transaction.provider_data || {}
    transaction.provider_data['dispute'] = {
      dispute_id: dispute_data['id'],
      charge_id: dispute_data['charge'],
      amount: dispute_data['amount'],
      reason: dispute_data['reason'],
      status: dispute_data['status'],
      created_at: Time.current.iso8601,
      evidence_due_by: dispute_data.evidence_details&.due_by
    }
    transaction.verification_status = 'disputed'
    transaction.save!
  end

  def self.attempt_transfer_reversal(transaction, dispute_data)
    transfer_data = transaction.provider_data['transfer']

    unless transfer_data && transfer_data['transfer_id']
      Rails.logger.warn "No transfer found to reverse for transaction #{transaction.id}"
      return { attempted: false, success: false, message: "No transfer to reverse" }
    end

    transfer_id = transfer_data['transfer_id']
    dispute_amount = dispute_data['amount']

    begin
      reversal = Stripe::Transfer.create_reversal(transfer_id, {
        description: "Dispute reversal for dispute #{dispute_data['id']}",
        metadata: {
          dispute_id: dispute_data['id'],
          transaction_id: transaction.id,
          reason: 'dispute_created'
        }
      })

      transaction.provider_data['dispute']['reversal'] = {
        reversal_id: reversal.id,
        amount: dispute_amount,
        created_at: Time.current.iso8601
      }
      transaction.save!


      {
        attempted: true,
        success: true,
        reversal_id: reversal.id,
        message: "Transfer reversed successfully"
      }

    rescue Stripe::StripeError => e
      Rails.logger.error "Failed to reverse transfer #{transfer_id}: #{e.message}"

      transaction.provider_data['dispute']['reversal_failed'] = {
        error: e.message,
        attempted_at: Time.current.iso8601
      }
      transaction.save!

      {
        attempted: true,
        success: false,
        message: "Transfer reversal failed: #{e.message}"
      }
    end
  end

  def self.handle_dispute_won(transaction, dispute_data)
    reversal_data = transaction.provider_data.dig('dispute', 'reversal')

    if reversal_data && reversal_data['reversal_id']
      transfer_funds_back(transaction, dispute_data, reversal_data)
    end

    # If we recorded a pending dispute fee, reverse that liability now
    fee_cents = transaction.provider_data.dig('dispute', 'fee_cents').to_i
    if fee_cents.positive?
      begin
        remove_pending_adjustment(transaction.payment_method, fee_cents, reason: 'dispute_fee', reference: dispute_data['id'])
      rescue => e
        Rails.logger.error "Failed to remove pending adjustment for dispute #{dispute_data['id']}: #{e.message}"
      end
    end

    transaction.provider_data['dispute']['status'] = 'won'
    transaction.provider_data['dispute']['resolved_at'] = Time.current.iso8601
    transaction.verification_status = 'approved'
    transaction.save!
  end

  def self.handle_dispute_lost(transaction, dispute_data)
    transaction.provider_data['dispute']['status'] = 'lost'
    transaction.provider_data['dispute']['resolved_at'] = Time.current.iso8601
    transaction.verification_status = 'disputed_lost'
    transaction.save!
  end

  def self.handle_dispute_warning_closed(transaction, dispute_data)
    # Treat as won - transfer funds back if reversed
    handle_dispute_won(transaction, dispute_data)
  end

  def self.transfer_funds_back(transaction, dispute_data, reversal_data)
    connected_account_id = transaction.payment_method.account_id
    amount = reversal_data['amount']

    begin
      transfer = Stripe::Transfer.create({
        amount: amount,
        currency: transaction.price.real_price.currency.iso_code.downcase,
        destination: connected_account_id,
        description: "Funds returned after winning dispute #{dispute_data['id']}",
        metadata: {
          dispute_id: dispute_data['id'],
          transaction_id: transaction.id,
          reason: 'dispute_won'
        }
      })

      transaction.provider_data['dispute']['return_transfer'] = {
        transfer_id: transfer.id,
        amount: amount,
        created_at: Time.current.iso8601
      }
      transaction.save!

      Rails.logger.info "Transferred #{amount} cents back to #{connected_account_id} after winning dispute"

    rescue Stripe::StripeError => e
      Rails.logger.error "Failed to transfer funds back after winning dispute: #{e.message}"

      transaction.provider_data['dispute']['return_transfer_failed'] = {
        error: e.message,
        attempted_at: Time.current.iso8601
      }
      transaction.save!
    end
  end

  # Record the dispute fee as a pending liability against the connected account
  def self.record_dispute_fee_liability(transaction, dispute_data)
    fee_cents = dispute_data.balance_transactions.map { |bt| bt.fee.to_i }.sum.abs
    return if fee_cents <= 0

    # Persist on transaction for traceability
    transaction.provider_data['dispute']['fee_cents'] = fee_cents
    transaction.save!

    payment_method = transaction.payment_method

    add_pending_adjustment(payment_method, fee_cents, reason: 'dispute_fee', reference: dispute_data['id'])
  rescue => e
    Rails.logger.error "Failed to record dispute fee liability: #{e.message}"
  end

  def self.add_pending_adjustment(payment_method, cents, reason:, reference:)
    cfg = payment_method.config || {}
    pending = cfg['pending_adjustment_cents'].to_i
    cfg['pending_adjustment_cents'] = pending + cents
    adjustments = cfg['adjustments'] || []
    adjustments << {
      'type' => reason,
      'cents' => cents,
      'reference' => reference,
      'created_at' => Time.current.iso8601
    }
    cfg['adjustments'] = adjustments
    payment_method.update!(config: cfg)
  end

  def self.remove_pending_adjustment(payment_method, cents, reason:, reference:)
    cfg = payment_method.config || {}
    pending = cfg['pending_adjustment_cents'].to_i
    cfg['pending_adjustment_cents'] = [pending - cents, 0].max
    adjustments = cfg['adjustments'] || []
    adjustments << {
      'type' => "#{reason}_reversal",
      'cents' => -cents,
      'reference' => reference,
      'created_at' => Time.current.iso8601
    }
    cfg['adjustments'] = adjustments
    payment_method.update!(config: cfg)
  end

  # Utility method to get dispute status
  def self.get_dispute_status(dispute_id)
    ::Stripe.api_key = ENV.fetch('STRIPE_CONNECT_API_KEY', '')

    begin
      dispute = Stripe::Dispute.retrieve(dispute_id)
      {
        id: dispute.id,
        status: dispute.status,
        reason: dispute.reason,
        amount: dispute.amount,
        currency: dispute.currency,
        evidence_due_by: dispute.evidence_details.due_by,
        created: dispute.created
      }
    rescue Stripe::StripeError => e
      { error: e.message }
    end
  end
end
