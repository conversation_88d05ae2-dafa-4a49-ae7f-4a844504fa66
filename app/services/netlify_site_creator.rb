class NetlifySiteCreator
  attr_reader :api_token

  def initialize
    @api_token = ENV['NETLIFY_API_TOKEN']
  end

  def create_new_site_and_record(domain)
    return { success: false, error: "Master API token not configured" } unless api_token.present?

    site_name = generate_unique_site_name

    netlify_result = NetlifyConcern::NetlifyAPIService.create_new_site(site_name, api_token, domain)

    unless netlify_result[:success]
      return { success: false, error: "Failed to create Netlify site: #{netlify_result[:error]}" }
    end

    begin
      netlify_site = NetlifySite.create!(
        site_id: netlify_result[:site_id],
        site_name: netlify_result[:site_name],
        active: true,
        metadata: {
          netlify_url: netlify_result[:url],
          ssl_url: netlify_result[:ssl_url]
        }
      )

      {
        success: true,
        netlify_site: netlify_site,
        message: "New Netlify site created successfully",
      }

    rescue ActiveRecord::RecordInvalid => e      
      { success: false, error: "Failed to save site record: #{e.message}" }
    end
  end

  private

  def generate_unique_site_name
    timestamp = Time.current.strftime("%Y%m%d%H%M%S")
    random_suffix = SecureRandom.hex(4)
    
    "custom-domains-#{random_suffix}-#{timestamp}"
  end
end
