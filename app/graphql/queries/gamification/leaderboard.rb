module Queries
  module Gamification
    class Leaderboard < GraphQL::Schema::Resolver
      description "Get leaderboard rankings"

      type Types::Gamification::LeaderboardEntryType.connection_type, null: false

      argument :limit, Integer, required: false, default_value: 10, description: "Number of top entries to return"
      argument :period, String, required: false, default_value: "monthly"
      argument :dashboard_view, <PERSON><PERSON>an, required: false

      def resolve(limit:, period: "monthly", dashboard_view: false)
        leaderboard_data = ::Gamification::UserPointsActivity
                            .leaderboard(period: period.to_sym, limit: limit)
                            .includes(user: { gamification_user_profile: :level_setting })

        entries = leaderboard_data.map.with_index(1) do |entry, index|
          OpenStruct.new(
            user: entry.user,
            total_points: entry.user.gamification_user_profile&.total_points || 0,
            rank: index
          )
        end

        if dashboard_view && context[:current_user].is_a?(Student) 
          unless entries.any? { |e| e.user.id == context[:current_user].id }
      
            current_user_rank = ::Gamification::UserPointsActivity.user_rank(context[:current_user], period: period.to_sym)
      
            if current_user_rank
              entries << OpenStruct.new(
                user: context[:current_user],
                total_points: context[:current_user].gamification_user_profile&.total_points || 0,
                rank: current_user_rank
              )
            end
          end
        end

        entries
      end
    end
  end
end
