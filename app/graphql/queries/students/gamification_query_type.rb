module Queries::Students
  module GamificationQueryType
    include Types::BaseInterface

    # field :gamification_setting, Types::Objects::Gamifications::GamificationSettingType

    # def gamification_setting
    #   context[:current_tenant].gamification_setting
    # end

    field :get_gamification_user_profile, Types::Objects::Gamifications::UserProfileType, null: false do
      argument :user_id, Integer, required: false
    end

    def get_gamification_user_profile(user_id: nil)
      return unless context[:current_user].present?

      if user_id.present?
        Gamification::UserProfile.find_by(user_id: user_id)
      else
        context[:current_user].gamification_user_profile
      end
    end

    field :get_gamification_rewards, Types::Objects::Gamifications::UserRewardType.connection_type, null: false

    def get_gamification_rewards
      context[:current_user].gamification_rewards
    end

    field :leaderboard_data, resolver: Queries::Gamification::Leaderboard

    field :user_badge_information, [Types::Objects::Gamifications::BadgeSettingType], null: false

    def user_badge_information
      return [] unless context[:current_user].present?
      return [] unless context[:current_tenant].gamification_setting.present?

      context[:current_tenant].gamification_setting.badge_settings
    end
  end
end
