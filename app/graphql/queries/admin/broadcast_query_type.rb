module Queries
  module Admin
    module <PERSON><PERSON>ueryType
      include Types::BaseInterface
      include UserFilterable

      field :broadcast_list, Types::Objects::Emails::BroadcastType.connection_type, null: false

      def broadcast_list
        context[:current_tenant].emails_broadcasts.order(created_at: :desc)
      end

      field :get_broadcast, Types::Objects::Emails::BroadcastType, null: false do
        argument :id, ID, required: true
      end

      def get_broadcast(id:)
        context[:current_tenant].emails_broadcasts.find(id)
      end

      field :broadcast_recipients, [Types::Interfaces::UserType], null: false do
        argument :filter_id, ID, required: false
        argument :filter_type, String, required: false
        argument :broadcast_id, ID, required: false
      end

      def broadcast_recipients(filter_id: nil, filter_type: nil, broadcast_id: nil)
        if broadcast_id.present?
          broadcast = context[:current_tenant].emails_broadcasts.find(broadcast_id)
          filter_type = broadcast.sent_to
        end
        users = context[:current_tenant].students
        if filter_id.present?
          user_filters = context[:current_tenant].user_filters.find(filter_id)
          filters = OpenStruct.new(user_filters.filters)
          users = apply_filters(users, filters) if filters.present?
        end

        users
      end
    end
  end
end
