module Queries
  module Admin
    module Broadcast<PERSON>ueryType
      include Types::BaseInterface
      include UserFilterable

      field :broadcasts, Types::Objects::Emails::BroadcastType.connection_type, null: false do
        argument :status, String, required: false
        argument :sent_to, String, required: false
      end

      def broadcasts(search: nil, status: nil, sent_to: nil)
        scope = context[:current_tenant].emails_broadcasts.includes(:signature, :template, :filter)

        scope = scope.where(status: status) if status.present?

        scope = scope.where(sent_to: sent_to) if sent_to.present?
        scope
      end

      field :broadcast, Types::Objects::Emails::BroadcastType, null: false do
        argument :id, ID, required: true
      end

      def broadcast(id:)
        context[:current_tenant].emails_broadcasts.includes(:signature, :template, :filter).find(id)
      end

      # field :broadcast_recipients, Types::Objects::UserType.connection_type, null: false do
      #   argument :filter_id, ID, required: false
      #   argument :filter_type, String, required: false
      #   argument :broadcast_id, ID, required: false
      # end

      # def broadcast_recipients(filter_id:, filter_type:, broadcast_id:)
      #   if broadcast_id.present?
      #     broadcast = context[:current_tenant].emails_broadcasts.find(broadcast_id)
      #     filter_type = broadcast.sent_to
      #   end
      #   users = context[:current_tenant].students
      #   if filter_type == 'list_with_filters'
      #     user_filters = context[:current_tenant].user_filters.find(filter_id)
      #     filters = user_filters.filters
      #     users = apply_filters(users, filters) if filters.present?
      #   end
      # end


    end
  end
end
