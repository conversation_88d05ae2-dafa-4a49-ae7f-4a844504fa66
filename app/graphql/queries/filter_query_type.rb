module Queries
  module Fi<PERSON><PERSON><PERSON>yType
    include Types::BaseInterface

    field :get_user_filters, [Types::Objects::FilterType], null: false do
      argument :list_type, String, required: false
    end

    field :get_user_filter, Types::Objects::FilterType, null: false do
      argument :id, ID, required: true
    end

    def get_user_filters(list_type: nil)
      filters = context[:current_tenant].user_filters.recent
      filters = filters.where(list_type: list_type) if list_type.present?
      filters
    end

    def get_user_filter(id:)
      context[:current_tenant].user_filters.find(id)
    end
  end
end
