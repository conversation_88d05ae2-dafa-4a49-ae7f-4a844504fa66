module Mutations
  class SetPlan < BaseMutation
    field :success, Bo<PERSON>an, null: false
    field :errors, [String], null: true
    field :tenant, Types::Objects::TenantType, null: false

    argument :subdomain, String, required: true
    argument :plan, String, required: true

    def resolve(subdomain:, plan:)
      tenant = Tenant.find_by(subdomain:)
      unless tenant.is_trial
        return { success: false, tenant:, errors: ["This tenant is not a trial"] }
      end
      tenant.feature_plan_id = plan

      tenant.feature_plan_expiration_date = nil if plan == 'free'
      tenant.save!
      { success: true, tenant:, errors: [] }
    end
  end
end
