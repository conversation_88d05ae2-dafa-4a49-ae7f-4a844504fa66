module Mutations
  class Websites::DeleteWebsiteFooterColumn < ::Mutations::BaseMutation
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: true

    argument :id, ID, required: true

    def resolve(id:)
      footer_column = fetch_website_footer_column(id)
      return { success:false, errors: ['Footer column Not Found'] } unless footer_column.present?

      if footer_column.destroy
        { success: true, errors: [] }
      else
        { success: false, errors: footer_column.errors.messages }
      end
    end
  end
end
