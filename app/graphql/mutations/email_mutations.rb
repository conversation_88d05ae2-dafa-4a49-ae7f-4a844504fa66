module Mutations
    module EmailMutations
      include Types::BaseInterface
      field :create_email, mutation: Mutations::Emails::CreateEmail
      field :update_email, mutation: Mutations::Emails::UpdateEmail
      field :delete_email, mutation: Mutations::Emails::DeleteEmail

      field :create_template, mutation: Mutations::Emails::CreateTemplate
      field :update_template, mutation: Mutations::Emails::UpdateTemplate
      field :delete_template, mutation: Mutations::Emails::DeleteTemplate

      field :update_automation, mutation: Mutations::Emails::UpdateAutomation
      field :delete_automation, mutation: Mutations::Emails::DeleteAutomation

      field :create_signature, mutation: Mutations::Emails::CreateSignature
      field :update_signature, mutation: Mutations::Emails::UpdateSignature
      field :delete_signature, mutation: Mutations::Emails::DeleteSignature
      field :resend_email_signature, mutation: Mutations::Emails::ResendEmailSignature

      # Filter mutations
      field :create_filter, mutation: Mutations::Emails::CreateFilter
      field :update_filter, mutation: Mutations::Emails::UpdateFilter
      field :delete_filter, mutation: Mutations::Emails::DeleteFilter

      # Broadcast mutations
      field :create_broadcast, mutation: Mutations::Emails::CreateBroadcast
      field :update_broadcast, mutation: Mutations::Emails::UpdateBroadcast
      field :delete_broadcast, mutation: Mutations::Emails::DeleteBroadcast
    end
end
