module Mutations
  class Emails::DeleteBroadcast < BaseMutation
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :message, String, null: true

    argument :id, ID, required: true

    def resolve(id:)
      broadcast = context[:current_tenant].emails_broadcasts.find(id)
      
      if broadcast.destroy
        {
          success: true,
          message: 'Broadcast deleted successfully'
        }
      else
        raise Sabiorealm::Error, 'Failed to delete broadcast'
      end
    end
  end
end
