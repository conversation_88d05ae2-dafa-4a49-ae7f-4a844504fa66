module Mutations
  class Emails::DeleteFilter < BaseMutation
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :message, String, null: true

    argument :id, ID, required: true

    def resolve(id:)
      filter = context[:current_tenant].user_filters.find(id)

      if filter.broadcasts.exists?
        raise Sabiorealm::Error, 'Cannot delete filter that is being used by broadcasts'
      end

      if filter.destroy
        {
          success: true,
          message: 'Filter deleted successfully'
        }
      else
        raise Sabiorealm::Error, 'Failed to delete filter'
      end
    end
  end
end
