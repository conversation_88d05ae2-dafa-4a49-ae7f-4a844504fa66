module Mutations
  class Emails::UpdateBroadcast < BaseMutation
    field :broadcast, Types::Objects::Emails::BroadcastType, null: false
    field :message, String, null: true

    argument :id, ID, required: true
    argument :broadcast_input, Types::Inputs::Emails::BroadcastInputType, required: true

    def resolve(id:, broadcast_input:)
      broadcast = context[:current_tenant].emails_broadcasts.find(id)

      if broadcast_input.sent_to == 'list_with_filters'
        unless broadcast_input.filter_id.present?
          raise Sabiorealm::Error, "Filter is required when 'sent_to' is 'list_with_filters'"
        end

        filter = context[:current_tenant].user_filters.find_by(id: broadcast_input.filter_id)
        unless filter
          raise Sabiorealm::Error, "Filter not found"
        end
      end

      if broadcast.update(**broadcast_input)
        {
          broadcast: broadcast,
          message: 'Broadcast updated successfully'
        }
      else
        raise Sabiorealm::Error, broadcast.errors.full_messages.join(', ')
      end
    end
  end
end
