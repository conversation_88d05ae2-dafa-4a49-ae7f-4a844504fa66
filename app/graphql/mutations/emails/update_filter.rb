module Mutations
  class Emails::UpdateFilter < BaseMutation
    field :filter, Types::Objects::FilterType, null: false
    field :message, String, null: true

    argument :id, ID, required: true
    argument :filter_input, Types::Inputs::FilterInputType, required: true

    def resolve(id:, filter_input:)
      filter = context[:current_tenant].user_filters.find(id)
      filter.update!(**filter_input)

      {
        filter: filter,
        message: 'Filter updated successfully'
      }
    end
  end
end
