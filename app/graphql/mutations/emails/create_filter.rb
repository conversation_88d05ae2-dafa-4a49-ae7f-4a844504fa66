module Mutations
  class Emails::CreateFilter < BaseMutation
    field :filter, Types::Objects::FilterType, null: false
    field :message, String, null: true

    argument :filter_input, Types::Inputs::FilterInputType, required: true

    def resolve(filter_input:)
      filter = context[:current_tenant].user_filters.new(**filter_input)
      filter.save!

      {
        filter: filter,
        message: 'Filter created successfully'
      }
    end
  end
end
