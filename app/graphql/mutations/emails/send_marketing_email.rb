module Mutations
  class Emails::SendMarketingEmail < BaseMutation
    field :broadcast, Types::Objects::Emails::BroadcastType, null: false
    field :message, String, null: true

    argument :id, ID, required: true
    argument :receive_copy, Bo<PERSON>an, required: true

    def resolve(id:, receive_copy:)
      broadcast = context[:current_tenant].emails_broadcasts.find(id)
      
      scheduler = Emails::BroadcastSchedulerService.new(broadcast, receive_copy: receive_copy)
      scheduler.schedule_broadcast
      
      {
        broadcast: broadcast.reload,
        message: 'Marketing email scheduled successfully'
      }
    end
  end
end
