module Mutations
  class Emails::CreateBroadcast < BaseMutation
    field :broadcast, Types::Objects::Emails::BroadcastType, null: false
    field :message, String, null: true

    argument :broadcast_input, Types::Inputs::Emails::BroadcastInputType, required: true

    def resolve(broadcast_input:)

      if broadcast_input.sent_to == 'list_with_filters'
        unless broadcast_input.filter_id.present?
          raise Sabiorealm::Error, "Filter is required when 'sent_to' is 'list_with_filters'"
        end

        filter = context[:current_tenant].user_filters.find_by(id: broadcast_input.filter_id)
        unless filter
          raise Sabiorealm::Error, "Filter not found"
        end
      end

      broadcast_email = ::Emails::Broadcast.new(**broadcast_input)
      broadcast_email.sender = context[:current_user] if broadcast_input.send_copy_to_sender

      if broadcast_email.save
        {
          broadcast: broadcast_email,
          message: 'Broadcast created successfully'
        }
      else
        raise Sabiorealm::Error, broadcast_email.errors.full_messages.join(', ')
      end
    end
  end
end
