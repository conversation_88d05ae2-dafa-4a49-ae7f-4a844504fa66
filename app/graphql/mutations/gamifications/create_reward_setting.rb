module Mutations
  class Gamifications::CreateRewardSetting < BaseMutation
    field :success, Bo<PERSON>an, null: false
    field :errors, [String], null: true
    field :reward_setting, Types::Objects::Gamifications::RewardSettingType, null: true

    argument :reward_setting_input, Types::Inputs::Gamifications::RewardSettingInputType, required: true

    def resolve(reward_setting_input:)
      reward_setting = ::Gamification::RewardSetting.new(
        tenant: context[:current_tenant],
        gamification_setting:  context[:current_tenant].gamification_setting,
        **reward_setting_input
      )
      
      if reward_setting.save
        {
          success: true,
          errors: [],
          reward_setting: reward_setting
        }
      else
        {
          success: false,
          errors: reward_setting.errors.full_messages,
          reward_setting: reward_setting
        }
      end
    end
  end
end
