module Mutations
  class Gamifications::UpdateGamificationSetting < BaseMutation
    field :success, Boolean, null: false
    field :errors, [String], null: true
    field :gamification_setting, Types::Objects::Gamifications::GamificationSettingType, null: false

    argument :gamification_setting_input, Types::Inputs::Gamifications::GamificationSettingInputType, required: true

    def resolve(gamification_setting_input:)
      gamification_setting = context[:current_tenant].gamification_setting
      return { success: false, errors: ['Gamification Setting Not Found'], gamification_setting: { } } unless gamification_setting.present?

      if gamification_setting.update!(**gamification_setting_input)
        { success: true, errors: [], gamification_setting: gamification_setting }
      else
        { success: false, errors: gamificationSettings.errors.full_messages, gamification_setting: gamification_setting }
      end
    end
  end
end
