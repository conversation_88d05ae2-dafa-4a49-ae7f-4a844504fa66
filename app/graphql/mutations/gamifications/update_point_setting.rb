module Mutations
  class Gamifications::UpdatePointSetting < BaseMutation
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: true
    field :point_setting, Types::Objects::Gamifications::PointSettingType, null: false

    argument :id, Integer, required: true
    argument :point_setting_input, Types::Inputs::Gamifications::PointSettingInputType, required: true

    def resolve(id:, point_setting_input:)
      point_setting = ::Gamification::PointSetting.find(id)
      return { success: false, errors: ['Point Setting Not Found'], point_setting: nil } unless point_setting.present?

      if point_setting.update!(**point_setting_input)
        { success: true, errors: [], point_setting: point_setting }
      else       
        { success: false, errors: point_setting.errors.full_messages, point_setting: point_setting }
      end
    end
  end
end
