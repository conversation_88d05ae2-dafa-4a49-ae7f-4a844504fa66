module Mutations
  class Gamifications::UpdateLevelSetting < BaseMutation
    field :success, <PERSON><PERSON>an, null: false
    field :errors, [String], null: true
    field :level_setting, Types::Objects::Gamifications::LevelSettingType, null: false

    argument :id, Integer, required: true
    argument :level_setting_input, Types::Inputs::Gamifications::LevelSettingInputType, required: true

    def resolve(id:, level_setting_input:)
      level_setting = ::Gamification::LevelSetting.find_by(id:)
      return { success: false, errors: ['Level Setting Not Found'], level_setting: nil } unless level_setting.present?

      if level_setting.update!(**level_setting_input)
        { success: true, errors: [], level_setting: level_setting }
      else       
        { success: false, errors: level_setting.errors.full_messages, point_setting: point_setting }
      end
    end
  end
end
