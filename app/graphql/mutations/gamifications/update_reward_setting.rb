module Mutations
  class Gamifications::UpdateRewardSetting < BaseMutation
    field :success, <PERSON><PERSON>an, null: false
    field :errors, [String], null: true
    field :reward_setting, Types::Objects::Gamifications::RewardSettingType, null: false

    argument :id, Integer, required: true
    argument :reward_setting_input, Types::Inputs::Gamifications::RewardSettingInputType, required: true

    def resolve(id:, reward_setting_input:)
      reward_setting = ::Gamification::RewardSetting.find_by(id:)
      return { success: false, errors: ['Reward Setting Not Found'], reward_setting: {} } unless reward_setting.present?

      if reward_setting.update!(**reward_setting_input)
        { success: true, errors: [], reward_setting: reward_setting }
      else
        { success: false, errors: reward_setting.errors.full_messages, reward_setting: reward_setting }
      end
    end
  end
end
