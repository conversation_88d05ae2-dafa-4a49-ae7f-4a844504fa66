module Mutations
  class Gamifications::UpdateBadgeSetting < BaseMutation
    field :success, <PERSON><PERSON>an, null: false
    field :errors, [String], null: true
    field :badge_setting, Types::Objects::Gamifications::BadgeSettingType, null: false

    argument :id, Integer, required: true
    argument :badge_setting_input, Types::Inputs::Gamifications::BadgeSettingInputType, required: true

    def resolve(id:, badge_setting_input:)
      badge_setting = ::Gamification::BadgeSetting.find_by(id:)
      return { success: false, errors: ['Badge Setting Not Found'], badge_setting: {} } unless badge_setting.present?

      if badge_setting.update!(**badge_setting_input)
        { success: true, errors: [], badge_setting: badge_setting }
      else       
        { success: false, errors: badge_setting.errors.full_messages, badge_setting: badge_setting }
      end
    end
  end
end
