module Mutations
  class Gamifications::Claim<PERSON><PERSON>ard < BaseMutation
    description "Claim a gamification reward"

    argument :reward_id, ID, required: true, description: "ID of the reward to claim"

    field :user_reward, Types::Objects::Gamifications::UserRewardType, null: true, description: "The claimed reward"
    field :subscription, Types::Objects::Subscriptions::SubscriptionType, null: true, description: "Course subscription if reward is a course"
    field :success, Boolean, null: false, description: "Whether the claim was successful"
    field :message, String, null: true, description: "Success or error message"

    def resolve(reward_id:)
      current_user = context[:current_user]
      
      user_reward = current_user.gamification_rewards.find_by(id: reward_id)
      
      unless user_reward
        return {
          success: false,
          message: "Reward not found",
          user_reward: nil,
          subscription: nil
        }
      end

      if user_reward.claimed_status == 'claimed'
        return {
          success: false,
          message: "Reward already claimed",
          user_reward: user_reward,
          subscription: nil
        }
      end

      subscription = nil
      
      begin
        ActiveRecord::Base.transaction do
          user_reward.update!(
            claimed_status: 'claimed',
            claimed_at: Time.current
          )

          if user_reward.reward_setting&.reward_type == 'course'
            course = user_reward.reward_setting.rewardable

            if course && course.is_a?(::Courses::Course)
              existing_subscription = current_user.subscriptions.find_by(course: course)

              unless existing_subscription
                course.add_student!(
                  current_user,
                  'reward_claim',
                  current_user
                )

                subscription = current_user.subscriptions.find_by(course: course)
              end
            end
          end
        end

        {
          success: true,
          message: "Reward claimed successfully!",
          user_reward: user_reward.reload,
          subscription: subscription
        }

      rescue ActiveRecord::RecordInvalid => e
        {
          success: false,
          message: "Failed to claim reward: #{e.message}",
          user_reward: user_reward,
          subscription: nil
        }
      rescue StandardError => e

        {
          success: false,
          message: "An error occurred while claiming the reward",
          user_reward: user_reward,
          subscription: nil
        }
      end
    end
  end
end
