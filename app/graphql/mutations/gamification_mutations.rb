module Mutations
  module GamificationMutations
    include Types::BaseInterface

    # Gamification Settings
    field :update_gamification_setting, mutation: Mutations::Gamifications::UpdateGamificationSetting

    # Point Settings
    field :update_point_setting, mutation: Mutations::Gamifications::UpdatePointSetting

    # Level Settings
    field :update_level_setting, mutation: Mutations::Gamifications::UpdateLevelSetting

    # Badge Settings
    field :update_badge_setting, mutation: Mutations::Gamifications::UpdateBadgeSetting

    # Reward Settings
    field :create_reward_setting, mutation: Mutations::Gamifications::CreateRewardSetting
    field :update_reward_setting, mutation: Mutations::Gamifications::UpdateRewardSetting

    # User Rewards
    field :claim_reward, mutation: Mutations::Gamifications::ClaimReward
  end
end
