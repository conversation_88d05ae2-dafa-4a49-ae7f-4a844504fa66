module Mutations
  module Courses
    class UpdateLessonPresentation < ::Mutations::BaseMutation
      field :lesson, Types::Objects::Courses::LessonPresentationType, null: false

      argument :lesson_input, Types::Inputs::Courses::LessonPresentationInputType, required: true
      argument :id, Integer, "The ID of the lesson to be updated", required: true
    
      def resolve(id:, lesson_input:)
        lesson = ::Courses::LessonPresentation.find(id)
        lesson.update!(**lesson_input)
        { lesson: lesson }
      end
    end
  end
end
