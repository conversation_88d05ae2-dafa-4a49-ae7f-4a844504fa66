module Mutations
  class Courses::CreateLessonPresentation < BaseMutation
    field :lesson, Types::Objects::Courses::LessonPresentationType, null: false

    argument :lesson, Types::Inputs::Courses::LessonPresentationInputType, required: true

    def resolve(lesson:)
      value = ::Courses::LessonPresentation.create_with_container!(**lesson)
      {
        lesson: value
      }
    end
  end
end
