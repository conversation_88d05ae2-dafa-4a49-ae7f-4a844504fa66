module Types::Objects
  class GlobalSettingsType < Types::BaseObject
    field :id, Integer, null: false
    field :subdomain, String, null: false
    field :country, String, null: false
    field :language, String, null: false
    field :timezone, String, null: false
    field :default_currency, String, null: false
    field :video_transcoding, Bo<PERSON>an, null: false
    field :contact_email, String, null: true
    field :contact_number, String, null: true
    field :school_name, String, null: true
    field :school_description, String, null: true

    field :available_option, <PERSON>olean, null: true
    field :link, String, null: true
    field :licenses, [Types::Objects::Marketplaces::LicenseType], null: true
    field :feature_plan_id, String, null: false
    field :feature_plan, Types::Objects::FeaturePlanType, null: false
    field :feature_plan_expiration_date, GraphQL::Types::ISO8601Date
    field :is_trial, Boolean, null: false
    field :disabled, Boolean, null: false

    field :login_page_only, <PERSON><PERSON>an, null: false
    field :has_stripe_customer_id, <PERSON><PERSON><PERSON>, null: true
    def has_stripe_customer_id
      object.stripe_customer_id.present?
    end

    field :users_count, Integer, null: false
    field :contacts_count, Integer, null: false
    field :courses_count, Integer, null: false
    field :used_storage_mib, Float, null: false
    field :bunny_storage_used_mib, Float, null: true
    field :exceeds_bunny_storage_limit, Boolean, null: true

    field :logo_light, Types::Objects::AttachmentType, null: true
    field :logo_dark, Types::Objects::AttachmentType, null: true
    field :favicon, Types::Objects::AttachmentType, null: true
    field :email_logo, Types::Objects::AttachmentType, null: true

    field :google_tag_manager, String, null: true
    field :facebook_pixel, String, null: true
    field :google_analytics, String, null: true

    field :show_register, Boolean, null: true

    field :api_key, String, null: false

    field :available_payment_platforms, [Types::Enums::Purchases::ProductPurchaseProviderType], null: true

    field :payment_methods, [Types::Interfaces::Purchases::PaymentMethodType], null: false

    field :project_type, String, null: true

    # Custom form
    field :phone_field_config, GraphQL::Types::JSON, null: false
    field :custom_fields_config, GraphQL::Types::JSON, null: true

    #Whatsapp 
    field :enabled_whatsapp, Boolean, null: false
    field :link_whatsapp, String, null: true

    #Pixels
    field :facebook_id, String, null: true
    field :tiktok_id, String, null: true

    #dark mode
    field :dark_mode, Boolean, null: false
    field :color_mode_light, String, null: true
    field :color_mode_dark, String, null: true

    field :mixed_theme, Boolean, null: false

    #agree new plan
    field :agree_new_plan, Boolean, null: false

    # has completed onboarding all steps.
    field :has_completed_onboarding, Boolean, null: false

    # SEO
    field :seo_title, String, null: true
    def seo_title
      object.seo_title || object.school_name
    end
    
    field :seo_description, String, null: true
    def seo_description
      object.seo_description || object.school_description
    end

    field :stripe_payment_methods, GraphQL::Types::JSON, null: false
    field :extra_fields, GraphQL::Types::JSON, null: false

    field :max_login_sessions, Integer, null: true
    field :requires_verification, Boolean, null: false
    field :restrict_sessions, Boolean, null: false

    # mailchimps fields
    field :mailchimp_api_key, String, null: true
    field :mailchimp_list_id, String, null: true
    field :mandrill_enabled, Boolean, null: true
    field :mandrill_api_key, String, null: true
    field :past_due_invoice_url, String, null: true
    field :is_customer, Boolean, null: true
    field :gamification_enabled, Boolean, null: true
    field :gamification_setting, Types::Objects::Gamifications::GamificationSettingType, null: true
  end
end
