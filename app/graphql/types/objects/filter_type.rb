# frozen_string_literal: true
module Types
  module Objects
    class FilterType < Types::BaseObject
    field :id, ID, null: false
    field :name, String, null: false
    field :description, String, null: true
    field :list_type, String, null: false
    field :filters, GraphQL::Types::JSON, null: false
    field :metadata, GraphQL::Types::JSON, null: false
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
    end
  end
end
