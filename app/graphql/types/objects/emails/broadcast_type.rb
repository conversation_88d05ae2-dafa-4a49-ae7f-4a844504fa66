# frozen_string_literal: true
module Types
  class Objects::Emails::BroadcastType < Types::BaseObject
    field :id, ID, null: false
    field :title, String, null: false
    field :subject, String, null: false
    field :body, String, null: false
    field :status, String, null: false
    field :active, Bo<PERSON>an, null: false
    field :sent_to, String, null: false
    field :scheduled_type, String, null: false
    field :scheduled_at, GraphQL::Types::ISO8601DateTime, null: true
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
    field :send_copy_to_sender, <PERSON><PERSON><PERSON>, null: false

    # Stats fields
    field :sent, Integer, null: false
    field :delivered, Integer, null: false
    field :bounced, Integer, null: false

    # Associations
    field :signature, Types::Objects::Emails::SignatureType, null: true
    field :template, Types::Objects::Emails::TemplateType, null: true
    field :filter, Types::Objects::FilterType, null: true

    # Resolvers for stats
    def sent
      object.activities.where(status: 'sent').count
    end

    def delivered
      object.activities.where(status: 'delivered').count
    end

    def bounced
      object.activities.where(status: 'bounced').count
    end
  end
end
