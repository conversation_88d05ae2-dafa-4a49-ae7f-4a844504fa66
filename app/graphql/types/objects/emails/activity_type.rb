module Types
  class Objects::Emails::ActivityType < Types::BaseObject
    field :id, ID, null: false
    field :email_address, String, null: false
    field :status, String, null: false
    field :message_id, String, null: true
    field :error_message, String, null: true
    field :error_code, Integer, null: true
    field :occurred_at, GraphQL::Types::ISO8601DateTime, null: false
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false

    # Associations
    field :broadcast, Types::Objects::Emails::BroadcastType, null: false
    field :user, Types::Interfaces::UserType, null: true

    # Computed fields
    field :is_successful, <PERSON><PERSON><PERSON>, null: false
    field :is_engagement_activity, <PERSON><PERSON><PERSON>, null: false
    field :is_negative_activity, <PERSON><PERSON><PERSON>, null: false

    def is_successful
      object.successful?
    end

    def is_engagement_activity
      object.engagement_activity?
    end

    def is_negative_activity
      object.negative_activity?
    end
  end
end
