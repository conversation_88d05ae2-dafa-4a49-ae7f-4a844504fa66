module Types
  class Objects::Gamifications::UserProfileType < Types::BaseObject
    field :id, Integer, null: false
    field :tenant_id, Integer, null: false
    field :total_points, Integer, null: false
    field :current_level_progress, Integer, null: false
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
    field :level_setting, Objects::Gamifications::LevelSettingType, null: false
    field :gamification_badges, [Objects::Gamifications::BadgeSettingType], null: false
    field :gamification_rewards, [Objects::Gamifications::UserRewardType], null: false
    field :user, Types::Interfaces::UserType, null: false
  end
end
