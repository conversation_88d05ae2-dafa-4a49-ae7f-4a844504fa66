module Types
  class Objects::Gamifications::PointSettingType < Types::BaseObject
    field :id, Integer, null: false
    field :tenant_id, Integer, null: false
    field :gamification_setting_id, Integer, null: false
    field :action_type, Types::Enums::Gamifications::ActionTypeEnum, null: false
    field :points, Integer, null: false
    field :description, String, null: true
    field :enabled, <PERSON><PERSON>an, null: false
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false

    field :gamification_setting, Objects::Gamifications::GamificationSettingType, null: false
  end
end
