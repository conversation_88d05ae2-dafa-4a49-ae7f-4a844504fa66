module Types
  class Objects::Gamifications::BadgeSettingType < Types::BaseObject
    field :id, Integer, null: false
    field :tenant_id, Integer, null: false
    field :gamification_setting_id, Integer, null: false
    field :condition_type, Types::Enums::Gamifications::BadgeConditionTypeEnum, null: false
    field :title, String, null: true
    field :description, String, null: true
    field :points, Integer, null: true
    field :icon, Types::Objects::AttachmentType, null: true
    field :enabled, Bo<PERSON>an, null: false
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false

    field :gamification_setting, Objects::Gamifications::GamificationSettingType, null: false

    field :user_progress, Float, null: true
    field :is_earned_by_user, <PERSON><PERSON><PERSON>, null: true

    def user_progress
      return nil unless context[:current_user]&.is_a?(Student)

      object.user_progress(context[:current_user]) || 0
    end

    def is_earned_by_user
      return nil unless context[:current_user]&.is_a?(Student)

      context[:current_user].gamification_badges.include?(object) || false
    end
  end
end
