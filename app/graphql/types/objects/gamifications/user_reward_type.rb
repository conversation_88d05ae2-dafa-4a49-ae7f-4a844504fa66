module Types
  class Objects::Gamifications::UserRewardType < Types::BaseObject
    field :id, Integer, null: false
    field :tenant_id, Integer, null: false
    field :claimed_at, GraphQL::Types::ISO8601DateTime, null: true
    field :claimed_status, Types::Enums::Gamifications::ClaimedStatusTypeEnum, null: false
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
    field :reward_setting, Objects::Gamifications::RewardSettingType, null: false
  end
end
