module Types
  class Objects::Gamifications::RewardSettingType < Types::BaseObject
    field :id, Integer, null: false
    field :tenant_id, Integer, null: false
    field :gamification_setting_id, Integer, null: false
    field :condition_type, Types::Enums::Gamifications::RewardConditionTypeEnum, null: false
    field :condition_value, Integer, null: false
    field :reward_type, Types::Enums::Gamifications::RewardTypeEnum, null: false
    field :rewardable_id, Integer, null: true
    field :rewardable_type, String, null: true
    # field :title, String, null: true
    # field :description, String, null: true
    field :enabled, Bo<PERSON>an, null: false
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false

    field :gamification_setting, Objects::Gamifications::GamificationSettingType, null: false
    field :rewardable, Types::Objects::Gamifications::RewardableType, null: true
  end
end
