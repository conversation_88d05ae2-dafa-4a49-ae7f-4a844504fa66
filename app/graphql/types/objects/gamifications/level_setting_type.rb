module Types
  class Objects::Gamifications::LevelSettingType < Types::BaseObject
    field :id, Integer, null: false
    field :tenant_id, Integer, null: false
    field :gamification_setting_id, Integer, null: false
    field :title, String, null: false
    field :min_points, Integer, null: false
    field :max_points, Integer, null: false
    field :icon, Types::Objects::AttachmentType, null: true
    field :enabled, Boolean, null: false
    field :position, Integer, null: true
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false

    field :gamification_setting, Objects::Gamifications::GamificationSettingType, null: false
  end
end
