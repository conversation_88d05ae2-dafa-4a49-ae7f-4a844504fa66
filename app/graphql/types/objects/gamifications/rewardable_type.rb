# app/graphql/types/unions/rewardable_union.rb
module Types::Objects
  module Gamifications
    class RewardableType < Types::BaseUnion
      description "Represents either a Course or a Coupon as a reward"

      possible_types Types::Objects::Courses::CourseType,
                     Types::Objects::Purchases::CouponType

      def self.resolve_type(object, context)
        case object
        when ::Courses::Course
          Types::Objects::Courses::CourseType
        when ::Purchases::Coupon
          Types::Objects::Purchases::CouponType
        else
          raise "Unexpected rewardable type: #{object.class.name}"
        end
      end
    end
  end
end
