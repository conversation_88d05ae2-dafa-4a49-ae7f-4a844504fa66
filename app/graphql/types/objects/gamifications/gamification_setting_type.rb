module Types
  class Objects::Gamifications::GamificationSettingType < Types::BaseObject
    field :id, Integer, null: true
    field :tenant_id, Integer, null: false
    field :points_enabled, <PERSON><PERSON>an, null: false
    field :badges_enabled, <PERSON><PERSON>an, null: false
    field :configured_settings, <PERSON><PERSON>an, null: false
    field :created_at, GraphQL::Types::ISO8601DateTime, null: true
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: true

    field :level_settings, [Objects::Gamifications::LevelSettingType], null: true
    field :point_settings, [Objects::Gamifications::PointSettingType], null: true
    field :badge_settings, [Objects::Gamifications::BadgeSettingType], null: true
    field :reward_settings, [Objects::Gamifications::RewardSettingType], null: true
  end
end
