module Types
  module Gamification
    class LeaderboardEntryType < Types::BaseObject
      description "A leaderboard entry showing user ranking and points"

      field :user, Types::Interfaces::UserType, null: false, description: "Complete user information"
      field :user_profile, Types::Objects::Gamifications::UserProfileType, null: true, description: "User's gamification profile"
      field :level, Types::Objects::Gamifications::LevelSettingType, null: true, description: "User's current level"
      field :total_points, Integer, null: false, description: "Total points earned in the period"
      field :rank, Integer, null: false, description: "User's rank in the leaderboard"

      def user
        object.user
      end

      def user_profile
        user&.gamification_user_profile
      end

      def level
        user_profile&.level_setting
      end

      def rank
        object.try(:rank) || 0
      end

      def total_points
        object.total_points
      end
    end
  end
end
