module Types
  class QueryType < Types::BaseObject
    # Add `node(id: ID!) and `nodes(ids: [ID!]!)`
    include GraphQL::Types::Relay::HasNodeField
    include GraphQL::Types::Relay::HasNodesField

    # Add root-level fields here.
    # They will be entry points for queries on your schema.
    implements Queries::Admin::CourseQueryType
    implements Queries::Admin::CommunityQueryType
    implements Queries::Admin::CategoryQueryType
    implements Queries::Admin::WebsiteSectionQueryType
    implements Queries::Admin::GlobalSettingsQueryType
    implements Queries::Admin::GroupQueryType
    implements Queries::Admin::PurchaseQueryType
    implements Queries::Admin::RefundQueryType
    implements Queries::Admin::InvoiceQueryType
    implements Queries::Admin::VersionQueryType
    implements Queries::Admin::EmailQueryType
    implements Queries::Admin::TriggerQueryType
    implements Queries::Admin::CouponQueryType
    implements Queries::Admin::MarketplaceCategoryQueryType
    implements Queries::Admin::PageQueryType
    implements Queries::Admin::ExceedFeatureType
    implements Queries::Admin::ReportQueryType
    implements Queries::Admin::AffiliateQueryType
    implements Queries::Admin::GamificationQueryType

    implements Queries::UserQueryType
    implements Queries::FilterQueryType

    implements Queries::SocialQueryType
    implements Queries::MediaQueryType

    # Student queries
    implements Queries::SubscriptionQueryType
    implements Queries::ExamAttemptQueryType
    implements Queries::SurveyQueryType
    implements Queries::NoticeQueryType
    implements Queries::Students::GroupQueryType
    implements Queries::Students::GamificationQueryType

    # Affiliate User Queries
    implements Queries::Affiliate::AffiliateCourseQueryType
    implements Queries::Affiliate::PaymentQueryType
    implements Queries::Affiliate::SettingQueryType

    implements Queries::Admin::BroadcastQueryType # For Email Broadcasting
  end
end
