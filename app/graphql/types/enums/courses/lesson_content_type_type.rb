# frozen_string_literal: true

module Types
  class Enums::Courses::LessonContentTypeType < Types::BaseEnum
    description "Lesson type enum"

    value "AUDIO", value: "Courses::LessonAudio"
    value "DOWNLOADABLE", value: "Courses::LessonDownloadable"
    value "EXAM", value: "Courses::LessonExam"
    value "HTML", value: "Courses::LessonHtml"
    value "PDF", value: "Courses::LessonPdf"
    value "PRESENTATION", value: "Courses::LessonPresentation"
    value "SCORM", value: "Courses::LessonScorm"
    value "VIDEO", value: "Courses::LessonVideo"
    value "VIDEOCONF", value: "Courses::LessonVideocall"
    value "WEBINAR", value: "Courses::LessonWebinar"
  end
end
