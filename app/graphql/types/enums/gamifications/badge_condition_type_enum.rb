module Types
  class Enums::Gamifications::BadgeConditionTypeEnum < Types::BaseEnum
    description "Available condition types for badge settings"

    value "COMPLETED_1_COURSE", "Completed 1 course", value: "completed_1_course"
    value "COMPLETED_5_COURSE", "Completed 5 courses", value: "completed_5_course"
    value "COMPLETED_12_COURSE", "Completed 12 courses", value: "completed_12_course"
    value "COMPLETED_30_COURSE", "Completed 30 courses", value: "completed_30_course"
    value "LOGGED_10_CONSECUTIVE_DAY", "Logged 10 consecutive days", value: "logged_10_consecutive_day"
    value "SCORED_100_IN_EXAM", "Scored 100% in exam", value: "scored_100_in_exam"
    value "EARNED_FIRST_CERTIFICATE", "Earned first certificate", value: "earned_first_certificate"
    value "EARNED_20_CERTIFICATES", "Earned 20 certificates", value: "earned_20_certificates"
    value "MADE_10_COMMUNITY_POSTS", "Made 10 community posts", value: "made_10_community_posts"
    value "RECEIVED_10_REACTIONS", "Received 10 reactions", value: "received_10_reactions"
    value "RESPONDED_TO_QUESTION_OR_POST", "Responded to question or post", value: "responded_to_question_or_post"
    value "COMMENTED_ON_A_LESSON", "Commented on a lesson", value: "commented_on_a_lesson"
    value "LEFT_A_COURSE_REVIEW", "Left a course review", value: "left_a_course_review"
  end
end
