module Types
  class Enums::Gamifications::ActionTypeEnum < Types::BaseEnum
    description "Available action types for point settings"

    value "MODULE_COMPLETED", "Module completed", value: "module_completed"
    value "COURSE_COMPLETED", "Course completed", value: "course_completed"
    value "EXAM_PASSED", "Exam passed", value: "exam_passed"
    value "COMMENT_ON_LESSON", "Comment on lesson", value: "comment_on_lesson"
    value "COMMENT_ON_COMMUNITY", "Comment on community", value: "comment_on_community"
    value "POST_ON_COMMUNITY", "Post on community", value: "post_on_community"
    value "CERTIFICATE_EARNED", "Certificate earned", value: "certificate_earned"
  end
end
