# frozen_string_literal: true

module Types
  class Enums::Media::LessonContentFileType < Types::BaseEnum
    description "Lesson content file type enum"

    value "PDF", "Pdf", value: 'pdf'
    value "VIDEO", "Video", value: 'video'
    value "AUDIO", "Audio", value: 'audio'
    value "OTHER", "Aquellos aún sin diferenciar", value: 'other'
    value "PRESENTATION", "Presentation", value: 'presentation'
  end
end
