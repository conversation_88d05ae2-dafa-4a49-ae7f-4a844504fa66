module Types
  class Inputs::Gamifications::LevelSettingInputType < Types::BaseInputObject
    description "Input type for level settings"

    argument :id, Integer, required: false
    argument :title, String, required: false
    argument :min_points, Integer, required: false
    argument :max_points, Integer, required: false
    argument :icon, GraphQL::Types::JSON, required: false
    argument :enabled, Bo<PERSON>an, required: false
    argument :position, Integer, required: false
  end
end
