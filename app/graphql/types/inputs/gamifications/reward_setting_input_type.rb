module Types
  class Inputs::Gamifications::RewardSettingInputType < Types::BaseInputObject
    description "Input type for reward settings"

    argument :condition_type, Types::Enums::Gamifications::RewardConditionTypeEnum, required: false
    argument :condition_value, Integer, required: false
    argument :reward_type, Types::Enums::Gamifications::RewardTypeEnum, required: false
    argument :rewardable_id, Integer, required: false
    argument :rewardable_type, String, required: false
    argument :title, String, required: false
    argument :description, String, required: false
    argument :enabled, <PERSON><PERSON><PERSON>, required: false
  end
end
