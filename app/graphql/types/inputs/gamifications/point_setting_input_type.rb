module Types
  class Inputs::Gamifications::PointSettingInputType < Types::BaseInputObject
    description "Input type for point settings"

    argument :id, Integer, required: false
    argument :action_type, Types::Enums::Gamifications::ActionTypeEnum, required: false
    argument :points, Integer, required: false
    argument :description, String, required: false
    argument :enabled, <PERSON><PERSON><PERSON>, required: false
  end
end
