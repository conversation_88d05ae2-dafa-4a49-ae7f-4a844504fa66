module Types
  class Inputs::Gamifications::BadgeSettingInputType < Types::BaseInputObject
    description "Input type for badge settings"

    argument :id, Integer, required: false
    argument :condition_type, Types::Enums::Gamifications::BadgeConditionTypeEnum, required: false
    argument :points, Integer, required: false
    argument :title, String, required: false
    argument :description, String, required: false
    argument :icon, GraphQL::Types::JSON, required: false
    argument :enabled, Bo<PERSON>an, required: false
  end
end
