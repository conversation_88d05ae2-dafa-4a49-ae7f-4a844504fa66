module Types
  class Inputs::TenantInputType < Types::BaseInputObject
    description "Input type for tenant"

    argument :disabled, Boolean, required: false
    argument :country, String, required: false
    argument :language, String, required: false
    argument :timezone, String, required: false
    argument :default_currency, String, required: false
    argument :contact_email, String, required: false
    argument :contact_number, String, required: false
    argument :school_name, String, required: false
    argument :school_description, String, required: false
    argument :school_logo, GraphQL::Types::JSON, required: false
    argument :school_favicon, GraphQL::Types::JSON, required: false
    argument :google_tag_manager, String, required: false
    argument :facebook_pixel, String, required: false
    argument :google_analytics, String, required: false
    argument :show_register, Bo<PERSON><PERSON>, required: false

    #Whatsapp 
    argument :enabled_whatsapp, Boolean, required: false
    argument :link_whatsapp, String, required: false
    #Pixels
    argument :facebook_id, String, required: false
    argument :tiktok_id, String, required: false

    # dark mode
    argument :color_mode_light, String, required: false
    argument :color_mode_dark, String, required: false
    argument :dark_mode, Boolean, required: false

    argument :mixed_theme, <PERSON><PERSON><PERSON>, required: false

    argument :mercado_pago_public_key, String, required: false
    argument :mercado_pago_secret_access_token, String, required: false

    argument :pay_pal_client_id, String, required: false
    argument :pay_pal_client_secret, String, required: false

    argument :stripe_api_key, String, required: false

    argument :available_option, Boolean, required: false
    argument :link, String, required: false

    argument :extra_fields, GraphQL::Types::JSON, required: false
  end
end
