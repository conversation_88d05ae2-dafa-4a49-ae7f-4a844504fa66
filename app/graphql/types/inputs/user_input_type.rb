module Types
  class Inputs::UserInputType < Types::BaseInputObject
    description "Input type for users"

    argument :first_name, String, required: false
    argument :last_name, String, required: false
    argument :email, String, required: false
    argument :password, String, required: false
    argument :type, String, required: false
    argument :avatar, Types::Inputs::UppyFileInputType, required: false
    argument :default_avatar_url, String, required: false
    argument :phone, String, required: false
    argument :custom_fields, GraphQL::Types::JSON, required: false
    argument :tag_ids, [Integer], required: false
  end
end
