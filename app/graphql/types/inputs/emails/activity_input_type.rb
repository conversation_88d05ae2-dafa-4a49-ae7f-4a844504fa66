module Types
  class Inputs::Emails::ActivityInputType < Types::BaseInputObject
    argument :broadcast_id, ID, required: true
    argument :user_id, ID, required: false
    argument :email_address, String, required: true
    argument :status, String, required: false
    argument :message_id, String, required: false
    argument :error_message, String, required: false
    argument :error_code, Integer, required: false
    argument :occurred_at, GraphQL::Types::ISO8601DateTime, required: false
  end
end
