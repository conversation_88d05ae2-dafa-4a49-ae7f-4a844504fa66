module Types
  class Inputs::Emails::BroadcastInputType < Types::BaseInputObject
    argument :title, String, required: true
    argument :subject, String, required: true
    argument :body, String, required: true
    argument :status, String, required: false
    argument :sent_to, String, required: true
    argument :scheduled_type, String, required: false
    argument :filter_id, ID, required: false
    argument :signature_id, ID, required: false
    argument :template_id, ID, required: false
    argument :broadcast_status, <PERSON><PERSON>an, required: true
    argument :send_copy_to_sender, <PERSON><PERSON><PERSON>, required: false
    argument :scheduled_at, GraphQL::Types::ISO8601DateTime, required: false
  end
end
