module Types
    module Inputs
      class SabionetUserInputType < ::Types::BaseInputObject
        description "Input type for sabionet user"
        argument :email, String, required: true
        argument :provider, String, required: false
        argument :uid, String, required: false
        argument :first_name, String, required: false
        argument :phone, String, required: false
        argument :password, String, required: false 
        argument :token, String, required: false
      end
    end
end