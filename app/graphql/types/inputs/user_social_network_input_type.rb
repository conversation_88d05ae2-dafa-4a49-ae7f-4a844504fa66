module Types
  class Inputs::UserSocialNetworkInputType < Types::BaseInputObject
    description "Input for creating or updating a user's social network profile"
    argument :id, Integer, required: false
    argument :tenant_id, Integer, required: false
    argument :platform, String, required: false
    argument :profile_url, String, required: false
    argument :_destroy, <PERSON><PERSON><PERSON>, required: false
  end
end
