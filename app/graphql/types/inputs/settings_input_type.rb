module Types
  class Inputs::SettingsInputType < Types::BaseInputObject
    description "Input type for global settings"

    argument :country, String, required: false
    argument :language, String, required: false
    argument :timezone, String, required: false
    argument :default_currency, String, required: false
    argument :video_transcoding, Boolean, required: false
    argument :contact_email, String, required: false
    argument :contact_number, String, required: false
    argument :school_name, String, required: false
    argument :school_description, String, required: false
  
    argument :logo_light, GraphQL::Types::JSON, required: false
    argument :logo_dark, GraphQL::Types::JSON, required: false
    argument :favicon, GraphQL::Types::JSON, required: false
    argument :email_logo, GraphQL::Types::JSON, required: false

    argument :google_tag_manager, String, required: false
    argument :facebook_pixel, String, required: false
    argument :google_analytics, String, required: false

    argument :show_register, Boolean, required: false

    #Whatsapp 
    argument :enabled_whatsapp, <PERSON><PERSON><PERSON>, required: false
    argument :link_whatsapp, String, required: false

     #Pixels 
     argument :facebook_id, String, required: false
     argument :tiktok_id, String, required: false

    # dark mode
    argument :dark_mode, Bo<PERSON>an, required: false
    argument :color_mode_light, String, required: false
    argument :color_mode_dark, String, required: false

    argument :mixed_theme, Boolean, required: false

    # new plan
    argument :agree_new_plan, Boolean, required: false

    # has completed onboarding
    argument :has_completed_onboarding, Boolean, required: false

    argument :mercado_pago_public_key, String, required: false
    argument :mercado_pago_secret_access_token, String, required: false

    argument :pay_pal_client_id, String, required: false
    argument :pay_pal_client_secret, String, required: false

    argument :stripe_api_key, String, required: false

    argument :phone_field_config, GraphQL::Types::JSON, required: false

    #SEO
    argument :seo_title, String, "SEO title", required: false
    argument :seo_description, String, "SEO description", required: false

    argument :login_page_only, Boolean, "Si el homepage es únicamente una página de login", required: false

    argument :available_option, Boolean, required: false
    argument :link, String, required: false

    argument :extra_fields, GraphQL::Types::JSON, required: false

    argument :max_login_sessions, Integer, required: false
    argument :requires_verification, Boolean, required: false
    argument :restrict_sessions, Boolean, required: false

    # mailchimps fields
    argument :mailchimp_api_key, String, required: false
    argument :mailchimp_list_id, String, required: false

    argument :gamification_enabled, Boolean, required: false # Gamification enabled
    argument :apply_to_past_data, Boolean, required: false # Gamification apply to Old users
  end
end
