# frozen_string_literal: true

module Types
  class Inputs::Triggers::TriggerInputType < Types::BaseInputObject
    argument :id, ID, required: false
    argument :action, String, required: true
    argument :when, String, required: true
    argument :when_entity_ids, [Integer], required: false
    argument :action_entity_ids, [Integer], required: false
    argument :course_id, ID, required: false
    argument :apply_to_past_entities, Boolean, required: false
  end
end
