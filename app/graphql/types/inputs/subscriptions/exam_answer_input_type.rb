class Types::Inputs::Subscriptions::ExamAnswerInputType < Types::BaseInputObject
  description "Input type for exam answer"

  argument :exam_attempt_id, Integer, required: true
  argument :question_id, Integer, required: true
  argument :answer, GraphQL::Types::JSON, required: true
  argument :exam_answer_type, Types::Enums::Exams::QuestionTypeType, required: true, as: :type, prepare: ->(val, _ctx) { "Subscriptions::ExamAnswer#{val}" }
  argument :time_taken, Integer, default_value: 0, description: 'Time taken to answer the question in seconds.'

  # argument :answer_text, String, required: false
  # argument :answer_option, Integer, required: false
  # argument :answer_file, GraphQL::Types::JSON, "The file type answer field", required: false
end
