class Types::Inputs::Subscriptions::CourseSubscriptionsInputType < Types::BaseInputObject
  description "Input type for course subscription"

  argument :student_id, Integer, required: true
  argument :course_id, Integer, required: true
  argument :created_by_id, Integer, required: true
  argument :source, String, required: true
  argument :type, String, required: false
  argument :total_progress, Integer, required: false
  argument :disabled, <PERSON><PERSON><PERSON>, required: false
  argument :expiration_date, GraphQL::Types::ISO8601Date, required: false
  # argument :completion_certificate, Types::Objects::AttachmentType, required: false
  argument :completion_certificate_reference_code, String, required: false
  argument :completion_certificate_generated_at, GraphQL::Types::ISO8601DateTime, required: false
  argument :created_at, GraphQL::Types::ISO8601DateTime, required: false
end
