# frozen_string_literal: true

module Types
  class Inputs::Subscriptions::AnswerContentInputType < Types::BaseInputObject
    argument :answer_text, String, "For a TEXT type answer, the text", required: false
    argument :answer_option_ids, [Int], "For a MULTIPLE_CHOICE type answer, an array with all options chosen by the student", required: false
    argument :answer_file, GraphQL::Types::JSON, "For a FILE type answer, the object returned by Uppy", required: false
  end
end
