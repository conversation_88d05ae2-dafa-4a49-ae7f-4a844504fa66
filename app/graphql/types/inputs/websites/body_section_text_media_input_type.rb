module Types
  class Inputs::Websites::BodySectionTextMediaInputType < Types::Inputs::Websites::BodySectionInputType
    argument :description, String, required: false
    argument :description_active, <PERSON><PERSON>an, required: false
    argument :website_button_attributes, Inputs::Websites::ButtonInputType, required: false
    argument :text_align, String, required: false
    argument :media, GraphQL::Types::JSON, required: false
    argument :media_align, String, required: false
    argument :text_align_header, String, required: false
    argument :text_align_subheader, String, required: false
    argument :text_align_content, String, required: false
    argument :line_height_header, String, required: false
    argument :line_height_subheader, String, required: false
    argument :line_height_content, String, required: false
    argument :content_font_color, String, required: false
    argument :content_font_size, String, required: false
    argument :content_font, String, required: false
    argument :content_font_bold, <PERSON><PERSON><PERSON>, required: false
    argument :content_font_cursive, <PERSON><PERSON>an, required: false
    argument :content_font_underlined, <PERSON><PERSON><PERSON>, required: false
  end
end
