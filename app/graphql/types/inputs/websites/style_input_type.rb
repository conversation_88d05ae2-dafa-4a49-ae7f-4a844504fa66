module Types
  class Inputs::Websites::StyleInputType < Types::BaseInputObject
    argument :id, Integer, required: false
    argument :active, Boolean, required: false
    argument :style, String, required: false
    argument :border, String, required: false
    argument :align, String, required: false
    argument :color, String, required: false
    argument :color_hover, String, required: false
    argument :color_text, String, required: false
    argument :font, String, required: false
    argument :font_size, String, required: false
    argument :font_bold, <PERSON><PERSON>an, required: false
    argument :font_cursive, <PERSON><PERSON>an, required: false  
    argument :font_underlined, <PERSON><PERSON>an, required: false
    argument :font_line_through, <PERSON><PERSON><PERSON>, required: false
  end
end
