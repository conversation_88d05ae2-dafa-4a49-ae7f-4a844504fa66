module Types
  module Inputs
    module Websites
      class BodySectionButtonInputType <  Types::Inputs::Websites::BodySectionInputType
        argument :id, ID, required: false
        argument :button_type, String, required: false
        argument :text, String, required: true
        argument :style, String, required: false
        argument :border, String, required: false
        argument :radius, Integer, required: false
        argument :color_text, String, required: false
        argument :color_button, String, required: false
        argument :url, String, required: false
      end
    end
  end
end
