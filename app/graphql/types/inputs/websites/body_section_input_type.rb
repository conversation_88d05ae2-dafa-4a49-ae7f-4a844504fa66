module Types
  class Inputs::Websites::BodySectionInputType < Types::BaseInputObject
    argument :header, String, "The header of the body section", required: false
    argument :subheader, String, "The subheader of the body section", required: false
    argument :header_color, String, "The header color", required: false
    argument :subheader_color, String, "The subheader color", required: false
    argument :header_font, String, "The header font", required: false
    argument :header_font_bold, Boolean, required: false
    argument :header_font_cursive, <PERSON><PERSON>an, required: false
    argument :header_font_underlined, <PERSON><PERSON><PERSON>, required: false
    argument :header_font_line_through, Bo<PERSON>an, required: false
    argument :header_font_alignment, String, required: false
    argument :header_font_line_height, String, required: false
    argument :subheader_font, String, "The subheader font", required: false
    argument :subheader_font_bold, Bo<PERSON>an, required: false
    argument :subheader_font_cursive, <PERSON>olean, required: false
    argument :subheader_font_underlined, Boolean, required: false
    argument :subheader_font_line_through, Bo<PERSON>an, required: false
    argument :subheader_font_alignment, String, required: false
    argument :subheader_font_line_height, String, required: false
    argument :header_size, String, "The header font", required: false
    argument :subheader_size, String, "The subheader font", required: false
    argument :header_active, Boolean, "If the header is active", required: false
    argument :subheader_active, Boolean, "If the subheader is active", required: false
    argument :background_color, String, required: false
    argument :background_visibility, Boolean, required: false
    argument :image, GraphQL::Types::JSON, required: false
    argument :image_visibility, Boolean, required: false
    argument :image_mobile, GraphQL::Types::JSON, required: false
    argument :visibility, Boolean, required: false
    argument :dark_elements, Boolean, required: false
    argument :position, Integer, "The position of the body section", required: false
    argument :margins_attributes, [Inputs::Websites::MarginInputType], required: false

    argument :parent_banner_id, ID, required: false
    argument :content_id, ID, required: false
    argument :existing_email_form_id, ID, required: false
    argument :existing_email_form_enabled, Boolean, required: false
  end
end
