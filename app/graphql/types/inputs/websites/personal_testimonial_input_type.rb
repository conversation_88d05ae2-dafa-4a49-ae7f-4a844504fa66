module Types
  class Inputs::Websites::PersonalTestimonialInputType < Types::BaseInputObject
    argument :name, String, "Person name", required: false
    argument :occupation, String, required: false
    argument :comment, String, required: false
    argument :position, Integer, required: false
    argument :section_testimonial_id, Integer, required: false
    argument :picture, GraphQL::Types::JSON, required: false
  end
end
