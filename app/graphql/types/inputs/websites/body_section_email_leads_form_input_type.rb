module Types
  module Inputs
    module Websites
      class BodySectionEmailLeadsFormInputType <  Types::Inputs::Websites::BodySectionInputType
        argument :id, ID, required: false
        argument :name, String, required: false
        argument :heading, String, required: false
        argument :description, String, required: false
        argument :checkbox_message, String, required: false
        argument :checkbox_active, Bo<PERSON>an, required: false
        argument :button_color, String, required: false
        argument :button_text, String, required: false
        argument :views_count, Integer, required: false
        argument :redirect_url, String, required: false
        argument :custom_fields_config, GraphQL::Types::JSON, required: false
      end
    end
  end
end
