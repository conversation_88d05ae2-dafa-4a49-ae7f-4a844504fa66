module Types
  class Inputs::Websites::BodySectionCountersRowInputType < Types::BaseInputObject
    argument :id, ID, required: false
    argument :text, String, required: false
    argument :number, Integer, required: false
    argument :units, String, required: false
    argument :count_backwards, <PERSON><PERSON>an, required: false
    argument :start_date, GraphQL::Types::ISO8601DateTime, required: false
    argument :start_now, Boolean, required: false
  end
end
