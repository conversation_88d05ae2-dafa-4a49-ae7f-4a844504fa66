module Types
  class Inputs::Websites::BodySectionBannerInputType < Types::Inputs::Websites::BodySectionInputType
    argument :button_align, String, required: false
    argument :media_align, String, required: false
    argument :text_align, String, required: false
    argument :website_button_attributes, Inputs::Websites::ButtonInputType, required: false
    argument :active_carousel, Boolean, required: false
    argument :items_banner, [GraphQL::Types::JSON], required: false
  end
end
