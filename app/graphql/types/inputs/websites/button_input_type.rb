module Types
  class Inputs::Websites::ButtonInputType < Types::BaseInputObject
    argument :title, String, "Title of the button", required: false
    argument :url, String, "Target url", required: false
    argument :target, String, "Type of redirection", required: false
    argument :color, String, required: false
    argument :color_hover, String, required: false
    argument :style, String, required: false
    argument :align, String, required: false
    argument :active, Boolean, "If the button is active", required: false

    argument :button_styles_attributes, [Inputs::Websites::ButtonStyleInputType], required: false
  end
end
