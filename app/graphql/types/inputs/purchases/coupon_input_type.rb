module Types
    class Inputs::Purchases::CouponInputType < Types::BaseInputObject
        argument :code, String
        argument :description, String
        argument :discount_percent, Integer, required: false
        argument :enabled, Boolean
        argument :start_date, String
        argument :end_date, String
        argument :quantity, Integer
        argument :type, String
        argument :trial_period_days, Integer, required: false
        argument :products, [Inputs::Purchases::CouponProductInputType]
    end
end