# frozen_string_literal: true

module Types
  class Inputs::Reports::ReportInputType < Types::BaseInputObject
    argument :id, ID, required: false
    argument :name, String, required: true
    argument :default_chart_name, String, required: false
    argument :chart_type, String, required: true
    argument :report_tab_id, ID, required: true
    argument :content, GraphQL::Types::JSON, required: true
  end
end
