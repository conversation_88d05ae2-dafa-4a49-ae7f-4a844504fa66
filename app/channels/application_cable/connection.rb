module ApplicationCable
  class Connection < ActionCable::Connection::Base
    identified_by :current_user

    include MultiTenantHelper

    def connect
      MultiTenantSupport.turn_off_protection
      self.current_user = find_verified_user

      reject_unauthorized_connection unless current_user
    end

    private

    def find_verified_user
      uid = request.params['uid']
      access_token = request.params['access-token']
      client = request.params['client'] || 'default'
      subdomain = request.params['subdomain']

      return nil unless uid.present? && access_token.present?

      tenant = Tenant.find_by(subdomain: subdomain)

      if tenant.present?
        user = User.find_by(uid: uid, tenant_id: tenant.id)
      else
        user = User.find_by(uid: uid)
      end

      return nil unless user

      if user.valid_token?(access_token, client)
        user
      else
        nil
      end
    end
  end
end
