{
    
    "filters":
              {
                "dateRange":"this_month",
               "customDateRange":{"start":null,"end":null},
               "filterGroups":[{
                                "conditions":[{"field":"course_status","operator":"","value":"finished","options":[]}]
                                }
                            ]
                }
}
'<html> <table style="width:100%"> <tr style="width: 100px; height: 5px; border-radius: 33px;"> <td> </td> </tr> <tr> <td class="email-body" width="570" cellpadding="0" cellspacing="0" align="center"> <table class="email-body_inner" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation"> <!-- Body title --> <tr class="email-title"> <td class="content-cell"> <h1 class="f-fallback"> Asignacion a curso/pack </h1> </td> </tr> <!-- Body content --> <tr> <td class="content-cell"> <div class="f-fallback"> <p style="text-align: center; color: #002A8D;">El administrador @admin_name te asignó a @product_name</p><br><table class="email-content" width="100%"> <tr> <td style="text-align-last: center;"> <img width="360" src="https://cdn.dribbble.com/users/1162077/screenshots/3695625/media/77f42e5839ded066a9511028baa00b9e.png" /> <br> <br> </td> </tr></table><p style="text-align: center; color: #002A8D;">Accede ahora al nuevo curso:</p><!-- Action --><table class="body-action" align="center" width="100%" cellpadding="0" cellspacing="0" style="color: #FFFFFF; "> <tr style="color: #FFFFFF; "> <td align="center"> <table width="100%" border="0" cellspacing="0" cellpadding="0" style="color: #FFFFFF;"> <tr style="color: #FFFFFF;"> <td align="center" style="color: #FFFFFF;"> <table border="0" cellspacing="0" cellpadding="0" style=""> <tr style="color: #FFFFFF; text-align: center;"> <td style="color: #FFFFFF;"> <a href="@school_url" class="button button--" target="_blank"><strong>Visitar Academia</strong> </a> </td> </tr> </table> </td> </tr> </table> </td> </tr></table> </div> </td> </tr> </table> </td> </tr> </table> <style scoped type="text/css" rel="stylesheet" media="all"> .email-title { font-size: 40px; text-align: center;} @import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,700&display=swap"); body { width: 100% !important; height: 100%; margin: 0; -webkit-text-size-adjust: none; } a { color: #3869D4; } a img { border: none; } td { word-break: break-word; } body, td, th { font-family: "Nunito Sans", Helvetica, Arial, sans-serif; } h1 { margin-top: 0; margin-bottom: 0; font-size: 25px; font-weight: bold; text-align: center; } h2 { margin-top: 0; color: #333333; font-size: 16px; font-weight: bold; text-align: left; } h3 { margin-top: 0; color: #333333; font-size: 14px; font-weight: bold; text-align: left; } td, th { font-size: 16px; } p, ul, ol, blockquote { margin: .4em 0 1.1875em; font-size: 16px; line-height: 1.625; } .align-right { text-align: right; } .align-left { text-align: left; } .align-center { text-align: center; } .button { background-color: #004899; border-top: 10px solid #004899; border-right: 18px solid #004899; border-bottom: 10px solid #004899; border-left: 18px solid #004899; display: inline-block; color: #FFF; text-decoration: none; border-radius: 3px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); -webkit-text-size-adjust: none; box-sizing: border-box; background: #004899; border-radius: 18px; width: 260px; height: 50px; } @media only screen and (max-width: 500px) { .button { width: 100% !important; text-align: center !important; } } .attributes { margin: 0 0 21px; } .attributes_content { background-color: #F4F4F7; padding: 16px; } .attributes_item { padding: 0; } body { background-color: #F2F4F6; color: #51545E; } p { color: #51545E; } .email-wrapper { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #F2F4F6; } .email-content { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body { width: 100%; margin: 0; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; } .email-body_inner { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; } .email-footer { width: 570px; margin: 0 auto; padding: 0; -premailer-width: 570px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .email-footer p { color: #A8AAAF; } .body-action { width: 100%; margin: 30px auto; padding: 0; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0; text-align: center; } .body-sub { margin-top: 25px; padding-top: 25px; border-top: 1px solid #EAEAEC; } .content-cell { padding: 12px 24px; } @media only screen and (max-width: 600px) { .email-body_inner, .email-footer { width: 100% !important; } } </style></html>'
