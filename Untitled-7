[#<Purchases::ProductPurchase:0x00007ff2829d8fd8                                
  id: 83904,                                                                    
  tenant_id: 9605,                                                              
  user_id: 314461,                                                              
  product_type: "Courses::Course",                                              
  product_id: 15122,                                                            
  status: "approved",                                                           
  provider: "stripe_connect",                                                   
  provider_data:                                                                
   {"verify_response_data"=>                                                    
     {"id"=>"pi_3RSrTsEXjsbbUbB91qNjEDAK",                                      
      "amount"=>2800,                                                           
      "object"=>"payment_intent",                                          
      "review"=>nil,                                                       
      "source"=>nil,
      "status"=>"succeeded",
      "created"=>**********,
      "invoice"=>"in_1RSrTsEXjsbbUbB9BHygy9v9",
      "currency"=>"usd",
      "customer"=>"cus_SNcj79XJUILmaq",
      "livemode"=>true,
      "metadata"=>{},
      "shipping"=>nil,
      "processing"=>nil,
      "application"=>nil,
      "canceled_at"=>nil,
      "description"=>"Subscription creation",
      "next_action"=>nil,
      "on_behalf_of"=>nil,
      "client_secret"=>"pi_3RSrTsEXjsbbUbB91qNjEDAK_secret_OlFCHugGEJuIieDNl4sU054EX",
      "latest_charge"=>"py_3RSrTsEXjsbbUbB91LzXmPKE",
      "receipt_email"=>nil,
      "transfer_data"=>{"amount"=>2686, "destination"=>"acct_1RBpCSIzpKcUhluP"},
      "amount_details"=>{"tip"=>{}},
      "capture_method"=>"automatic",
      "payment_method"=>"pm_1RSrUXEXjsbbUbB9P56UQNlU",
      "transfer_group"=>"group_py_3RSrTsEXjsbbUbB91LzXmPKE",
      "amount_received"=>2800,
      "amount_capturable"=>0,
      "last_payment_error"=>nil,
      "setup_future_usage"=>"off_session",
      "cancellation_reason"=>nil,
      "confirmation_method"=>"automatic",
      "payment_method_types"=>["card", "cashapp", "link", "us_bank_account"],
      "statement_descriptor"=>nil,
      "application_fee_amount"=>nil,
      "payment_method_options"=>
       {"card"=>{"network"=>nil, "installments"=>nil, "mandate_options"=>nil, "request_three_d_secure"=>"automatic"},
        "link"=>{"persistent_token"=>nil},
        "cashapp"=>{},
        "us_bank_account"=>{"mandate_options"=>{}, "verification_method"=>"automatic"}},
      "automatic_payment_methods"=>nil,
      "statement_descriptor_suffix"=>nil,
      "payment_method_configuration_details"=>nil},
    "initiate_request_data"=>
     {"items"=>[{"price"=>"price_1RC58HEXjsbbUbB9nI69DsVK"}],
      "expand"=>["latest_invoice.payment_intent"],
      "customer"=>"cus_SNcj79XJUILmaq",
      "metadata"=>{"transaction_id"=>nil},
      "transfer_data"=>{"destination"=>"acct_1RBpCSIzpKcUhluP", "amount_percent"=>95.93},
      "payment_behavior"=>"default_incomplete",
      "payment_settings"=>{"save_default_payment_method"=>"on_subscription"}},
    "initiate_response_data"=>
     {"id"=>"sub_1RSrTsEXjsbbUbB9CwbA0OFR",
      "plan"=>
       {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
        "meter"=>nil,
        "active"=>true,
        "amount"=>2800,
        "object"=>"plan",
        "created"=>**********,
        "product"=>"prod_S6HLJUbgynrskK",
        "currency"=>"usd",
        "interval"=>"month",
        "livemode"=>true,
        "metadata"=>{},
        "nickname"=>nil,
        "tiers_mode"=>nil,
        "usage_type"=>"licensed",
        "amount_decimal"=>"2800",
        "billing_scheme"=>"per_unit",
        "interval_count"=>1,
        "aggregate_usage"=>nil,
        "transform_usage"=>nil,
        "trial_period_days"=>nil},
      "items"=>
       {"url"=>"/v1/subscription_items?subscription=sub_1RSrTsEXjsbbUbB9CwbA0OFR",
        "data"=>
         [{"id"=>"si_SNcjb12aX20eRX",
           "plan"=>
            {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
             "meter"=>nil,
             "active"=>true,
             "amount"=>2800,
             "object"=>"plan",
             "created"=>**********,
             "product"=>"prod_S6HLJUbgynrskK",
             "currency"=>"usd",
             "interval"=>"month",
             "livemode"=>true,
             "metadata"=>{},
             "nickname"=>nil,
             "tiers_mode"=>nil,
             "usage_type"=>"licensed",
             "amount_decimal"=>"2800",
             "billing_scheme"=>"per_unit",
             "interval_count"=>1,
             "aggregate_usage"=>nil,
             "transform_usage"=>nil,
             "trial_period_days"=>nil},
           "price"=>
            {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
             "type"=>"recurring",
             "active"=>true,
             "object"=>"price",
             "created"=>**********,
             "product"=>"prod_S6HLJUbgynrskK",
             "currency"=>"usd",
             "livemode"=>true,
             "metadata"=>{},
             "nickname"=>nil,
             "recurring"=>{"meter"=>nil, "interval"=>"month", "usage_type"=>"licensed", "interval_count"=>1, "aggregate_usage"=>nil, "trial_period_days"=>nil},
             "lookup_key"=>nil,
             "tiers_mode"=>nil,
             "unit_amount"=>2800,
             "tax_behavior"=>"unspecified",
             "billing_scheme"=>"per_unit",
             "custom_unit_amount"=>nil,
             "transform_quantity"=>nil,
             "unit_amount_decimal"=>"2800"},
           "object"=>"subscription_item",
           "created"=>**********,
           "metadata"=>{},
           "quantity"=>1,
           "discounts"=>[],
           "tax_rates"=>[],
           "subscription"=>"sub_1RSrTsEXjsbbUbB9CwbA0OFR",
           "billing_thresholds"=>nil,
           "current_period_end"=>**********,
           "current_period_start"=>**********}],
        "object"=>"list",
        "has_more"=>false,
        "total_count"=>1},
      "object"=>"subscription",
      "status"=>"incomplete",
      "created"=>**********,
      "currency"=>"usd",
      "customer"=>"cus_SNcj79XJUILmaq",
      "discount"=>nil,
      "ended_at"=>nil,
      "livemode"=>true,
      "metadata"=>{},
      "quantity"=>1,
      "schedule"=>nil,
      "cancel_at"=>nil,
      "discounts"=>[],
      "trial_end"=>nil,
      "start_date"=>**********,
      "test_clock"=>nil,
      "application"=>nil,
      "canceled_at"=>nil,
      "description"=>nil,
      "trial_start"=>nil,
      "on_behalf_of"=>nil,
      "automatic_tax"=>{"enabled"=>false, "liability"=>nil, "disabled_reason"=>nil},
      "transfer_data"=>{"destination"=>"acct_1RBpCSIzpKcUhluP", "amount_percent"=>95.93},
      "days_until_due"=>nil,
      "default_source"=>nil,
      "latest_invoice"=>
       {"id"=>"in_1RSrTsEXjsbbUbB9BHygy9v9",
        "tax"=>nil,
        "paid"=>false,
        "lines"=>
         {"url"=>"/v1/invoices/in_1RSrTsEXjsbbUbB9BHygy9v9/lines",
          "data"=>
           [{"id"=>"il_1RSrTsEXjsbbUbB997sSJOaN",
             "plan"=>
              {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
               "meter"=>nil,
               "active"=>true,
               "amount"=>2800,
               "object"=>"plan",
               "created"=>**********,
               "product"=>"prod_S6HLJUbgynrskK",
               "currency"=>"usd",
               "interval"=>"month",
               "livemode"=>true,
               "metadata"=>{},
               "nickname"=>nil,
               "tiers_mode"=>nil,
               "usage_type"=>"licensed",
               "amount_decimal"=>"2800",
               "billing_scheme"=>"per_unit",
               "interval_count"=>1,
               "aggregate_usage"=>nil,
               "transform_usage"=>nil,
               "trial_period_days"=>nil},
             "type"=>"subscription",
             "price"=>
              {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
               "type"=>"recurring",
               "active"=>true,
               "object"=>"price",
               "created"=>**********,
               "product"=>"prod_S6HLJUbgynrskK",
               "currency"=>"usd",
               "livemode"=>true,
               "metadata"=>{},
               "nickname"=>nil,
               "recurring"=>{"meter"=>nil, "interval"=>"month", "usage_type"=>"licensed", "interval_count"=>1, "aggregate_usage"=>nil, "trial_period_days"=>nil},
               "lookup_key"=>nil,
               "tiers_mode"=>nil,
               "unit_amount"=>2800,
               "tax_behavior"=>"unspecified",
               "billing_scheme"=>"per_unit",
               "custom_unit_amount"=>nil,
               "transform_quantity"=>nil,
               "unit_amount_decimal"=>"2800"},
             "taxes"=>[],
             "amount"=>2800,
             "object"ebug2: channel 0: window 997799 sent adjust 50777
m=>0,        "parent"=>
        "application"=>nil,scription_item_details",
        "description"=>nil,m_details"=>nil,
        "invoice_pdf"=>"https://pay.stripe.com/invoice/acct_1PYB9REXjsbbUbB9/live_YWNjdF8xUFlCOVJFWGpzYmJVYkI5LF9TTmNqdmtia2kzRDBGNFBLc3h1cjNzMmR6Z0ZTTUhQLDEzODc3MDAyMQ0200GB65b5sR/pdf?s=ap",
        "total_taxes"=>[],n"=>false, "invoice_item"=>nil, "subscription"=>"sub_1RSrTsEXjsbbUbB9CwbA0OFR", "proration_details"=>{"credited_items"=>nil}, "subscription_item"=>"si_SNcjb12aX20eRX"}},
        "account_name"=>"Sabiomarket, LLC","start"=>**********},
        "auto_advance"=>false,SrTsEXjsbbUbB9BHygy9v9",
        "effective_at"=>**********,rice_details", "price_details"=>{"price"=>"price_1RC58HEXjsbbUbB9nI69DsVK", "product"=>"prod_S6HLJUbgynrskK"}, "unit_amount_decimal"=>"2800"},
        "from_invoice"=>nil,d",
        "on_behalf_of"=>nil,e,
        "period_start"=>**********,
        "subscription"=>"sub_1RSrTsEXjsbbUbB9CwbA0OFR",
        "attempt_count"=>0,],
        "automatic_tax"=>{"status"=>nil, "enabled"=>false, "provider"=>nil, "liability"=>nil, "disabled_reason"=>nil},
        "custom_fields"=>nil,
        "customer_name"=>"Juan Martinez A",RIME (at $28.00 / month)",
        "shipping_cost"=>nil,],
        "transfer_data"=>{"amount"=>2686, "destination"=>"acct_1RBpCSIzpKcUhluP"},
        "billing_reason"=>"subscription_create",B9CwbA0OFR",
        "customer_email"=>"<EMAIL>",
        "customer_phone"=>nil,s"=>{"credited_items"=>nil},
        "default_source"=>nil,m"=>"si_SNcjb12aX20eRX",
        "ending_balance"=>0,ng_tax"=>2800,
        "payment_intent"=>t_amounts"=>[],
         {"id"=>"pi_3RSrTsEXjsbbUbB91qNjEDAK",0"}],
          "amount"=>2800,",
          "object"=>"payment_intent",
          "review"=>nil,>1},
          "source"=>nil,
          "status"=>"requires_payment_method",
          "created"=>**********,
          "invoice"=>"in_1RSrTsEXjsbbUbB9BHygy9v9",
          "currency"=>"usd",self"},
          "customer"=>"cus_SNcj79XJUILmaq",
          "livemode"=>true,,
          "metadata"=>{},=>"subscription_details", "quote_details"=>nil, "subscription_details"=>{"metadata"=>{}, "subscription"=>"sub_1RSrTsEXjsbbUbB9CwbA0OFR"}},
          "shipping"=>nil,
          "processing"=>nil,0,
          "application"=>nil,
          "canceled_at"=>nil,79XJUILmaq",
          "description"=>"Subscription creation",
          "next_action"=>nil,
          "on_behalf_of"=>nil,
          "client_secret"=>"pi_3RSrTsEXjsbbUbB91qNjEDAK_secret_OlFCHugGEJuIieDNl4sU054EX",
          "latest_charge"=>nil,
          "receipt_email"=>nil,
          "transfer_data"=>{"amount"=>2686, "destination"=>"acct_1RBpCSIzpKcUhluP"},
          "amount_details"=>{"tip"=>{}},
          "capture_method"=>"automatic",
          "payment_method"=>nil,,
          "transfer_group"=>nil,
          "amount_received"=>0,
          "amount_capturable"=>0,
          "last_payment_error"=>nil,
          "setup_future_usage"=>"off_session",
          "cancellation_reason"=>nil,
          "confirmation_method"=>"automatic",
          "payment_method_types"=>["card", "cashapp", "link", "us_bank_account"],
          "statement_descriptor"=>nil,
          "application_fee_amount"=>nil,
          "payment_method_options"=>
           {"card"=>{"network"=>nil, "installments"=>nil, "mandate_options"=>nil, "request_three_d_secure"=>"automatic"},
            "link"=>{"persistent_token"=>nil},
            "cashapp"=>{},
            "us_bank_account"=>{"mandate_options"=>{}, "verification_method"=>"automatic"}},
          "automatic_payment_methods"=>nil,
          "statement_descriptor_suffix"=>nil,
          "payment_method_configuration_details"=>nil},
        "receipt_number"=>nil,
        "account_country"=>"US",
        "account_tax_ids"=>nil,
        "amount_overpaid"=>0,
        "amount_shipping"=>0,
        "latest_revision"=>nil,
        "amount_remaining"=>2800,
        "customer_address"=>nil,
        "customer_tax_ids"=>[],
        "paid_out_of_band"=>false,
        "payment_settings"=>{"default_mandate"=>nil, "payment_method_types"=>nil, "payment_method_options"=>nil},
        "shipping_details"=>nil,
        "starting_balance"=>0,
        "collection_method"=>"charge_automatically",
        "customer_shipping"=>nil,
        "default_tax_rates"=>[],
        "total_tax_amounts"=>[],
        "hosted_invoice_url"=>"https://invoice.stripe.com/i/acct_1PYB9REXjsbbUbB9/live_YWNjdF8xUFlCOVJFWGpzYmJVYkI5LF9TTmNqdmtia2kzRDBGNFBLc3h1cjNzMmR6Z0ZTTUhQLDEzODc3MDAyMQ0200GB65b5sR?s=ap",
        "status_transitions"=>{"paid_at"=>nil, "voided_at"=>nil, "finalized_at"=>**********, "marked_uncollectible_at"=>nil},
        "customer_tax_exempt"=>"none",
        "total_excluding_tax"=>2800,
        "next_payment_attempt"=>nil,
        "statement_descriptor"=>nil,
        "subscription_details"=>{"metadata"=>{}},
        "webhooks_delivered_at"=>nil,
        "application_fee_amount"=>nil,
        "default_payment_method"=>nil,
        "subtotal_excluding_tax"=>2800,
        "total_discount_amounts"=>[],
        "last_finalization_error"=>nil,
        "automatically_finalizes_at"=>nil,
        "total_pretax_credit_amounts"=>[],
        "pre_payment_credit_notes_amount"=>0,
        "post_payment_credit_notes_amount"=>0},
      "pending_update"=>nil,
      "trial_settings"=>{"end_behavior"=>{"missing_payment_method"=>"create_invoice"}},
      "invoice_settings"=>{"issuer"=>{"type"=>"self"}, "account_tax_ids"=>nil},
      "pause_collection"=>nil,
      "payment_settings"=>{"payment_method_types"=>nil, "payment_method_options"=>nil, "save_default_payment_method"=>"on_subscription"},
      "collection_method"=>"charge_automatically",
      "default_tax_rates"=>[],
      "billing_thresholds"=>nil,
      "current_period_end"=>**********,
      "billing_cycle_anchor"=>**********,
      "cancel_at_period_end"=>false,
      "cancellation_details"=>{"reason"=>nil, "comment"=>nil, "feedback"=>nil},
      "current_period_start"=>**********,
      "pending_setup_intent"=>nil,
      "default_payment_method"=>nil,
      "application_fee_percent"=>nil,
      "billing_cycle_anchor_config"=>nil,
      "pending_invoice_item_interval"=>nil,
      "next_pending_invoice_item_invoice"=>nil}},
  created_at: Mon, 26 May 2025 03:13:41.********* UTC +00:00,
  updated_at: Mon, 26 May 2025 03:14:28.********* UTC +00:00,
  amount_cents: 2800,
  amount_currency: "USD",
  origin: "https://theprimeapex.sabionet.com",
  provider_id: "pi_3RSrTsEXjsbbUbB91qNjEDAK",
  price_id: 6555,
  payment_method_id: 2097,
  coupon_id: nil,
  quantity: 1,
  payment_type: "subscription",
  visibility: true>,
 #<Purchases::ProductPurchase:0x00007ff282a63f70
  id: 85866,
  tenant_id: 9605,
  user_id: 314461,
  product_type: "Courses::Course",
  product_id: 15122,
  status: "approved",
  provider: "stripe_connect",
  provider_data:
   {"verify_response_data"=>
     {"id"=>"pi_3Rf8j5EXjsbbUbB90RF3y8zt",
      "amount"=>2800,
      "object"=>"payment_intent",
      "review"=>nil,
      "source"=>nil,
      "status"=>"succeeded",
      "created"=>**********,
      "invoice"=>"in_1Rf8j5EXjsbbUbB9tNCw8NWV",
      "currency"=>"usd",
      "customer"=>"cus_SNcj79XJUILmaq",
      "livemode"=>true,
      "metadata"=>{},
      "shipping"=>nil,
      "processing"=>nil,
      "application"=>nil,
      "canceled_at"=>nil,
      "description"=>"Subscription creation",
      "next_action"=>nil,
      "on_behalf_of"=>nil,
      "client_secret"=>"pi_3Rf8j5EXjsbbUbB90RF3y8zt_secret_rPDBQ3uH7akBKvBveaIWXOW7j",
      "latest_charge"=>"ch_3Rf8j5EXjsbbUbB907tZaFAb",
      "receipt_email"=>nil,
      "transfer_data"=>{"amount"=>2644, "destination"=>"acct_1RBpCSIzpKcUhluP"},
      "amount_details"=>{"tip"=>{}},
      "capture_method"=>"automatic",
      "payment_method"=>"pm_1Rf8sFEXjsbbUbB9vKrP0JPF",
      "transfer_group"=>"group_pi_3Rf8j5EXjsbbUbB90RF3y8zt",
      "amount_received"=>2800,
      "amount_capturable"=>0,
      "last_payment_error"=>nil,
      "setup_future_usage"=>"off_session",
      "cancellation_reason"=>nil,
      "confirmation_method"=>"automatic",
      "payment_method_types"=>["card", "cashapp", "link", "us_bank_account"],
      "statement_descriptor"=>nil,
      "application_fee_amount"=>nil,
      "payment_method_options"=>
       {"card"=>{"network"=>nil, "installments"=>nil, "mandate_options"=>nil, "request_three_d_secure"=>"automatic"},
        "link"=>{"persistent_token"=>nil},
        "cashapp"=>{},
        "us_bank_account"=>{"mandate_options"=>{}, "verification_method"=>"automatic"}},
      "automatic_payment_methods"=>nil,
      "statement_descriptor_suffix"=>nil,
      "payment_method_configuration_details"=>nil},
    "initiate_request_data"=>
     {"items"=>[{"price"=>"price_1RC58HEXjsbbUbB9nI69DsVK"}],
      "expand"=>["latest_invoice.payment_intent"],
      "customer"=>"cus_SNcj79XJUILmaq",
      "metadata"=>{"transaction_id"=>nil},
      "transfer_data"=>{"destination"=>"acct_1RBpCSIzpKcUhluP", "amount_percent"=>94.43},
      "payment_behavior"=>"default_incomplete",
      "payment_settings"=>{"save_default_payment_method"=>"on_subscription"}},
    "initiate_response_data"=>
     {"id"=>"sub_1Rf8j5EXjsbbUbB9VEYpc0Wd",
      "plan"=>
       {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
        "meter"=>nil,
        "active"=>true,
        "amount"=>2800,
        "object"=>"plan",
        "created"=>**********,
        "product"=>"prod_S6HLJUbgynrskK",
        "currency"=>"usd",
        "interval"=>"month",
        "livemode"=>true,
        "metadata"=>{},
        "nickname"=>nil,
        "tiers_mode"=>nil,
        "usage_type"=>"licensed",
        "amount_decimal"=>"2800",
        "billing_scheme"=>"per_unit",
        "interval_count"=>1,
        "aggregate_usage"=>nil,
        "transform_usage"=>nil,
        "trial_period_days"=>nil},
      "items"=>
       {"url"=>"/v1/subscription_items?subscription=sub_1Rf8j5EXjsbbUbB9VEYpc0Wd",
        "data"=>
         [{"id"=>"si_SaJL1siAeddMgP",
           "plan"=>
            {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
             "meter"=>nil,
             "active"=>true,
             "amount"=>2800,
             "object"=>"plan",
             "created"=>**********,
             "product"=>"prod_S6HLJUbgynrskK",
             "currency"=>"usd",
             "interval"=>"month",
             "livemode"=>true,
             "metadata"=>{},
             "nickname"=>nil,
             "tiers_mode"=>nil,
             "usage_type"=>"licensed",
             "amount_decimal"=>"2800",
             "billing_scheme"=>"per_unit",
             "interval_count"=>1,
             "aggregate_usage"=>nil,
             "transform_usage"=>nil,
             "trial_period_days"=>nil},
           "price"=>
            {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
             "type"=>"recurring",
             "active"=>true,
             "object"=>"price",
             "created"=>**********,
             "product"=>"prod_S6HLJUbgynrskK",
             "currency"=>"usd",
             "livemode"=>true,
             "metadata"=>{},
             "nickname"=>nil,
             "recurring"=>{"meter"=>nil, "interval"=>"month", "usage_type"=>"licensed", "interval_count"=>1, "aggregate_usage"=>nil, "trial_period_days"=>nil},
             "lookup_key"=>nil,
             "tiers_mode"=>nil,
             "unit_amount"=>2800,
             "tax_behavior"=>"unspecified",
             "billing_scheme"=>"per_unit",
             "custom_unit_amount"=>nil,
             "transform_quantity"=>nil,
             "unit_amount_decimal"=>"2800"},
           "object"=>"subscription_item",
           "created"=>**********,
           "metadata"=>{},
           "quantity"=>1,
           "discounts"=>[],
           "tax_rates"=>[],
           "subscription"=>"sub_1Rf8j5EXjsbbUbB9VEYpc0Wd",
           "billing_thresholds"=>nil,
           "current_period_end"=>**********,
           "current_period_start"=>**********}],
        "object"=>"list",
        "has_more"=>false,
        "total_count"=>1},
      "object"=>"subscription",
      "status"=>"incomplete",
      "created"=>**********,
      "currency"=>"usd",
      "customer"=>"cus_SNcj79XJUILmaq",
      "discount"=>nil,
      "ended_at"=>nil,
      "livemode"=>true,
      "metadata"=>{},
      "quantity"=>1,
      "schedule"=>nil,
      "cancel_at"=>nil,
      "discounts"=>[],
      "trial_end"=>nil,
      "start_date"=>**********,
      "test_clock"=>nil,
      "application"=>nil,
      "canceled_at"=>nil,
      "description"=>nil,
      "trial_start"=>nil,
      "billing_mode"=>{"type"=>"classic"},
      "on_behalf_of"=>nil,
      "automatic_tax"=>{"enabled"=>false, "liability"=>nil, "disabled_reason"=>nil},
      "transfer_data"=>{"destination"=>"acct_1RBpCSIzpKcUhluP", "amount_percent"=>94.43},
      "days_until_due"=>nil,
      "default_source"=>nil,
      "latest_invoice"=>
       {"id"=>"in_1Rf8j5EXjsbbUbB9tNCw8NWV",
        "tax"=>nil,
        "paid"=>false,
        "lines"=>
         {"url"=>"/v1/invoices/in_1Rf8j5EXjsbbUbB9tNCw8NWV/lines",
          "data"=>
           [{"id"=>"il_1Rf8j5EXjsbbUbB9FyXiwqbP",
             "plan"=>
              {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
               "meter"=>nil,
               "active"=>true,
               "amount"=>2800,
               "object"=>"plan",
               "created"=>**********,
               "product"=>"prod_S6HLJUbgynrskK",
               "currency"=>"usd",
               "interval"=>"month",
               "livemode"=>true,
               "metadata"=>{},
               "nickname"=>nil,
               "tiers_mode"=>nil,
               "usage_type"=>"licensed",
               "amount_decimal"=>"2800",
               "billing_scheme"=>"per_unit",
               "interval_count"=>1,
               "aggregate_usage"=>nil,
               "transform_usage"=>nil,
               "trial_period_days"=>nil},
             "type"=>"subscription",
             "price"=>
              {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
               "type"=>"recurring",
               "active"=>true,
               "object"=>"price",
               "created"=>**********,
               "product"=>"prod_S6HLJUbgynrskK",
               "currency"=>"usd",
               "livemode"=>true,
               "metadata"=>{},
               "nickname"=>nil,
               "recurring"=>{"meter"=>nil, "interval"=>"month", "usage_type"=>"licensed", "interval_count"=>1, "aggregate_usage"=>nil, "trial_period_days"=>nil},
               "lookup_key"=>nil,
               "tiers_mode"=>nil,
               "unit_amount"=>2800,
               "tax_behavior"=>"unspecified",
               "billing_scheme"=>"per_unit",
               "custom_unit_amount"=>nil,
               "transform_quantity"=>nil,
               "unit_amount_decimal"=>"2800"},
             "taxes"=>[],
             "amount"=>2800,
             "object"=>"line_item",
             "parent"=>
              {"type"=>"subscription_item_details",
               "invoice_item_details"=>nil,
               "subscription_item_details"=>
                {"proration"=>false, "invoice_item"=>nil, "subscription"=>"sub_1Rf8j5EXjsbbUbB9VEYpc0Wd", "proration_details"=>{"credited_items"=>nil}, "subscription_item"=>"si_SaJL1siAeddMgP"}},
             "period"=>{"end"=>**********, "start"=>**********},
             "invoice"=>"in_1Rf8j5EXjsbbUbB9tNCw8NWV",
             "pricing"=>{"type"=>"price_details", "price_details"=>{"price"=>"price_1RC58HEXjsbbUbB9nI69DsVK", "product"=>"prod_S6HLJUbgynrskK"}, "unit_amount_decimal"=>"2800"},
             "currency"=>"usd",
             "livemode"=>true,
             "metadata"=>{},
             "quantity"=>1,
             "discounts"=>[],
             "proration"=>false,
             "tax_rates"=>[],
             "description"=>"1 × THE APEX PRIME (at $28.00 / month)",
             "tax_amounts"=>[],
             "discountable"=>true,
             "subscription"=>"sub_1Rf8j5EXjsbbUbB9VEYpc0Wd",
             "discount_amounts"=>[],
             "proration_details"=>{"credited_items"=>nil},
             "subscription_item"=>"si_SaJL1siAeddMgP",
             "amount_excluding_tax"=>2800,
             "pretax_credit_amounts"=>[],
             "unit_amount_excluding_tax"=>"2800"}],
          "object"=>"list",
          "has_more"=>false,
          "total_count"=>1},
        "quote"=>nil,
        "total"=>2800,
        "charge"=>nil,
        "footer"=>nil,
        "issuer"=>{"type"=>"self"},
        "number"=>"FPNHLJIH-0003",
        "object"=>"invoice",
        "parent"=>{"type"=>"subscription_details", "quote_details"=>nil, "subscription_details"=>{"metadata"=>{}, "subscription"=>"sub_1Rf8j5EXjsbbUbB9VEYpc0Wd"}},
        "status"=>"open",
        "created"=>**********,
        "currency"=>"usd",
        "customer"=>"cus_SNcj79XJUILmaq",
        "discount"=>nil,
        "due_date"=>nil,
        "livemode"=>true,
        "metadata"=>{},
        "subtotal"=>2800,
        "attempted"=>false,
        "discounts"=>[],
        "rendering"=>nil,
        "amount_due"=>2800,
        "period_end"=>**********,
        "test_clock"=>nil,
        "amount_paid"=>0,
        "application"=>nil,
        "description"=>nil,
        "invoice_pdf"=>"https://pay.stripe.com/invoice/acct_1PYB9REXjsbbUbB9/live_YWNjdF8xUFlCOVJFWGpzYmJVYkI5LF9TYUpMRkNQZzN4T1VqRXc1MW5HeWtrYXc5YmNMUW5GLDE0MTY5NjI0OA0200W8Zi0jMv/pdf?s=ap",
        "total_taxes"=>[],
        "account_name"=>"Sabiomarket, LLC",
        "auto_advance"=>false,
        "effective_at"=>**********,
        "from_invoice"=>nil,
        "on_behalf_of"=>nil,
        "period_start"=>**********,
        "subscription"=>"sub_1Rf8j5EXjsbbUbB9VEYpc0Wd",
        "attempt_count"=>0,
        "automatic_tax"=>{"status"=>nil, "enabled"=>false, "provider"=>nil, "liability"=>nil, "disabled_reason"=>nil},
        "custom_fields"=>nil,
        "customer_name"=>"Juan Martinez A",
        "shipping_cost"=>nil,
        "transfer_data"=>{"amount"=>2644, "destination"=>"acct_1RBpCSIzpKcUhluP"},
        "billing_reason"=>"subscription_create",
        "customer_email"=>"<EMAIL>",
        "customer_phone"=>nil,
        "default_source"=>nil,
        "ending_balance"=>0,
        "payment_intent"=>
         {"id"=>"pi_3Rf8j5EXjsbbUbB90RF3y8zt",
          "amount"=>2800,
          "object"=>"payment_intent",
          "review"=>nil,
          "source"=>nil,
          "status"=>"requires_payment_method",
          "created"=>**********,
          "invoice"=>"in_1Rf8j5EXjsbbUbB9tNCw8NWV",
          "currency"=>"usd",
          "customer"=>"cus_SNcj79XJUILmaq",
          "livemode"=>true,
          "metadata"=>{},
          "shipping"=>nil,
          "processing"=>nil,
          "application"=>nil,
          "canceled_at"=>nil,
          "description"=>"Subscription creation",
          "next_action"=>nil,
          "on_behalf_of"=>nil,
          "client_secret"=>"pi_3Rf8j5EXjsbbUbB90RF3y8zt_secret_rPDBQ3uH7akBKvBveaIWXOW7j",
          "latest_charge"=>nil,
          "receipt_email"=>nil,
          "transfer_data"=>{"amount"=>2644, "destination"=>"acct_1RBpCSIzpKcUhluP"},
          "amount_details"=>{"tip"=>{}},
          "capture_method"=>"automatic",
          "payment_method"=>nil,
          "transfer_group"=>nil,
          "amount_received"=>0,
          "amount_capturable"=>0,
          "last_payment_error"=>nil,
          "setup_future_usage"=>"off_session",
          "cancellation_reason"=>nil,
          "confirmation_method"=>"automatic",
          "payment_method_types"=>["card", "cashapp", "link", "us_bank_account"],
          "statement_descriptor"=>nil,
          "application_fee_amount"=>nil,
          "payment_method_options"=>
           {"card"=>{"network"=>nil, "installments"=>nil, "mandate_options"=>nil, "request_three_d_secure"=>"automatic"},
            "link"=>{"persistent_token"=>nil},
            "cashapp"=>{},
            "us_bank_account"=>{"mandate_options"=>{}, "verification_method"=>"automatic"}},
          "automatic_payment_methods"=>nil,
          "statement_descriptor_suffix"=>nil,
          "payment_method_configuration_details"=>nil},
        "receipt_number"=>nil,
        "account_country"=>"US",
        "account_tax_ids"=>nil,
        "amount_overpaid"=>0,
        "amount_shipping"=>0,
        "latest_revision"=>nil,
        "amount_remaining"=>2800,
        "customer_address"=>nil,
        "customer_tax_ids"=>[],
        "paid_out_of_band"=>false,
        "payment_settings"=>{"default_mandate"=>nil, "payment_method_types"=>nil, "payment_method_options"=>nil},
        "shipping_details"=>nil,
        "starting_balance"=>0,
        "collection_method"=>"charge_automatically",
debug2: channel 0: window 997680 sent adjust 50896
        "customer_shipping"=>nil,
        "default_tax_rates"=>[],
        "total_tax_amounts"=>[],
        "hosted_invoice_url"=>"https://invoice.stripe.com/i/acct_1PYB9REXjsbbUbB9/live_YWNjdF8xUFlCOVJFWGpzYmJVYkI5LF9TYUpMRkNQZzN4T1VqRXc1MW5HeWtrYXc5YmNMUW5GLDE0MTY5NjI0OA0200W8Zi0jMv?s=ap",
        "status_transitions"=>{"paid_at"=>nil, "voided_at"=>nil, "finalized_at"=>**********, "marked_uncollectible_at"=>nil},
        "customer_tax_exempt"=>"none",
        "total_excluding_tax"=>2800,
        "next_payment_attempt"=>nil,
        "statement_descriptor"=>nil,
        "subscription_details"=>{"metadata"=>{}},
        "webhooks_delivered_at"=>nil,
        "application_fee_amount"=>nil,
        "default_payment_method"=>nil,
        "subtotal_excluding_tax"=>2800,
        "total_discount_amounts"=>[],
        "last_finalization_error"=>nil,
        "automatically_finalizes_at"=>nil,
        "total_pretax_credit_amounts"=>[],
        "pre_payment_credit_notes_amount"=>0,
        "post_payment_credit_notes_amount"=>0},
      "pending_update"=>nil,
      "trial_settings"=>{"end_behavior"=>{"missing_payment_method"=>"create_invoice"}},
      "invoice_settings"=>{"issuer"=>{"type"=>"self"}, "account_tax_ids"=>nil},
      "pause_collection"=>nil,
      "payment_settings"=>{"payment_method_types"=>nil, "payment_method_options"=>nil, "save_default_payment_method"=>"on_subscription"},
      "collection_method"=>"charge_automatically",
      "default_tax_rates"=>[],
      "billing_thresholds"=>nil,
      "current_period_end"=>**********,
      "billing_cycle_anchor"=>**********,
      "cancel_at_period_end"=>false,
      "cancellation_details"=>{"reason"=>nil, "comment"=>nil, "feedback"=>nil},
      "current_period_start"=>**********,
      "pending_setup_intent"=>nil,
      "default_payment_method"=>nil,
      "application_fee_percent"=>nil,
      "billing_cycle_anchor_config"=>nil,
      "pending_invoice_item_interval"=>nil,
      "next_pending_invoice_item_invoice"=>nil}},
  created_at: Sun, 29 Jun 2025 00:04:08.********* UTC +00:00,
  updated_at: Sun, 29 Jun 2025 00:13:38.********* UTC +00:00,
  amount_cents: 2800,
  amount_currency: "USD",
  origin: "https://theprimeapex.sabionet.com",
  provider_id: "pi_3Rf8j5EXjsbbUbB90RF3y8zt",
  price_id: 6555,
  payment_method_id: 2097,
  coupon_id: nil,
  quantity: 1,
  payment_type: "subscription",
  visibility: true>,
 #<Purchases::ProductPurchase:0x00007ff282a63e08
  id: 85868,
  tenant_id: 9605,
  user_id: 314461,
  product_type: "Courses::Course",
  product_id: 15122,
  status: "approved",
  provider: "stripe_connect",
  provider_data:
   {"verify_response_data"=>
     {"id"=>"pi_3RSrTsEXjsbbUbB91qNjEDAK",
      "amount"=>2800,
      "object"=>"payment_intent",
      "review"=>nil,
      "source"=>nil,
      "status"=>"succeeded",
      "created"=>**********,
      "invoice"=>"in_1RSrTsEXjsbbUbB9BHygy9v9",
      "currency"=>"usd",
      "customer"=>"cus_SNcj79XJUILmaq",
      "livemode"=>true,
      "metadata"=>{},
      "shipping"=>nil,
      "processing"=>nil,
      "application"=>nil,
      "canceled_at"=>nil,
      "description"=>"Subscription creation",
      "next_action"=>nil,
      "on_behalf_of"=>nil,
      "client_secret"=>"pi_3RSrTsEXjsbbUbB91qNjEDAK_secret_OlFCHugGEJuIieDNl4sU054EX",
      "latest_charge"=>"py_3RSrTsEXjsbbUbB91LzXmPKE",
      "receipt_email"=>nil,
      "transfer_data"=>{"amount"=>2686, "destination"=>"acct_1RBpCSIzpKcUhluP"},
      "amount_details"=>{"tip"=>{}},
      "capture_method"=>"automatic",
      "payment_method"=>"pm_1RSrUXEXjsbbUbB9P56UQNlU",
      "transfer_group"=>"group_py_3RSrTsEXjsbbUbB91LzXmPKE",
      "amount_received"=>2800,
      "amount_capturable"=>0,
      "last_payment_error"=>nil,
      "setup_future_usage"=>"off_session",
      "cancellation_reason"=>nil,
      "confirmation_method"=>"automatic",
      "payment_method_types"=>["card", "cashapp", "link", "us_bank_account"],
      "statement_descriptor"=>nil,
      "application_fee_amount"=>nil,
      "payment_method_options"=>
       {"card"=>{"network"=>nil, "installments"=>nil, "mandate_options"=>nil, "request_three_d_secure"=>"automatic"},
        "link"=>{"persistent_token"=>nil},
        "cashapp"=>{},
        "us_bank_account"=>{"mandate_options"=>{}, "verification_method"=>"automatic"}},
      "automatic_payment_methods"=>nil,
      "statement_descriptor_suffix"=>nil,
      "payment_method_configuration_details"=>nil},
    "initiate_request_data"=>
     {"items"=>[{"price"=>"price_1RC58HEXjsbbUbB9nI69DsVK"}],
      "expand"=>["latest_invoice.payment_intent"],
      "customer"=>"cus_SNcj79XJUILmaq",
      "metadata"=>{"transaction_id"=>nil},
      "transfer_data"=>{"destination"=>"acct_1RBpCSIzpKcUhluP", "amount_percent"=>95.93},
      "payment_behavior"=>"default_incomplete",
      "payment_settings"=>{"save_default_payment_method"=>"on_subscription"}},
    "initiate_response_data"=>
     {"id"=>"sub_1RSrTsEXjsbbUbB9CwbA0OFR",
      "plan"=>
       {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
        "meter"=>nil,
        "active"=>true,
        "amount"=>2800,
        "object"=>"plan",
        "created"=>**********,
        "product"=>"prod_S6HLJUbgynrskK",
        "currency"=>"usd",
        "interval"=>"month",
        "livemode"=>true,
        "metadata"=>{},
        "nickname"=>nil,
        "tiers_mode"=>nil,
        "usage_type"=>"licensed",
        "amount_decimal"=>"2800",
        "billing_scheme"=>"per_unit",
        "interval_count"=>1,
        "aggregate_usage"=>nil,
        "transform_usage"=>nil,
        "trial_period_days"=>nil},
      "items"=>
       {"url"=>"/v1/subscription_items?subscription=sub_1RSrTsEXjsbbUbB9CwbA0OFR",
        "data"=>
         [{"id"=>"si_SNcjb12aX20eRX",
           "plan"=>
            {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
             "meter"=>nil,
             "active"=>true,
             "amount"=>2800,
             "object"=>"plan",
             "created"=>**********,
             "product"=>"prod_S6HLJUbgynrskK",
             "currency"=>"usd",
             "interval"=>"month",
             "livemode"=>true,
             "metadata"=>{},
             "nickname"=>nil,
             "tiers_mode"=>nil,
             "usage_type"=>"licensed",
             "amount_decimal"=>"2800",
             "billing_scheme"=>"per_unit",
             "interval_count"=>1,
             "aggregate_usage"=>nil,
             "transform_usage"=>nil,
             "trial_period_days"=>nil},
           "price"=>
            {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
             "type"=>"recurring",
             "active"=>true,
             "object"=>"price",
             "created"=>**********,
             "product"=>"prod_S6HLJUbgynrskK",
             "currency"=>"usd",
             "livemode"=>true,
             "metadata"=>{},
             "nickname"=>nil,
             "recurring"=>{"meter"=>nil, "interval"=>"month", "usage_type"=>"licensed", "interval_count"=>1, "aggregate_usage"=>nil, "trial_period_days"=>nil},
             "lookup_key"=>nil,
             "tiers_mode"=>nil,
             "unit_amount"=>2800,
             "tax_behavior"=>"unspecified",
             "billing_scheme"=>"per_unit",
             "custom_unit_amount"=>nil,
             "transform_quantity"=>nil,
             "unit_amount_decimal"=>"2800"},
           "object"=>"subscription_item",
           "created"=>**********,
           "metadata"=>{},
           "quantity"=>1,
           "discounts"=>[],
           "tax_rates"=>[],
           "subscription"=>"sub_1RSrTsEXjsbbUbB9CwbA0OFR",
           "billing_thresholds"=>nil,
           "current_period_end"=>**********,
           "current_period_start"=>**********}],
        "object"=>"list",
        "has_more"=>false,
        "total_count"=>1},
      "object"=>"subscription",
      "status"=>"incomplete",
      "created"=>**********,
      "currency"=>"usd",
      "customer"=>"cus_SNcj79XJUILmaq",
      "discount"=>nil,
      "ended_at"=>nil,
      "livemode"=>true,
      "metadata"=>{},
      "quantity"=>1,
      "schedule"=>nil,
      "cancel_at"=>nil,
      "discounts"=>[],
      "trial_end"=>nil,
      "start_date"=>**********,
      "test_clock"=>nil,
      "application"=>nil,
      "canceled_at"=>nil,
      "description"=>nil,
      "trial_start"=>nil,
      "on_behalf_of"=>nil,
      "automatic_tax"=>{"enabled"=>false, "liability"=>nil, "disabled_reason"=>nil},
      "transfer_data"=>{"destination"=>"acct_1RBpCSIzpKcUhluP", "amount_percent"=>95.93},
      "days_until_due"=>nil,
      "default_source"=>nil,
      "latest_invoice"=>
       {"id"=>"in_1RSrTsEXjsbbUbB9BHygy9v9",
        "tax"=>nil,
        "paid"=>false,
        "lines"=>
         {"url"=>"/v1/invoices/in_1RSrTsEXjsbbUbB9BHygy9v9/lines",
          "data"=>
           [{"id"=>"il_1RSrTsEXjsbbUbB997sSJOaN",
             "plan"=>
              {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
               "meter"=>nil,
               "active"=>true,
               "amount"=>2800,
               "object"=>"plan",
               "created"=>**********,
               "product"=>"prod_S6HLJUbgynrskK",
               "currency"=>"usd",
               "interval"=>"month",
               "livemode"=>true,
               "metadata"=>{},
               "nickname"=>nil,
               "tiers_mode"=>nil,
               "usage_type"=>"licensed",
               "amount_decimal"=>"2800",
               "billing_scheme"=>"per_unit",
               "interval_count"=>1,
               "aggregate_usage"=>nil,
               "transform_usage"=>nil,
               "trial_period_days"=>nil},
             "type"=>"subscription",
             "price"=>
              {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
               "type"=>"recurring",
               "active"=>true,
               "object"=>"price",
               "created"=>**********,
               "product"=>"prod_S6HLJUbgynrskK",
               "currency"=>"usd",
               "livemode"=>true,
               "metadata"=>{},
               "nickname"=>nil,
               "recurring"=>{"meter"=>nil, "interval"=>"month", "usage_type"=>"licensed", "interval_count"=>1, "aggregate_usage"=>nil, "trial_period_days"=>nil},
               "lookup_key"=>nil,
               "tiers_mode"=>nil,
               "unit_amount"=>2800,
               "tax_behavior"=>"unspecified",
               "billing_scheme"=>"per_unit",
               "custom_unit_amount"=>nil,
               "transform_quantity"=>nil,
               "unit_amount_decimal"=>"2800"},
             "taxes"=>[],
             "amount"=>2800,
             "object"=>"line_item",
             "parent"=>
              {"type"=>"subscription_item_details",
               "invoice_item_details"=>nil,
               "subscription_item_details"=>
                {"proration"=>false, "invoice_item"=>nil, "subscription"=>"sub_1RSrTsEXjsbbUbB9CwbA0OFR", "proration_details"=>{"credited_items"=>nil}, "subscription_item"=>"si_SNcjb12aX20eRX"}},
             "period"=>{"end"=>**********, "start"=>**********},
             "invoice"=>"in_1RSrTsEXjsbbUbB9BHygy9v9",
             "pricing"=>{"type"=>"price_details", "price_details"=>{"price"=>"price_1RC58HEXjsbbUbB9nI69DsVK", "product"=>"prod_S6HLJUbgynrskK"}, "unit_amount_decimal"=>"2800"},
             "currency"=>"usd",
             "livemode"=>true,
             "metadata"=>{},
             "quantity"=>1,
             "discounts"=>[],
             "proration"=>false,
             "tax_rates"=>[],
             "description"=>"1 × THE APEX PRIME (at $28.00 / month)",
             "tax_amounts"=>[],
             "discountable"=>true,
             "subscription"=>"sub_1RSrTsEXjsbbUbB9CwbA0OFR",
             "discount_amounts"=>[],
             "proration_details"=>{"credited_items"=>nil},
             "subscription_item"=>"si_SNcjb12aX20eRX",
             "amount_excluding_tax"=>2800,
             "pretax_credit_amounts"=>[],
             "unit_amount_excluding_tax"=>"2800"}],
          "object"=>"list",
          "has_more"=>false,
          "total_count"=>1},
        "quote"=>nil,
        "total"=>2800,
        "charge"=>nil,
        "footer"=>nil,
        "issuer"=>{"type"=>"self"},
        "number"=>"FPNHLJIH-0001",
        "object"=>"invoice",
        "parent"=>{"type"=>"subscription_details", "quote_details"=>nil, "subscription_details"=>{"metadata"=>{}, "subscription"=>"sub_1RSrTsEXjsbbUbB9CwbA0OFR"}},
        "status"=>"open",
        "created"=>**********,
        "currency"=>"usd",
        "customer"=>"cus_SNcj79XJUILmaq",
        "discount"=>nil,
        "due_date"=>nil,
        "livemode"=>true,
        "metadata"=>{},
        "subtotal"=>2800,
        "attempted"=>false,
        "discounts"=>[],
        "rendering"=>nil,
        "amount_due"=>2800,
        "period_end"=>**********,
        "test_clock"=>nil,
        "amount_paid"=>0,
        "application"=>nil,
        "description"=>nil,
        "invoice_pdf"=>"https://pay.stripe.com/invoice/acct_1PYB9REXjsbbUbB9/live_YWNjdF8xUFlCOVJFWGpzYmJVYkI5LF9TTmNqdmtia2kzRDBGNFBLc3h1cjNzMmR6Z0ZTTUhQLDEzODc3MDAyMQ0200GB65b5sR/pdf?s=ap",
        "total_taxes"=>[],
        "account_name"=>"Sabiomarket, LLC",
        "auto_advance"=>false,
        "effective_at"=>**********,
        "from_invoice"=>nil,
        "on_behalf_of"=>nil,
        "period_start"=>**********,
        "subscription"=>"sub_1RSrTsEXjsbbUbB9CwbA0OFR",
        "attempt_count"=>0,
        "automatic_tax"=>{"status"=>nil, "enabled"=>false, "provider"=>nil, "liability"=>nil, "disabled_reason"=>nil},
        "custom_fields"=>nil,
        "customer_name"=>"Juan Martinez A",
        "shipping_cost"=>nil,
        "transfer_data"=>{"amount"=>2686, "destination"=>"acct_1RBpCSIzpKcUhluP"},
        "billing_reason"=>"subscription_create",
        "customer_email"=>"<EMAIL>",
        "customer_phone"=>nil,
        "default_source"=>nil,
        "ending_balance"=>0,
        "payment_intent"=>
         {"id"=>"pi_3RSrTsEXjsbbUbB91qNjEDAK",
          "amount"=>2800,
          "object"=>"payment_intent",
          "review"=>nil,
          "source"=>nil,
          "status"=>"requires_payment_method",
          "created"=>**********,
          "invoice"=>"in_1RSrTsEXjsbbUbB9BHygy9v9",
          "currency"=>"usd",
          "customer"=>"cus_SNcj79XJUILmaq",
          "livemode"=>true,
          "metadata"=>{},
          "shipping"=>nil,
          "processing"=>nil,
          "application"=>nil,
          "canceled_at"=>nil,
          "description"=>"Subscription creation",
          "next_action"=>nil,
          "on_behalf_of"=>nil,
          "client_secret"=>"pi_3RSrTsEXjsbbUbB91qNjEDAK_secret_OlFCHugGEJuIieDNl4sU054EX",
          "latest_charge"=>nil,
          "receipt_email"=>nil,
          "transfer_data"=>{"amount"=>2686, "destination"=>"acct_1RBpCSIzpKcUhluP"},
          "amount_details"=>{"tip"=>{}},
          "capture_method"=>"automatic",
          "payment_method"=>nil,
          "transfer_group"=>nil,
          "amount_received"=>0,
          "amount_capturable"=>0,
          "last_payment_error"=>nil,
          "setup_future_usage"=>"off_session",
          "cancellation_reason"=>nil,
          "confirmation_method"=>"automatic",
          "payment_method_types"=>["card", "cashapp", "link", "us_bank_account"],
          "statement_descriptor"=>nil,
          "application_fee_amount"=>nil,
          "payment_method_options"=>
           {"card"=>{"network"=>nil, "installments"=>nil, "mandate_options"=>nil, "request_three_d_secure"=>"automatic"},
            "link"=>{"persistent_token"=>nil},
            "cashapp"=>{},
            "us_bank_account"=>{"mandate_options"=>{}, "verification_method"=>"automatic"}},
          "automatic_payment_methods"=>nil,
          "statement_descriptor_suffix"=>nil,
          "payment_method_configuration_details"=>nil},
        "receipt_number"=>nil,
        "account_country"=>"US",
        "account_tax_ids"=>nil,
        "amount_overpaid"=>0,
        "amount_shipping"=>0,
        "latest_revision"=>nil,
        "amount_remaining"=>2800,
        "customer_address"=>nil,
        "customer_tax_ids"=>[],
        "paid_out_of_band"=>false,
        "payment_settings"=>{"default_mandate"=>nil, "payment_method_types"=>nil, "payment_method_options"=>nil},
        "shipping_details"=>nil,
        "starting_balance"=>0,
        "collection_method"=>"charge_automatically",
        "customer_shipping"=>nil,
        "default_tax_rates"=>[],
        "total_tax_amounts"=>[],
        "hosted_invoice_url"=>"https://invoice.stripe.com/i/acct_1PYB9REXjsbbUbB9/live_YWNjdF8xUFlCOVJFWGpzYmJVYkI5LF9TTmNqdmtia2kzRDBGNFBLc3h1cjNzMmR6Z0ZTTUhQLDEzODc3MDAyMQ0200GB65b5sR?s=ap",
        "status_transitions"=>{"paid_at"=>nil, "voided_at"=>nil, "finalized_at"=>**********, "marked_uncollectible_at"=>nil},
        "customer_tax_exempt"=>"none",
        "total_excluding_tax"=>2800,
        "next_payment_attempt"=>nil,
        "statement_descriptor"=>nil,
        "subscription_details"=>{"metadata"=>{}},
        "webhooks_delivered_at"=>nil,
        "application_fee_amount"=>nil,
        "default_payment_method"=>nil,
        "subtotal_excluding_tax"=>2800,
        "total_discount_amounts"=>[],
        "last_finalization_error"=>nil,
        "automatically_finalizes_at"=>nil,
        "total_pretax_credit_amounts"=>[],
        "pre_payment_credit_notes_amount"=>0,
        "post_payment_credit_notes_amount"=>0},
      "pending_update"=>nil,
      "trial_settings"=>{"end_behavior"=>{"missing_payment_method"=>"create_invoice"}},
      "invoice_settings"=>{"issuer"=>{"type"=>"self"}, "account_tax_ids"=>nil},
      "pause_collection"=>nil,
      "payment_settings"=>{"payment_method_types"=>nil, "payment_method_options"=>nil, "save_default_payment_method"=>"on_subscription"},
      "collection_method"=>"charge_automatically",
      "default_tax_rates"=>[],
      "billing_thresholds"=>nil,
      "current_period_end"=>**********,
      "billing_cycle_anchor"=>**********,
      "cancel_at_period_end"=>false,
      "cancellation_details"=>{"reason"=>nil, "comment"=>nil, "feedback"=>nil},
      "current_period_start"=>**********,
      "pending_setup_intent"=>nil,
      "default_payment_method"=>nil,
      "application_fee_percent"=>nil,
      "billing_cycle_anchor_config"=>nil,
      "pending_invoice_item_interval"=>nil,
      "next_pending_invoice_item_invoice"=>nil}},
  created_at: Sun, 29 Jun 2025 00:13:39.********* UTC +00:00,
  updated_at: Sun, 29 Jun 2025 00:13:39.********* UTC +00:00,
  amount_cents: 2800,
  amount_currency: "USD",
  origin: "https://theprimeapex.sabionet.com",
  provider_id: "pi_3RSrTsEXjsbbUbB91qNjEDAK",
  price_id: 6555,
  payment_method_id: 2097,
  coupon_id: nil,
  quantity: 1,
  payment_type: "subscription",
  visibility: true>,
 #<Purchases::ProductPurchase:0x00007ff282a63ca0
  id: 86813,
  tenant_id: 9605,
  user_id: 314461,
  product_type: "Courses::Course",
  product_id: 15122,
  status: "approved",
  provider: "stripe_connect",
  provider_data:
   {"verify_response_data"=>
     {"id"=>"pi_3RSrTsEXjsbbUbB91qNjEDAK",
      "amount"=>2800,
      "object"=>"payment_intent",
      "review"=>nil,
      "source"=>nil,
      "status"=>"succeeded",
      "created"=>**********,
      "invoice"=>"in_1RSrTsEXjsbbUbB9BHygy9v9",
      "currency"=>"usd",
      "customer"=>"cus_SNcj79XJUILmaq",
      "livemode"=>true,
      "metadata"=>{},
      "shipping"=>nil,
      "processing"=>nil,
      "application"=>nil,
      "canceled_at"=>nil,
      "description"=>"Subscription creation",
      "next_action"=>nil,
      "on_behalf_of"=>nil,
      "client_secret"=>"pi_3RSrTsEXjsbbUbB91qNjEDAK_secret_OlFCHugGEJuIieDNl4sU054EX",
      "latest_charge"=>"py_3RSrTsEXjsbbUbB91LzXmPKE",
      "receipt_email"=>nil,
      "transfer_data"=>{"amount"=>2686, "destination"=>"acct_1RBpCSIzpKcUhluP"},
      "amount_details"=>{"tip"=>{}},
      "capture_method"=>"automatic",
      "payment_method"=>"pm_1RSrUXEXjsbbUbB9P56UQNlU",
      "transfer_group"=>"group_py_3RSrTsEXjsbbUbB91LzXmPKE",
      "amount_received"=>2800,
      "amount_capturable"=>0,
      "last_payment_error"=>nil,
      "setup_future_usage"=>"off_session",
      "cancellation_reason"=>nil,
      "confirmation_method"=>"automatic",
      "payment_method_types"=>["card", "cashapp", "link", "us_bank_account"],
      "statement_descriptor"=>nil,
      "application_fee_amount"=>nil,
      "payment_method_options"=>
       {"card"=>{"network"=>nil, "installments"=>nil, "mandate_options"=>nil, "request_three_d_secure"=>"automatic"},
        "link"=>{"persistent_token"=>nil},
        "cashapp"=>{},
        "us_bank_account"=>{"mandate_options"=>{}, "verification_method"=>"automatic"}},
      "automatic_payment_methods"=>nil,
      "statement_descriptor_suffix"=>nil,
      "payment_method_configuration_details"=>nil},
    "initiate_request_data"=>
     {"items"=>[{"price"=>"price_1RC58HEXjsbbUbB9nI69DsVK"}],
      "expand"=>["latest_invoice.payment_intent"],
      "customer"=>"cus_SNcj79XJUILmaq",
      "metadata"=>{"transaction_id"=>nil},
      "transfer_data"=>{"destination"=>"acct_1RBpCSIzpKcUhluP", "amount_percent"=>95.93},
      "payment_behavior"=>"default_incomplete",
      "payment_settings"=>{"save_default_payment_method"=>"on_subscription"}},
    "initiate_response_data"=>
     {"id"=>"sub_1RSrTsEXjsbbUbB9CwbA0OFR",
      "plan"=>
       {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
        "meter"=>nil,
        "active"=>true,
        "amount"=>2800,
        "object"=>"plan",
        "created"=>**********,
        "product"=>"prod_S6HLJUbgynrskK",
        "currency"=>"usd",
        "interval"=>"month",
        "livemode"=>true,
        "metadata"=>{},
        "nickname"=>nil,
        "tiers_mode"=>nil,
        "usage_type"=>"licensed",
        "amount_decimal"=>"2800",
        "billing_scheme"=>"per_unit",
        "interval_count"=>1,
        "aggregate_usage"=>nil,
        "transform_usage"=>nil,
        "trial_period_days"=>nil},
      "items"=>
       {"url"=>"/v1/subscription_items?subscription=sub_1RSrTsEXjsbbUbB9CwbA0OFR",
        "data"=>
         [{"id"=>"si_SNcjb12aX20eRX",
           "plan"=>
            {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
             "meter"=>nil,
             "active"=>true,
             "amount"=>2800,
             "object"=>"plan",
             "created"=>**********,
             "product"=>"prod_S6HLJUbgynrskK",
             "currency"=>"usd",
             "interval"=>"month",
             "livemode"=>true,
             "metadata"=>{},
             "nickname"=>nil,
             "tiers_mode"=>nil,
             "usage_type"=>"licensed",
             "amount_decimal"=>"2800",
             "billing_scheme"=>"per_unit",
             "interval_count"=>1,
             "aggregate_usage"=>nil,
             "transform_usage"=>nil,
             "trial_period_days"=>nil},
           "price"=>
            {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
             "type"=>"recurring",
             "active"=>true,
             "object"=>"price",
             "created"=>**********,
             "product"=>"prod_S6HLJUbgynrskK",
             "currency"=>"usd",
             "livemode"=>true,
             "metadata"=>{},
             "nickname"=>nil,
             "recurring"=>{"meter"=>nil, "interval"=>"month", "usage_type"=>"licensed", "interval_count"=>1, "aggregate_usage"=>nil, "trial_period_days"=>nil},
             "lookup_key"=>nil,
             "tiers_mode"=>nil,
             "unit_amount"=>2800,
             "tax_behavior"=>"unspecified",
             "billing_scheme"=>"per_unit",
             "custom_unit_amount"=>nil,
             "transform_quantity"=>nil,
             "unit_amount_decimal"=>"2800"},
           "object"=>"subscription_item",
           "created"=>**********,
           "metadata"=>{},
           "quantity"=>1,
           "discounts"=>[],
           "tax_rates"=>[],
           "subscription"=>"sub_1RSrTsEXjsbbUbB9CwbA0OFR",
           "billing_thresholds"=>nil,
           "current_period_end"=>**********,
           "current_period_start"=>**********}],
        "object"=>"list",
        "has_more"=>false,
        "total_count"=>1},
      "object"=>"subscription",
      "status"=>"incomplete",
      "created"=>**********,
      "currency"=>"usd",
      "customer"=>"cus_SNcj79XJUILmaq",
      "discount"=>nil,
      "ended_at"=>nil,
      "livemode"=>true,
      "metadata"=>{},
      "quantity"=>1,
      "schedule"=>nil,
      "cancel_at"=>nil,
      "discounts"=>[],
      "trial_end"=>nil,
      "start_date"=>**********,
      "test_clock"=>nil,
      "application"=>nil,
      "canceled_at"=>nil,
      "description"=>nil,
      "trial_start"=>nil,
      "on_behalf_of"=>nil,
      "automatic_tax"=>{"enabled"=>false, "liability"=>nil, "disabled_reason"=>nil},
      "transfer_data"=>{"destination"=>"acct_1RBpCSIzpKcUhluP", "amount_percent"=>95.93},
      "days_until_due"=>nil,
      "default_source"=>nil,
      "latest_invoice"=>
       {"id"=>"in_1RSrTsEXjsbbUbB9BHygy9v9",
        "tax"=>nil,
        "paid"=>false,
        "lines"=>
         {"url"=>"/v1/invoices/in_1RSrTsEXjsbbUbB9BHygy9v9/lines",
          "data"=>
           [{"id"=>"il_1RSrTsEXjsbbUbB997sSJOaN",
             "plan"=>
              {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
               "meter"=>nil,
               "active"=>true,
               "amount"=>2800,
               "object"=>"plan",
               "created"=>**********,
               "product"=>"prod_S6HLJUbgynrskK",
               "currency"=>"usd",
               "interval"=>"month",
               "livemode"=>true,
               "metadata"=>{},
               "nickname"=>nil,
               "tiers_mode"=>nil,
               "usage_type"=>"licensed"debug2: channel 0: window 983040 sent adjust 65536
,
               "amount_decimal"=>"2800",
               "billing_scheme"=>"per_unit",
               "interval_count"=>1,
               "aggregate_usage"=>nil,
               "transform_usage"=>nil,
               "trial_period_days"=>nil},
             "type"=>"subscription",
             "price"=>
              {"id"=>"price_1RC58HEXjsbbUbB9nI69DsVK",
               "type"=>"recurring",
               "active"=>true,
               "object"=>"price",
               "created"=>**********,
               "product"=>"prod_S6HLJUbgynrskK",
               "currency"=>"usd",
               "livemode"=>true,
               "metadata"=>{},
               "nickname"=>nil,
               "recurring"=>{"meter"=>nil, "interval"=>"month", "usage_type"=>"licensed", "interval_count"=>1, "aggregate_usage"=>nil, "trial_period_days"=>nil},
               "lookup_key"=>nil,
               "tiers_mode"=>nil,
               "unit_amount"=>2800,
               "tax_behavior"=>"unspecified",
               "billing_scheme"=>"per_unit",
               "custom_unit_amount"=>nil,
               "transform_quantity"=>nil,
               "unit_amount_decimal"=>"2800"},
             "taxes"=>[],
             "amount"=>2800,
             "object"=>"line_item",
             "parent"=>
              {"type"=>"subscription_item_details",
               "invoice_item_details"=>nil,
               "subscription_item_details"=>
                {"proration"=>false, "invoice_item"=>nil, "subscription"=>"sub_1RSrTsEXjsbbUbB9CwbA0OFR", "proration_details"=>{"credited_items"=>nil}, "subscription_item"=>"si_SNcjb12aX20eRX"}},
             "period"=>{"end"=>**********, "start"=>**********},
             "invoice"=>"in_1RSrTsEXjsbbUbB9BHygy9v9",
             "pricing"=>{"type"=>"price_details", "price_details"=>{"price"=>"price_1RC58HEXjsbbUbB9nI69DsVK", "product"=>"prod_S6HLJUbgynrskK"}, "unit_amount_decimal"=>"2800"},
             "currency"=>"usd",
             "livemode"=>true,
             "metadata"=>{},
             "quantity"=>1,
             "discounts"=>[],
             "proration"=>false,
             "tax_rates"=>[],
             "description"=>"1 × THE APEX PRIME (at $28.00 / month)",
             "tax_amounts"=>[],
             "discountable"=>true,
             "subscription"=>"sub_1RSrTsEXjsbbUbB9CwbA0OFR",
             "discount_amounts"=>[],
             "proration_details"=>{"credited_items"=>nil},
             "subscription_item"=>"si_SNcjb12aX20eRX",
             "amount_excluding_tax"=>2800,
             "pretax_credit_amounts"=>[],
             "unit_amount_excluding_tax"=>"2800"}],
          "object"=>"list",
          "has_more"=>false,
          "total_count"=>1},
        "quote"=>nil,
        "total"=>2800,
        "charge"=>nil,
        "footer"=>nil,
        "issuer"=>{"type"=>"self"},
        "number"=>"FPNHLJIH-0001",
        "object"=>"invoice",
        "parent"=>{"type"=>"subscription_details", "quote_details"=>nil, "subscription_details"=>{"metadata"=>{}, "subscription"=>"sub_1RSrTsEXjsbbUbB9CwbA0OFR"}},
        "status"=>"open",
        "created"=>**********,
        "currency"=>"usd",
        "customer"=>"cus_SNcj79XJUILmaq",
        "discount"=>nil,
        "due_date"=>nil,
        "livemode"=>true,
        "metadata"=>{},
        "subtotal"=>2800,
        "attempted"=>false,
        "discounts"=>[],
        "rendering"=>nil,
        "amount_due"=>2800,
        "period_end"=>**********,
        "test_clock"=>nil,
        "amount_paid"=>0,
        "application"=>nil,
        "description"=>nil,
        "invoice_pdf"=>"https://pay.stripe.com/invoice/acct_1PYB9REXjsbbUbB9/live_YWNjdF8xUFlCOVJFWGpzYmJVYkI5LF9TTmNqdmtia2kzRDBGNFBLc3h1cjNzMmR6Z0ZTTUhQLDEzODc3MDAyMQ0200GB65b5sR/pdf?s=ap",
        "total_taxes"=>[],
        "account_name"=>"Sabiomarket, LLC",
        "auto_advance"=>false,
        "effective_at"=>**********,
        "from_invoice"=>nil,
        "on_behalf_of"=>nil,
        "period_start"=>**********,
        "subscription"=>"sub_1RSrTsEXjsbbUbB9CwbA0OFR",
        "attempt_count"=>0,
        "automatic_tax"=>{"status"=>nil, "enabled"=>false, "provider"=>nil, "liability"=>nil, "disabled_reason"=>nil},
        "custom_fields"=>nil,
        "customer_name"=>"Juan Martinez A",
        "shipping_cost"=>nil,
        "transfer_data"=>{"amount"=>2686, "destination"=>"acct_1RBpCSIzpKcUhluP"},
        "billing_reason"=>"subscription_create",
        "customer_email"=>"<EMAIL>",
        "customer_phone"=>nil,
        "default_source"=>nil,
        "ending_balance"=>0,
        "payment_intent"=>
         {"id"=>"pi_3RSrTsEXjsbbUbB91qNjEDAK",
          "amount"=>2800,
          "object"=>"payment_intent",
          "review"=>nil,
          "source"=>nil,
          "status"=>"requires_payment_method",
          "created"=>**********,
          "invoice"=>"in_1RSrTsEXjsbbUbB9BHygy9v9",
          "currency"=>"usd",
          "customer"=>"cus_SNcj79XJUILmaq",
          "livemode"=>true,
          "metadata"=>{},
          "shipping"=>nil,
          "processing"=>nil,
          "application"=>nil,
          "canceled_at"=>nil,
          "description"=>"Subscription creation",
          "next_action"=>nil,
          "on_behalf_of"=>nil,
          "client_secret"=>"pi_3RSrTsEXjsbbUbB91qNjEDAK_secret_OlFCHugGEJuIieDNl4sU054EX",
          "latest_charge"=>nil,
          "receipt_email"=>nil,
          "transfer_data"=>{"amount"=>2686, "destination"=>"acct_1RBpCSIzpKcUhluP"},
          "amount_details"=>{"tip"=>{}},
          "capture_method"=>"automatic",
          "payment_method"=>nil,
          "transfer_group"=>nil,
          "amount_received"=>0,
          "amount_capturable"=>0,
          "last_payment_error"=>nil,
          "setup_future_usage"=>"off_session",
          "cancellation_reason"=>nil,
          "confirmation_method"=>"automatic",
          "payment_method_types"=>["card", "cashapp", "link", "us_bank_account"],
          "statement_descriptor"=>nil,
          "application_fee_amount"=>nil,
          "payment_method_options"=>
           {"card"=>{"network"=>nil, "installments"=>nil, "mandate_options"=>nil, "request_three_d_secure"=>"automatic"},
            "link"=>{"persistent_token"=>nil},
            "cashapp"=>{},
            "us_bank_account"=>{"mandate_options"=>{}, "verification_method"=>"automatic"}},
          "automatic_payment_methods"=>nil,
          "statement_descriptor_suffix"=>nil,
          "payment_method_configuration_details"=>nil},
        "receipt_number"=>nil,
        "account_country"=>"US",
        "account_tax_ids"=>nil,
        "amount_overpaid"=>0,
        "amount_shipping"=>0,
        "latest_revision"=>nil,
        "amount_remaining"=>2800,
        "customer_address"=>nil,
        "customer_tax_ids"=>[],
        "paid_out_of_band"=>false,
        "payment_settings"=>{"default_mandate"=>nil, "payment_method_types"=>nil, "payment_method_options"=>nil},
        "shipping_details"=>nil,
        "starting_balance"=>0,
        "collection_method"=>"charge_automatically",
        "customer_shipping"=>nil,
        "default_tax_rates"=>[],
        "total_tax_amounts"=>[],
        "hosted_invoice_url"=>"https://invoice.stripe.com/i/acct_1PYB9REXjsbbUbB9/live_YWNjdF8xUFlCOVJFWGpzYmJVYkI5LF9TTmNqdmtia2kzRDBGNFBLc3h1cjNzMmR6Z0ZTTUhQLDEzODc3MDAyMQ0200GB65b5sR?s=ap",
        "status_transitions"=>{"paid_at"=>nil, "voided_at"=>nil, "finalized_at"=>**********, "marked_uncollectible_at"=>nil},
        "customer_tax_exempt"=>"none",
        "total_excluding_tax"=>2800,
        "next_payment_attempt"=>nil,
        "statement_descriptor"=>nil,
        "subscription_details"=>{"metadata"=>{}},
        "webhooks_delivered_at"=>nil,
        "application_fee_amount"=>nil,
        "default_payment_method"=>nil,
        "subtotal_excluding_tax"=>2800,
        "total_discount_amounts"=>[],
        "last_finalization_error"=>nil,
        "automatically_finalizes_at"=>nil,
        "total_pretax_credit_amounts"=>[],
        "pre_payment_credit_notes_amount"=>0,
        "post_payment_credit_notes_amount"=>0},
      "pending_update"=>nil,
      "trial_settings"=>{"end_behavior"=>{"missing_payment_method"=>"create_invoice"}},
      "invoice_settings"=>{"issuer"=>{"type"=>"self"}, "account_tax_ids"=>nil},
      "pause_collection"=>nil,
      "payment_settings"=>{"payment_method_types"=>nil, "payment_method_options"=>nil, "save_default_payment_method"=>"on_subscription"},
      "collection_method"=>"charge_automatically",
      "default_tax_rates"=>[],
      "billing_thresholds"=>nil,
      "current_period_end"=>**********,
      "billing_cycle_anchor"=>**********,
      "cancel_at_period_end"=>false,
      "cancellation_details"=>{"reason"=>nil, "comment"=>nil, "feedback"=>nil},
      "current_period_start"=>**********,
      "pending_setup_intent"=>nil,
      "default_payment_method"=>nil,
      "application_fee_percent"=>nil,
      "billing_cycle_anchor_config"=>nil,
      "pending_invoice_item_interval"=>nil,
      "next_pending_invoice_item_invoice"=>nil}},
  created_at: Tue, 29 Jul 2025 01:06:50.********* UTC +00:00,
  updated_at: Tue, 29 Jul 2025 01:06:50.********* UTC +00:00,
  amount_cents: 2800,
  amount_currency: "USD",
  origin: "https://theprimeapex.sabionet.com",
  provider_id: "pi_3RSrTsEXjsbbUbB91qNjEDAK",
  price_id: 6555,
  payment_method_id: 2097,
  coupon_id: nil,
  quantity: 1,
  payment_type: "subscription",
  visibility: true>]
