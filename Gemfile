source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby '3.2.1'

# Bundle edge Rails instead: gem 'rails', github: 'rails/rails', branch: 'main'
gem 'rails', '~> 7.0'
gem 'strscan', '3.0.1'
# Use postgresql as the database for Active Record
gem 'pg', '~> 1.4'
gem 'scenic' # for database views
# Use Puma as the app server
gem 'puma', '< 7'
# Build JSON APIs with ease. Read more: https://github.com/rails/jbuilder
# gem 'jbuilder', '~> 2.7'
# Use Redis adapter to run Action Cable in production
 gem 'redis', '~> 5.0'
# Use Active Model has_secure_password
# gem 'bcrypt', '~> 3.1.7'

# Reduces boot times through caching; required in config/boot.rb
gem 'bootsnap', '>= 1.10.1', require: false

# Use Rack CORS for handling Cross-Origin Resource Sharing (CORS), making cross-origin AJAX possible
# gem 'rack-cors'

# Auth
gem 'devise', '>= 4.9.0'
#gem "devise"#, github: "heartcombo/devise", branch: "master"
gem 'devise_token_auth', '~> 1.2' # git: 'https://github.com/camiloforero/devise_token_auth.git', branch: 'rails7'
gem "sorcery"

#google
gem 'omniauth'
gem 'omniauth-facebook'
gem 'omniauth-google-oauth2'

# General purpose gems
gem 'acts_as_list'
gem 'counter_culture', '~> 3.2'
gem 'normalize_attributes'

# Data analytics
gem 'jwt'

# Use GraphQL
gem 'graphlient'
gem 'graphql', '~> 2.0'
gem 'graphql-batch'

# Deal with CORS
gem 'rack-cors'

# Multitenancy support
gem 'multi-tenant-support'

# Localization, personalization and global data
gem 'countries', '~> 4.2'
gem 'IPinfo'
gem 'money-rails'
gem 'rails-i18n', '~> 7.0.6' # For 7.0.0

# Storage and files
gem 'content_disposition'
gem 'google-cloud-storage'
gem 'google-cloud-video-transcoder'
gem "shrine", "~> 3.4"
gem "shrine-google_cloud_storage", github: "/camiloforero/shrine-google_cloud_storage", ref: "b9649d8"
gem 'aws-sdk-s3'
# http 
gem 'httparty'

# Emails
gem 'postmark-rails'

gem 'MailchimpMarketing', '~> 3.0.80'

# trail
gem 'paper_trail'

# Workers
# gem 'sidekiq', '~> 6.4'
gem 'sidekiq', '< 8'

# Error logging and APM
gem 'newrelic_rpm'
gem 'rollbar'


# Authorization
gem 'pundit', '~> 2.2'

# PDF generation
gem 'prawn', '~> 2.4'
gem 'matrix'

# Batch imports
gem 'smarter_csv', github: 'tilo/smarter_csv', ref: '14b7b06'
gem "roo", "~> 2.8.0"

# File processing
gem 'image_processing', '~> 1.12'
gem 'mini_magick'

# Hubspot
gem 'hubspot-api-client', '< 15'


# Payments
gem 'mercadopago-sdk', '~> 2.1'
gem 'paypal-checkout-sdk', '~> 1.0'
gem 'paypalhttp', github: 'camiloforero/paypalhttp_ruby', ref: 'b926776'
gem 'stripe', '~> 6.2'

gem 'kaminari' # For Server Side Pagination
gem 'dnsruby', '~> 1.59', '>= 1.59.3'
gem 'tinyurl'
gem 'gibbon'

# Gems for generating QR codes with image output support
gem 'rqrcode'       # Used to generate QR codes
gem 'chunky_png'    # Adds PNG image rendering support for rqrcode

gem 'wicked_pdf'
gem 'wkhtmltopdf-binary' # or install manually from https://wkhtmltopdf.org/

group :development, :test do
  gem "debug", ">= 1.0.0"
  gem 'factory_bot_rails'
  gem 'faker'
  gem 'rspec-rails', '~> 5.1'
  gem 'dotenv-rails'
  gem 'pry'
end

group :development do
  gem 'listen', '~> 3.7'
  # Spring speeds up development by keeping your application running in the background. Read more: https://github.com/rails/spring
  gem 'solargraph'
  gem 'spring'

  # linting and code quality
  gem 'rubocop', require: false
  gem 'rubocop-performance', require: false
  gem 'rubocop-rails', require: false
  gem 'rubocop-rspec', require: false
end

group :test do
  gem 'database_cleaner-active_record'
end

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem 'tzinfo-data', platforms: [:mingw, :mswin, :x64_mingw, :jruby]
