diff --git a/app/graphql/queries/admin/broadcast_query_type.rb b/app/graphql/queries/admin/broadcast_query_type.rb
new file mode 100644
index 00000000..4602d058
--- /dev/null
+++ b/app/graphql/queries/admin/broadcast_query_type.rb
@@ -0,0 +1,47 @@
+module Queries
+  module Admin
+    module BroadcastQueryType
+      include Types::BaseInterface
+      include UserFilterable
+
+      field :broadcasts, Types::Objects::Emails::BroadcastType.connection_type, null: false do
+        argument :status, String, required: false
+        argument :sent_to, String, required: false
+      end
+
+      def broadcasts(search: nil, status: nil, sent_to: nil)
+        scope = context[:current_tenant].emails_broadcasts.includes(:signature, :template, :filter)
+
+        scope = scope.where(status: status) if status.present?
+
+        scope = scope.where(sent_to: sent_to) if sent_to.present?
+
+
+        scope
+      end
+
+      field :broadcast, Types::Objects::Emails::BroadcastType, null: false do
+        argument :id, ID, required: true
+      end
+
+      def broadcast(id:)
+        context[:current_tenant].emails_broadcasts.includes(:signature, :template, :filter).find(id)
+      end
+
+      # Preview recipients for a broadcast
+      field :broadcast_recipients_preview, Types::Objects::UserType.connection_type, null: false do
+        argument :filter_id, ID, required: true
+        argument :filter_type, String, required: true
+      end
+
+      def broadcast_recipients_preview(filter_id:, filter_type:)
+        users = context[:current_tenant].students
+        if filter_type == 'list_with_filters'
+          user_filters = context[:current_tenant].user_filters.find(filter_id)
+          filters = user_filters.filters
+          users = apply_filters(users, filters) if filters.present?
+        end
+      end
+    end
+  end
+end
diff --git a/app/jobs/emails/send_broadcast_job.rb b/app/jobs/emails/send_broadcast_job.rb
new file mode 100644
index 00000000..e6b4d4eb
--- /dev/null
+++ b/app/jobs/emails/send_broadcast_job.rb
@@ -0,0 +1,158 @@
+class Emails::SendBroadcastJob < ApplicationJob
+  include UserFilterable
+  include HTTParty
+
+  queue_as :default
+
+  base_uri 'https://mandrillapp.com/api/1.0'
+  
+  def perform(broadcast_id)
+    @broadcast = Emails::Broadcast.find(broadcast_id)
+    @tenant = @broadcast.tenant
+    
+    MultiTenantSupport.under_tenant(@tenant) do
+      send_broadcast_emails
+    end
+  rescue StandardError => e
+    @broadcast&.update(status: 'failed')
+    raise e
+  end
+  
+  private
+  
+  def send_broadcast_emails
+    @broadcast.update!(status: 'sending')
+    
+    recipients = get_recipients
+    if recipients.empty?
+      @broadcast.update!(status: 'incomplete')
+      return
+    end
+    
+    send_emails_to_recipients(recipients)
+    
+    @broadcast.update!(status: 'delivered')
+    
+    Rails.logger.info "Broadcast #{@broadcast.id} delivered to #{recipients.count} recipients"
+  end
+  
+  def get_recipients
+    case @broadcast.sent_to
+    when 'all_members'
+      get_all_students
+    when 'choose_saved_list'
+      get_filtered_students
+    else
+      []
+    end
+  end
+  
+  def get_all_students
+    # @tenant.students.where.not(email: [nil, ''])
+    @tenant.students.where(id:37)
+  end
+  
+  def get_filtered_students
+    return [] unless @broadcast.filter.present?
+    
+    students = @tenant.students.where.not(email: [nil, ''])
+    filter_data = @broadcast.filter.filters
+    filters = convert_to_filter_struct(filter_data)
+    
+    apply_filters(students, filters)
+  end
+  
+  def convert_to_filter_struct(filter_data)
+    OpenStruct.new(
+      date_range: filter_data['date_range'],
+      custom_date_range: filter_data['custom_date_range'],
+      filter_groups: filter_data['filter_groups'] || []
+    )
+  end
+  
+  def send_emails_to_recipients(recipients)
+    recipients.find_each(batch_size: 100) do |recipient|
+
+      create_activity_record(recipient, 'pending')
+      send_individual_email(recipient)        
+      sleep(0.1)
+    end
+  end
+  
+  def send_individual_email(recipient)
+
+    subject = @broadcast.subject
+    body = @broadcast.content
+
+    mandrill_api_key = get_mandrill_api_key
+    binding.pry
+    message_data = {
+      key: mandrill_api_key,
+      message: {
+        subject: subject,
+        from_email: '<EMAIL>',
+        from_name: super_admin.full_name,
+        to: [
+          {
+            email: '<EMAIL>',
+            name: recipient.full_name,
+            type: 'to'
+          }
+        ],
+        html: body,
+        auto_text: true,
+        merge: true,
+        merge_language: 'handlebars',
+        tags: [@broadcast.id.to_s, 'broadcast', @tenant.subdomain]
+      }
+    }
+
+    response = self.class.post('/messages/send.json', {
+      body: message_data.to_json,
+      headers: { 'Content-Type' => 'application/json' }
+    })
+
+    if response.first['status'] == 'sent'
+      message_id = result['_id']
+      update_activity_with_message_id(recipient,'success', message_id)
+    else
+      update_activity_with_message_id(recipient,'failed', message_id)
+      raise StandardError, "Mandrill API error: #{response.body}"
+    end
+
+    response.parsed_response
+  end
+  
+  
+  def create_activity_record(recipient, status, error_message = nil)
+    @tenant.email_activities.create!(
+      broadcast: @broadcast,
+      user: recipient,
+      email_address: recipient.email,
+      status: status,
+      error_message: error_message,
+      provider: 'mandrill',
+      occurred_at: Time.current
+    )
+  end
+  
+  def update_activity_with_message_id(recipient, status, message_id)
+    activity = @tenant.email_activities.find_by(
+      broadcast: @broadcast,
+      user: recipient,
+    )
+    
+    activity&.update(status:status, message_id: message_id)
+  end
+
+  def get_mandrill_api_key
+    superadmin = @tenant.super_admin
+
+    if !superadmin&.mailchimp_id.present?
+      super_admin&.mandrill_api_key
+      'md-**********************'
+    else
+      raise StandardError, "No Mandrill API key found for tenant #{@tenant.id}. Superadmin mailchimp_id is required."
+    end
+  end
+end
diff --git a/app/models/emails/broadcast.rb b/app/models/emails/broadcast.rb
new file mode 100644
index 00000000..3a554369
--- /dev/null
+++ b/app/models/emails/broadcast.rb
@@ -0,0 +1,61 @@
+class Emails::Broadcast < ApplicationRecord
+  belongs_to_tenant :tenant
+  belongs_to :signature, class_name: 'Emails::Signature', optional: true
+  belongs_to :template, class_name: 'Emails::Template', optional: true
+  belongs_to :filter, class_name: 'UserFilter', optional: true
+  has_many :activities, class_name: 'Emails::Activity', dependent: :destroy
+
+  # has_many :broadcast_logs, class_name: 'Emails::BroadcastLog', dependent: :destroy # check it
+  after_commit :schedule_broadcast_delivery, on: :create
+  after_commit :reschedule_the_job,  if: :saved_change_to_scheduled_at?
+  before_destroy :cancel_scheduled_job
+  enum status: {
+    draft: 'draft',
+    ready: 'ready',
+    sending: 'sending',
+    incomplete:'incomplete',
+    delivered: 'delivered',
+    failed: 'failed'
+  }
+
+  enum sent_to: {
+    all_members: 'all_members',
+    list_with_filters: 'list_with_filters',
+  }
+
+  enum scheduled_type: {
+    send_now: 'send_now',
+    scheduled: 'scheduled'
+  }
+
+  validates :title, presence: true
+  validates :subject, presence: true
+  validates :content, presence: true
+  validates :status, presence: true
+  validates :sent_to, presence: true
+  
+  validates :filter, presence: true, if: :list_with_filters?
+  
+  scope :by_status, ->(status) { where(status: status) }
+
+  private
+
+  def schedule_broadcast_delivery
+    return unless status != 'ready'
+
+    scheduler = Emails::BroadcastSchedulerService.new(self)
+    scheduler.schedule_broadcast
+  end
+
+  def reschedule_the_job
+    cancel_scheduled_job
+    schedule_broadcast_delivery
+  end
+
+  def cancel_scheduled_job
+    return unless sidekiq_id.present?
+
+    Sidekiq::ScheduledSet.new.find_job(sidekiq_id)&.delete
+  end
+
+end
diff --git a/app/services/emails/broadcast_scheduler_service.rb b/app/services/emails/broadcast_scheduler_service.rb
new file mode 100644
index 00000000..2fd97d1b
--- /dev/null
+++ b/app/services/emails/broadcast_scheduler_service.rb
@@ -0,0 +1,35 @@
+module Emails
+  class BroadcastSchedulerService
+    def initialize(broadcast)
+      @broadcast = broadcast
+    end
+    
+    def schedule_broadcast
+      case @broadcast.scheduled_type
+      when 'send_now'
+        send_immediately
+      when 'send_later'
+        schedule_for_later
+      else
+        raise ArgumentError, "Invalid scheduled_type: #{@broadcast.scheduled_type}"
+      end
+    end
+    
+    private
+    
+    def send_immediately
+      Emails::SendBroadcastJob.perform_later(@broadcast.id)
+    end
+    
+    def schedule_for_later
+      return unless @broadcast.scheduled_at.present?
+
+      if @broadcast.scheduled_at <= Time.current
+        raise ArgumentError, "Scheduled time must be in the future"
+      end
+      job = Emails::SendBroadcastJob.set(wait_until: @broadcast.scheduled_at).perform_later(@broadcast.id)
+      @broadcast.update!(sidekiq_id: job.job_id) if job.job_id
+
+    end
+  end
+end
