diff --git a/app/graphql/types/inputs/settings_input_type.rb b/app/graphql/types/inputs/settings_input_type.rb
index b09fae25..b0f384d4 100644
--- a/app/graphql/types/inputs/settings_input_type.rb
+++ b/app/graphql/types/inputs/settings_input_type.rb
@@ -72,7 +72,8 @@ module Types
     # mailchimps fields
     argument :mailchimp_api_key, String, required: false
     argument :mailchimp_list_id, String, required: false
-
+    argument :mandrill_enabled, Bo<PERSON>an, required: false
+    argument :mandrill_api_key, String, required: false
     argument :gamification_enabled, Bo<PERSON>an, required: false # Gamification enabled
     argument :apply_to_past_data, <PERSON><PERSON><PERSON>, required: false # Gamification apply to Old users
   end
diff --git a/app/graphql/types/objects/global_settings_type.rb b/app/graphql/types/objects/global_settings_type.rb
index 020b5a60..60fa4101 100644
--- a/app/graphql/types/objects/global_settings_type.rb
+++ b/app/graphql/types/objects/global_settings_type.rb
@@ -99,7 +99,8 @@ module Types::Objects
     # mailchimps fields
     field :mailchimp_api_key, String, null: true
     field :mailchimp_list_id, String, null: true
-
+    field :mandrill_enabled, Boolean, null: true
+    field :mandrill_api_key, String, null: true
     field :past_due_invoice_url, String, null: true
     field :is_customer, Boolean, null: true
     field :gamification_enabled, Boolean, null: true
diff --git a/app/jobs/emails/send_broadcast_job.rb b/app/jobs/emails/send_broadcast_job.rb
new file mode 100644
index 00000000..c1c638f9
--- /dev/null
+++ b/app/jobs/emails/send_broadcast_job.rb
@@ -0,0 +1,221 @@
+class Emails::SendBroadcastJob < ApplicationJob
+  include UserFilterable
+  include HTTParty
+
+  queue_as :default
+
+  base_uri 'https://mandrillapp.com/api/1.0'
+
+  def perform(broadcast_id, receive_copy: false)
+    @broadcast = Emails::Broadcast.find(broadcast_id)
+    @tenant = @broadcast.tenant
+    @receive_copy = receive_copy
+    send_broadcast_emails
+  rescue StandardError => e
+    @broadcast&.update(status: 'failed')
+    raise e
+  end
+
+  private
+
+  def send_broadcast_emails
+    @broadcast.update!(status: 'sending')
+
+    recipients = get_recipients
+    if recipients.empty?
+      @broadcast.update!(status: 'incomplete')
+      Rails.logger.info "No recipients found for this broadcast"
+      return
+    end
+    send_emails_to_recipients(recipients)
+
+    @broadcast.update!(status: 'delivered')
+
+    Rails.logger.info "Broadcast #{@broadcast.id} proccessed for #{recipients.count} recipients"
+  end
+  
+  def get_recipients
+    recipients = case @broadcast.sent_to
+    when 'all_members'
+      get_all_students
+    when 'choose_saved_list'
+      get_filtered_students
+    else
+      []
+    end
+    if @broadcast.send_copy_to_sender || @receive_copy
+      recipients << @tenant.super_admin
+    end
+    recipients.uniq
+  end
+
+  def get_all_students
+    @tenant.students.where.not(email: [nil, ''])
+  end
+
+  def get_filtered_students
+    return [] unless @broadcast.filter.present?
+
+    students = @tenant.students.where.not(email: [nil, ''])
+    filter_data = @broadcast.filter.filters
+    filters = convert_to_filter_struct(filter_data)
+
+    filter_users = studentsapply_filters(students, filters).uniq
+    filter_users
+  end
+
+  def convert_to_filter_struct(filter_data)
+    OpenStruct.new(
+      date_range: filter_data['date_range'],
+      custom_date_range: filter_data['custom_date_range'],
+      filter_groups: filter_data['filter_groups'] || []
+    )
+  end
+
+  def send_emails_to_recipients(recipients)
+    delivered_count = 0
+    bounced_count = 0
+    recipients.each do |recipient|
+      create_activity_record(recipient, 'pending')
+        result = send_individual_email(recipient)
+        if result == 'sent'
+          Rails.logger.info "Broadcast #{@broadcast.title} delivered for #{recipient.full_name} recipients"
+          delivered_count += 1
+        else
+          Rails.logger.info "Broadcast #{@broadcast.title} bounced for #{recipient.full_name} recipients"
+
+          bounced_count += 1
+        end
+      sleep(0.1)
+    end
+
+    @broadcast.update!(delivered: delivered_count, bounced: bounced_count)
+  end
+  
+  def send_individual_email(recipient)
+    @inline_attachments = []
+
+    subject = personalize_content(@broadcast.subject, recipient)
+    personalized_body = personalize_content(@broadcast.body, recipient)
+
+    processed_body = process_inline_images(personalized_body)
+
+    final_body = "<div style='text-align: center; margin: 0 auto; max-width: 500px; padding: 0 20px;'>#{processed_body}</div>"
+    signature = @broadcast.signature_status ? @broadcast.signature : @tenant.default_signature
+    mandrill_api_key = get_mandrill_api_key 
+    message_data = {
+      key: mandrill_api_key,
+      message: {
+        subject: subject,
+        from_email: signature,
+        from_name: @tenant.super_admin.full_name,
+        to: [
+          {
+            email: recipient.email,
+            name: recipient.full_name,
+            type: 'to'
+          }
+        ],
+        html: final_body,
+        auto_text: true,
+        merge: true,
+        merge_language: 'handlebars',
+        tags: [@broadcast.id.to_s, 'broadcast', @tenant.subdomain]
+      }
+    }
+
+    if @inline_attachments&.any?
+      message_data[:message][:images] = @inline_attachments
+    end
+
+    response = self.class.post('/messages/send.json', {
+      body: message_data.to_json,
+      headers: { 'Content-Type' => 'application/json' }
+    })
+
+    if response.first['status'] == 'sent'
+      message_id = response.first['_id']
+      update_activity_with_message_id(recipient,'success', message_id)
+    else
+      message_id = response&.dig(0, '_id') || raise("Mandrill response missing _id: #{response.inspect}")
+      update_activity_with_message_id(recipient,'failed', message_id)
+    end
+
+    response.first['status']
+  end
+  
+  
+  def create_activity_record(recipient, status, error_message = nil)
+    @tenant.email_activities.create!(
+      broadcast: @broadcast,
+      user: recipient,
+      email_address: recipient.email,
+      status: status,
+      error_message: error_message,
+      occurred_at: Time.current
+    )
+  end
+  
+  def update_activity_with_message_id(recipient, status, message_id)
+    activity = @tenant.email_activities.find_by(
+      broadcast: @broadcast,
+      user: recipient,
+    )
+
+    activity&.update(status:status, message_id: message_id)
+  end
+
+  def get_mandrill_api_key
+    superadmin = @tenant.super_admin
+
+    if @tenant.mandrill_enabled &&@tenant.mandrill_api_key.present?
+      @tenant.mandrill_api_key
+      'md-**********************'
+    else
+      raise StandardError, "No Mandrill API key found for tenant #{@tenant.id}."
+    end
+  end
+
+  def personalize_content(content, recipient)
+    new_content = content.sub('@school_name', @tenant.school_name || '')
+                        .sub('@user_name', recipient.full_name || '')
+                        .sub('@user_email', recipient.email || '')
+                        .sub('@user_firstname', recipient.first_name || '')
+                        .sub('@user_lastname', recipient.last_name || '')
+                        .sub('@school_url', "https://#{@tenant.subdomain}.sabionet.com")
+                        .sub('@academy_url', @tenant.academy_url || "https://#{@tenant.subdomain}.sabionet.com")
+                        .sub('@subdomain', @tenant.subdomain || '')
+                        .sub('@student_name', recipient.full_name || '')
+                        .sub('@admin_name', @tenant.super_admin&.full_name || '')
+
+    new_content = new_content.sub('@current_date', Date.current.strftime("%B %d, %Y"))
+                            .sub('@current_year', Date.current.year.to_s)
+                            .sub('@broadcast_title', @broadcast.title || '')
+
+    new_content
+  end
+
+  def process_inline_images(body)
+    return body unless body.present?
+    binding.pry
+    base64_image_tags = body.scan(/<img[^>]+src=["']data:image\/(png|jpeg|gif);base64,([^"']+)["'][^>]*>/)
+
+    base64_image_tags.each_with_index do |(format, base64_data), index|
+      decoded_image = Base64.decode64(base64_data)
+
+      cid = "image#{index}"
+
+      @inline_attachments ||= []
+      @inline_attachments << {
+        type: "image/#{format}",
+        name: "#{cid}.#{format}",
+        content: Base64.encode64(decoded_image).strip,
+        content_id: cid
+      }
+
+      body.gsub!(%r{<img\s+[^>]*src=["']data:image/#{format};base64,#{Regexp.escape(base64_data)}["'][^>]*>}i, %Q(<img src="cid:#{cid}"))
+    end
+
+    body
+  end
+end
\ No newline at end of file
diff --git a/app/models/emails/broadcast.rb b/app/models/emails/broadcast.rb
index 332d602f..8307855f 100644
--- a/app/models/emails/broadcast.rb
+++ b/app/models/emails/broadcast.rb
@@ -5,10 +5,16 @@ class Emails::Broadcast < ApplicationRecord
   belongs_to :filter, class_name: 'UserFilter', optional: true
   has_many :activities, class_name: 'Emails::Activity', dependent: :destroy
 
+  after_commit :schedule_broadcast_delivery, on: :create
+  after_commit :reschedule_the_job,  if: :saved_change_to_scheduled_at?
+  before_destroy :cancel_scheduled_job
+
   enum status: {
     draft: 'draft',
     ready: 'ready',
-    sent: 'sent',
+    sending: 'sending',
+    incomplete:'incomplete',
+    delivered: 'delivered',
     failed: 'failed'
   }
 
@@ -31,4 +37,25 @@ class Emails::Broadcast < ApplicationRecord
   validates :filter, presence: true, if: :list_with_filters?
 
   scope :by_status, ->(status) { where(status: status) }
+
+  private
+
+  def schedule_broadcast_delivery
+    return unless status != 'ready'
+
+    scheduler = Emails::BroadcastSchedulerService.new(self)
+    scheduler.schedule_broadcast
+  end
+
+  def reschedule_the_job
+    cancel_scheduled_job
+    schedule_broadcast_delivery
+  end
+
+  def cancel_scheduled_job
+    return unless sidekiq_id.present?
+
+    Sidekiq::ScheduledSet.new.find_job(sidekiq_id)&.delete
+  end
+
 end
diff --git a/app/services/emails/broadcast_scheduler_service.rb b/app/services/emails/broadcast_scheduler_service.rb
new file mode 100644
index 00000000..ca38c297
--- /dev/null
+++ b/app/services/emails/broadcast_scheduler_service.rb
@@ -0,0 +1,36 @@
+module Emails
+  class BroadcastSchedulerService
+    def initialize(broadcast, receive_copy: false)
+      @broadcast = broadcast
+      @receive_copy = receive_copy
+    end
+
+    def schedule_broadcast
+      case @broadcast.scheduled_type
+      when 'send_now'
+        send_immediately
+      when 'send_later'
+        schedule_for_later
+      else
+        raise ArgumentError, "Invalid scheduled_type: #{@broadcast.scheduled_type}"
+      end
+    end
+
+    private
+
+    def send_immediately
+      Emails::SendBroadcastJob.perform_later(@broadcast.id, receive_copy: @receive_copy)
+    end
+
+    def schedule_for_later
+      return unless @broadcast.scheduled_at.present?
+
+      if @broadcast.scheduled_at <= Time.current
+        raise ArgumentError, "Scheduled time must be in the future"
+      end
+      job = Emails::SendBroadcastJob.set(wait_until: @broadcast.scheduled_at).perform_later(@broadcast.id, receive_copy: @receive_copy)
+      @broadcast.update!(sidekiq_id: job.job_id) if job.job_id
+
+    end
+  end
+end
diff --git a/db/migrate/20250731180056_create_email_broadcast_schema.rb b/db/migrate/20250731180056_create_email_broadcast_schema.rb
index a20f818a..bf62b35c 100644
--- a/db/migrate/20250731180056_create_email_broadcast_schema.rb
+++ b/db/migrate/20250731180056_create_email_broadcast_schema.rb
@@ -32,6 +32,8 @@ class CreateEmailBroadcastSchema < ActiveRecord::Migration[7.0]
       t.string :scheduled_type, null: false, default: 'send_now'
       t.datetime :scheduled_at, null: true
       t.string :sent_to, null: false, default: 'all_members'
+      t.boolean :send_copy_to_sender, null: false, default: false
+      t.boolean :signature_status, null: false, default: false
       t.timestamps
     end
 
@@ -53,5 +55,7 @@ class CreateEmailBroadcastSchema < ActiveRecord::Migration[7.0]
     end
 
     add_index :emails_activities, :occurred_at
+    add_column :tenants, :mandrill_enabled, :boolean
+    add_column :tenants, :mandrill_api_key, :string
   end
 end
