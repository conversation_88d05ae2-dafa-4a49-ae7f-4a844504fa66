diff --git a/app/graphql/mutations/email_mutations.rb b/app/graphql/mutations/email_mutations.rb
index b2476f5e..e97417b5 100644
--- a/app/graphql/mutations/email_mutations.rb
+++ b/app/graphql/mutations/email_mutations.rb
@@ -16,5 +16,15 @@ module Mutations
       field :update_signature, mutation: Mutations::Emails::UpdateSignature
       field :delete_signature, mutation: Mutations::Emails::DeleteSignature
       field :resend_email_signature, mutation: Mutations::Emails::ResendEmailSignature
+
+      # Saved Lists mutations
+      field :create_saved_list, mutation: Mutations::Emails::CreateSavedList
+      field :update_saved_list, mutation: Mutations::Emails::UpdateSavedList
+      field :delete_saved_list, mutation: Mutations::Emails::DeleteSavedList
+
+      # Broadcast mutations
+      field :create_broadcast, mutation: Mutations::Emails::CreateBroadcast
+      field :update_broadcast, mutation: Mutations::Emails::UpdateBroadcast
+      field :delete_broadcast, mutation: Mutations::Emails::DeleteBroadcast
     end
 end
diff --git a/app/graphql/mutations/emails/create_broadcast.rb b/app/graphql/mutations/emails/create_broadcast.rb
new file mode 100644
index 00000000..d1e6ba27
--- /dev/null
+++ b/app/graphql/mutations/emails/create_broadcast.rb
@@ -0,0 +1,32 @@
+module Mutations
+  class Emails::CreateBroadcast < BaseMutation
+    field :broadcast, Types::Objects::Emails::BroadcastType, null: false
+    field :message, String, null: true
+
+    argument :broadcast_input, Types::Inputs::Emails::BroadcastInputType, required: true
+
+    def resolve(broadcast_input:)
+  
+      if broadcast_input.to == 'choose_saved_list'
+        unless broadcast_input.saved_list_id.present?
+          raise Sabiorealm::Error, "Saved list is required when 'to' is 'choose_saved_list'"
+        end
+        
+        saved_list = context[:current_tenant].emails_saved_lists.find_by(id: broadcast_input.saved_list_id)
+        unless saved_list
+          raise Sabiorealm::Error, "Saved list not found"
+        end
+      end
+
+      broadcast_email = ::Emails::Broadcast.new(**broadcast_input)
+      if broadcast_email.save
+        {
+          broadcast: broadcast_email,
+          message: 'Broadcast created successfully'
+        }
+      else
+        raise Sabiorealm::Error, broadcast_email.errors.full_messages.join(', ')
+      end
+    end
+  end
+end
diff --git a/app/graphql/mutations/emails/create_saved_list.rb b/app/graphql/mutations/emails/create_saved_list.rb
new file mode 100644
index 00000000..2982f3a4
--- /dev/null
+++ b/app/graphql/mutations/emails/create_saved_list.rb
@@ -0,0 +1,20 @@
+module Mutations
+  class Emails::CreateSavedList < BaseMutation
+    field :saved_list, Types::Objects::Emails::SavedListType, null: false
+    field :message, String, null: true
+
+    argument :saved_list_input, Types::Inputs::Emails::SavedListInputType, required: true
+
+    def resolve(saved_list_input:)  
+      saved_list = ::Email::SavedList.new(saved_list_input)
+      if saved_list.save
+        {
+          saved_list: saved_list,
+          message: 'Filter list saved successfully'
+        }
+      else
+        raise Sabiorealm::Error, saved_list.errors.full_messages.join(', ')
+      end
+    end
+  end
+end
diff --git a/app/graphql/mutations/emails/delete_broadcast.rb b/app/graphql/mutations/emails/delete_broadcast.rb
new file mode 100644
index 00000000..9d81b7b5
--- /dev/null
+++ b/app/graphql/mutations/emails/delete_broadcast.rb
@@ -0,0 +1,21 @@
+module Mutations
+  class Emails::DeleteBroadcast < BaseMutation
+    field :success, Boolean, null: false
+    field :message, String, null: true
+
+    argument :id, ID, required: true
+
+    def resolve(id:)
+      broadcast = context[:current_tenant].emails_broadcasts.find(id)
+      
+      if broadcast.destroy
+        {
+          success: true,
+          message: 'Broadcast deleted successfully'
+        }
+      else
+        raise Sabiorealm::Error, 'Failed to delete broadcast'
+      end
+    end
+  end
+end
diff --git a/app/graphql/mutations/emails/delete_saved_list.rb b/app/graphql/mutations/emails/delete_saved_list.rb
new file mode 100644
index 00000000..c4122a1d
--- /dev/null
+++ b/app/graphql/mutations/emails/delete_saved_list.rb
@@ -0,0 +1,26 @@
+module Mutations
+  class Emails::DeleteSavedList < BaseMutation
+    field :success, Boolean, null: false
+    field :message, String, null: true
+
+    argument :id, ID, required: true
+
+    def resolve(id:)
+      saved_list = context[:current_tenant].emails_saved_lists.find(id)
+      
+      # Check if the saved list is being used by any broadcasts
+      if saved_list.broadcasts.exists?
+        raise Sabiorealm::Error, 'Cannot delete filter list that is being used by broadcasts'
+      end
+
+      if saved_list.destroy
+        {
+          success: true,
+          message: 'Filter list deleted successfully'
+        }
+      else
+        raise Sabiorealm::Error, 'Failed to delete filter list'
+      end
+    end
+  end
+end
diff --git a/app/graphql/mutations/emails/update_broadcast.rb b/app/graphql/mutations/emails/update_broadcast.rb
new file mode 100644
index 00000000..25d5898d
--- /dev/null
+++ b/app/graphql/mutations/emails/update_broadcast.rb
@@ -0,0 +1,33 @@
+module Mutations
+  class Emails::UpdateBroadcast < BaseMutation
+    field :broadcast, Types::Objects::Emails::BroadcastType, null: false
+    field :message, String, null: true
+
+    argument :id, ID, required: true
+    argument :broadcast_input, Types::Inputs::Emails::BroadcastInputType, required: true
+
+    def resolve(id:, broadcast_input:)
+      broadcast = context[:current_tenant].emails_broadcasts.find(id)
+
+      if broadcast_input.to == 'choose_saved_list'
+        unless broadcast_input.saved_list_id.present?
+          raise Sabiorealm::Error, "Saved list is required when 'to' is 'choose_saved_list'"
+        end
+        
+        saved_list = context[:current_tenant].emails_saved_lists.find_by(id: broadcast_input.saved_list_id)
+        unless saved_list
+          raise Sabiorealm::Error, "Saved list not found"
+        end
+      end
+
+      if broadcast.update(update_params)
+        {
+          broadcast: broadcast,
+          message: 'Broadcast updated successfully'
+        }
+      else
+        raise Sabiorealm::Error, broadcast.errors.full_messages.join(', ')
+      end
+    end
+  end
+end
diff --git a/app/graphql/mutations/emails/update_saved_list.rb b/app/graphql/mutations/emails/update_saved_list.rb
new file mode 100644
index 00000000..fb78450f
--- /dev/null
+++ b/app/graphql/mutations/emails/update_saved_list.rb
@@ -0,0 +1,22 @@
+module Mutations
+  class Emails::UpdateSavedList < BaseMutation
+    field :saved_list, Types::Objects::Emails::SavedListType, null: false
+    field :message, String, null: true
+
+    argument :id, ID, required: true
+    argument :saved_list_input, Types::Inputs::Emails::SavedListInputType, required: true
+
+    def resolve(id:, saved_list_input:)
+      saved_list = context[:current_tenant].emails_saved_lists.find(id)
+
+      if saved_list.update(update_params)
+        {
+          saved_list: saved_list,
+          message: 'Filter list updated successfully'
+        }
+      else
+        raise Sabiorealm::Error, saved_list.errors.full_messages.join(', ')
+      end
+    end
+  end
+end
diff --git a/app/graphql/queries/admin/email_query_type.rb b/app/graphql/queries/admin/email_query_type.rb
index 0df25033..7dd7b7c9 100644
--- a/app/graphql/queries/admin/email_query_type.rb
+++ b/app/graphql/queries/admin/email_query_type.rb
@@ -2,6 +2,8 @@ module Queries
     module Admin
       module EmailQueryType
         include Types::BaseInterface
+        include Queries::Emails::SavedListQueryType
+        include Queries::Emails::BroadcastQueryType
 
         field :signature_list, Types::Objects::Emails::SignatureType.connection_type, null: false
         def signature_list
diff --git a/app/graphql/queries/emails/broadcast_query_type.rb b/app/graphql/queries/emails/broadcast_query_type.rb
new file mode 100644
index 00000000..8b3bfb91
--- /dev/null
+++ b/app/graphql/queries/emails/broadcast_query_type.rb
@@ -0,0 +1,45 @@
+module Queries
+  module Emails
+    module BroadcastQueryType
+      include Types::BaseInterface
+
+      # Get all broadcasts
+      field :broadcasts, Types::Objects::Emails::BroadcastType.connection_type, null: false do
+        argument :status, String, required: false
+        argument :to, String, required: false
+      end
+
+      def broadcasts(search: nil, status: nil, to: nil)
+        scope = context[:current_tenant].emails_broadcasts.includes( :signature, :saved_list)
+        
+        scope = scope.where(status: status) if status.present?
+        
+        scope = scope.where(to: to) if to.present?
+        
+        
+        scope
+      end
+
+      # Get a specific broadcast
+      field :broadcast, Types::Objects::Emails::BroadcastType, null: false do
+        argument :id, ID, required: true
+      end
+
+      def broadcast(id:)
+        context[:current_tenant].emails_broadcasts.includes(:created_by, :signature, :template, :saved_list).find(id)
+      end
+
+      # Preview recipients for a broadcast
+      # field :broadcast_recipients_preview, Types::Objects::UserType.connection_type, null: false do
+      #   argument :id, ID, required: true
+      #   argument :limit, Integer, required: false
+      # end
+
+      # def broadcast_recipients_preview(id:, limit: 10)
+      #   broadcast = context[:current_tenant].emails_broadcasts.find(id)
+      #   broadcast.recipients_preview(limit)
+      # end
+
+    end
+  end
+end
diff --git a/app/graphql/queries/emails/saved_list_query_type.rb b/app/graphql/queries/emails/saved_list_query_type.rb
new file mode 100644
index 00000000..a68f11b0
--- /dev/null
+++ b/app/graphql/queries/emails/saved_list_query_type.rb
@@ -0,0 +1,89 @@
+module Queries
+  module Emails
+    module SavedListQueryType
+      include Types::BaseInterface
+
+      # Get all saved lists
+      field :saved_lists, Types::Objects::Emails::SavedListType.connection_type, null: false do
+        argument :search, String, required: false
+        argument :created_by_id, ID, required: false
+        argument :list_type, String, required: false
+        argument :order_by, String, required: false
+      end
+
+      def saved_lists(search: nil, created_by_id: nil, list_type: nil, order_by: 'created_at_desc')
+        scope = context[:current_tenant].emails_saved_lists.includes(:created_by)
+
+        # Apply list type filter
+        scope = scope.where(list_type: list_type) if list_type.present?
+        
+        # Apply search filter
+        scope = scope.where('name ILIKE ?', "%#{search}%") if search.present?
+        
+        # Apply creator filter
+        if created_by_id.present?
+          creator = User.find(created_by_id)
+          scope = scope.by_creator(creator)
+        end
+        
+        # Apply ordering
+        case order_by
+        when 'name_asc'
+          scope = scope.order(:name)
+        when 'name_desc'
+          scope = scope.order(name: :desc)
+        when 'user_count_asc'
+          scope = scope.order(:user_count)
+        when 'user_count_desc'
+          scope = scope.order(user_count: :desc)
+        when 'last_used_desc'
+          scope = scope.order(last_used_at: :desc)
+        when 'created_at_asc'
+          scope = scope.order(:created_at)
+        else # 'created_at_desc'
+          scope = scope.order(created_at: :desc)
+        end
+        
+        scope
+      end
+
+      # Get a specific saved list
+      field :saved_list, Types::Objects::Emails::SavedListType, null: false do
+        argument :id, ID, required: true
+      end
+
+      def saved_list(id:)
+        context[:current_tenant].emails_saved_lists.find(id)
+      end
+
+      # Preview users for a saved list
+      # field :saved_list_users_preview, Types::Objects::UserType.connection_type, null: false do
+      #   argument :id, ID, required: true
+      #   argument :limit, Integer, required: false
+      # end
+
+      # def saved_list_users_preview(id:, limit: 10)
+      #   saved_list = context[:current_tenant].emails_saved_lists.find(id)
+      #   saved_list.users.includes(:tags, :enrollments, :group_memberships).limit(limit)
+      # end
+
+      # Get filter options for building filters
+
+      # Get recently used saved lists
+      field :recently_used_saved_lists, [Types::Objects::Emails::SavedListType], null: false do
+        argument :limit, Integer, required: false
+        argument :list_type, String, required: false
+      end
+
+      def recently_used_saved_lists(limit: 5, list_type: 'email_filter')
+        scope = context[:current_tenant].emails_saved_lists
+                                        .where.not(last_used_at: nil)
+                                        .order(last_used_at: :desc)
+                                        .limit(limit)
+
+        scope = scope.where(list_type: list_type) if list_type.present?
+        scope
+      end
+    end
+  end
+end
diff --git a/app/graphql/queries/user_query_type.rb b/app/graphql/queries/user_query_type.rb
index 482b49a1..0b165502 100644
--- a/app/graphql/queries/user_query_type.rb
+++ b/app/graphql/queries/user_query_type.rb
@@ -42,6 +42,7 @@ module Queries
       when Student
         raise GraphQL::ExecutionError, "This user type can't access this query"
       end
+      binding.pry
       users = apply_filters(users, filters) if filters.present?
       users = User.where(id: users.map(&:id)).where.not(type: 'Affiliate')
       users.includes(:tenant, :courses, :groups, :tags)
diff --git a/app/graphql/types/inputs/emails/broadcast_input_type.rb b/app/graphql/types/inputs/emails/broadcast_input_type.rb
new file mode 100644
index 00000000..b66b0e11
--- /dev/null
+++ b/app/graphql/types/inputs/emails/broadcast_input_type.rb
@@ -0,0 +1,14 @@
+module Types
+  class Inputs::Emails::BroadcastInputType < Types::BaseInputObject
+    argument :title, String, required: true
+    argument :subject, String, required: true
+    argument :content, String, required: true
+    argument :status, String, required: false
+    argument :to, String, required: true
+    argument :filters, GraphQL::Types::JSON, required: false
+    argument :saved_list_id, ID, required: false
+    argument :signature_id, ID, required: false
+    argument :scheduled_at, GraphQL::Types::ISO8601DateTime, required: false
+    argument :metadata, GraphQL::Types::JSON, required: false
+  end
+end
diff --git a/app/graphql/types/inputs/emails/saved_list_input_type.rb b/app/graphql/types/inputs/emails/saved_list_input_type.rb
new file mode 100644
index 00000000..40b8ad6f
--- /dev/null
+++ b/app/graphql/types/inputs/emails/saved_list_input_type.rb
@@ -0,0 +1,9 @@
+module Types
+  class Inputs::Emails::SavedListInputType < Types::BaseInputObject
+    argument :name, String, required: true
+    argument :description, String, required: false
+    argument :list_type, String, required: true
+    argument :filters, GraphQL::Types::JSON, required: true
+    argument :metadata, GraphQL::Types::JSON, required: false
+  end
+end
diff --git a/app/graphql/types/objects/emails/broadcast_type.rb b/app/graphql/types/objects/emails/broadcast_type.rb
new file mode 100644
index 00000000..10632550
--- /dev/null
+++ b/app/graphql/types/objects/emails/broadcast_type.rb
@@ -0,0 +1,21 @@
+# frozen_string_literal: true
+module Types
+  class Objects::Emails::BroadcastType < Types::BaseObject
+    field :id, ID, null: false
+    field :title, String, null: false
+    field :subject, String, null: false
+    field :content, String, null: false
+    field :status, String, null: false
+    field :to, String, null: false
+    field :filters, GraphQL::Types::JSON, null: false
+    field :scheduled_at, GraphQL::Types::ISO8601DateTime, null: true
+    field :sent_at, GraphQL::Types::ISO8601DateTime, null: true
+    field :metadata, GraphQL::Types::JSON, null: false
+    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
+    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
+    
+    # Associations
+    field :signature, Types::Objects::Emails::SignatureType, null: true
+    field :saved_list, Types::Objects::Emails::SavedListType, null: true
+  end
+end
diff --git a/app/graphql/types/objects/emails/saved_list_type.rb b/app/graphql/types/objects/emails/saved_list_type.rb
new file mode 100644
index 00000000..4fc79ba1
--- /dev/null
+++ b/app/graphql/types/objects/emails/saved_list_type.rb
@@ -0,0 +1,13 @@
+# frozen_string_literal: true
+module Types
+  class Objects::Emails::SavedListType < Types::BaseObject
+    field :id, ID, null: false
+    field :name, String, null: false
+    field :description, String, null: true
+    field :list_type, String, null: false
+    field :filters, GraphQL::Types::JSON, null: false
+    field :metadata, GraphQL::Types::JSON, null: false
+    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
+    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
+  end
+end
diff --git a/app/models/emails/broadcast.rb b/app/models/emails/broadcast.rb
new file mode 100644
index 00000000..cc3dc2da
--- /dev/null
+++ b/app/models/emails/broadcast.rb
@@ -0,0 +1,32 @@
+class Emails::Broadcast < ApplicationRecord
+  belongs_to_tenant :tenant
+  belongs_to :signature, class_name: 'Emails::Signature', optional: true
+  belongs_to :saved_list, class_name: 'Emails::SavedList', optional: true
+  
+  has_many :activities, class_name: 'Emails::Activity', dependent: :destroy
+  has_many :broadcast_logs, class_name: 'Emails::BroadcastLog', dependent: :destroy
+  
+  enum status: {
+    draft: 'draft',
+    ready: 'ready',
+    sent: 'sent',
+    failed: 'failed'
+  }
+  
+  enum to: {
+    all_members: 'all_members',
+    create_list_with_filters: 'create_list_with_filters',
+    choose_saved_list: 'choose_saved_list'
+  }
+  
+  validates :title, presence: true, uniqueness: { scope: :tenant_id }
+  validates :subject, presence: true
+  validates :content, presence: true
+  validates :status, presence: true
+  validates :to, presence: true
+  
+  validates :saved_list, presence: true, if: :choose_saved_list?
+  validates :filters, presence: true, if: :create_list_with_filters?
+  
+  scope :by_status, ->(status) { where(status: status) }
+end
diff --git a/app/models/tenant.rb b/app/models/tenant.rb
index 3a6549e0..08232019 100644
--- a/app/models/tenant.rb
+++ b/app/models/tenant.rb
@@ -67,6 +67,8 @@ class Tenant < ApplicationRecord
   has_many :affiliates_tenants, dependent: :destroy
   has_one :gamification_setting, dependent: :destroy, class_name: 'Gamification::Setting'
   has_one :tenant_metric, dependent: :destroy
+  has_many :emails_broadcasts, dependent: :destroy, class_name: 'Emails::Broadcast'
+  has_many :saved_filter_lists, dependent: :destroy, class_name: 'Emails::SavedList'
   scope :trials, -> { where(is_trial: true) }
   scope :expired_trials, -> { trials.where('created_at < ?', 180.days.ago) }
 
diff --git a/db/migrate/20250731074717_create_emails_broadcasts.rb b/db/migrate/20250731074717_create_emails_broadcasts.rb
new file mode 100644
index 00000000..716be241
--- /dev/null
+++ b/db/migrate/20250731074717_create_emails_broadcasts.rb
@@ -0,0 +1,31 @@
+class CreateEmailsBroadcasts < ActiveRecord::Migration[7.0]
+  def change
+    create_table :emails_broadcasts do |t|
+      t.references :tenant, null: false, foreign_key: { on_delete: :cascade }
+      t.references :signature, null: true, foreign_key: { to_table: :emails_signatures }
+      t.references :template, null: true, foreign_key: { to_table: :emails_templates }
+      t.bigint :saved_list_id, null: true
+
+      t.string :title, null: false
+      t.string :subject, null: false
+      t.text :content, null: false
+
+      t.string :status, null: false
+      t.string :send_time_status, null: false
+      t.datetime :scheduled_at, null: true
+      t.string :to, null: false, default: 'all_members' # 'all_members', 'create_list_with_filters', 'choose_saved_list'
+      t.jsonb :filters, null: false #can be null value 
+      t.timestamps
+    end
+
+    # Add foreign key for saved_list_id
+    add_foreign_key :emails_broadcasts, :emails_saved_lists, column: :saved_list_id
+
+    # Indexes for performance
+    # add_index :emails_broadcasts, [:tenant_id, :status]
+    # add_index :emails_broadcasts, [:tenant_id, :scheduled_at]
+    # add_index :emails_broadcasts, [:tenant_id, :to]
+    # add_index :emails_broadcasts, :saved_list_id
+    # add_index :emails_broadcasts, [:tenant_id, :title], unique: true
+  end
+end
diff --git a/db/migrate/20250731074916_create_emails_saved_lists.rb b/db/migrate/20250731074916_create_emails_saved_lists.rb
new file mode 100644
index 00000000..86439503
--- /dev/null
+++ b/db/migrate/20250731074916_create_emails_saved_lists.rb
@@ -0,0 +1,27 @@
+class CreateEmailsSavedLists < ActiveRecord::Migration[7.0]
+  def change
+    create_table :emails_saved_lists do |t|
+      t.references :tenant, null: false, foreign_key: { on_delete: :cascade }
+
+      # List information
+      t.string :name, null: false
+      t.text :description, null: true
+      t.string :list_type, null: false, default: 'email_filter' # 'email_filter' or 'user_list_filter'
+
+      # Filter configuration
+      t.jsonb :filters, null: false, default: {}
+      t.integer :user_count, null: false, default: 0 #remove it 
+
+      # Metadata
+      t.jsonb :metadata, null: false, default: {}
+
+      t.timestamps
+    end
+
+    # Indexes
+    # add_index :emails_saved_lists, [:tenant_id, :list_type, :name], unique: true
+    # add_index :emails_saved_lists, [:tenant_id, :list_type, :created_at]
+    # add_index :emails_saved_lists, [:tenant_id, :last_used_at]
+    # add_index :emails_saved_lists, :list_type
+  end
+end
