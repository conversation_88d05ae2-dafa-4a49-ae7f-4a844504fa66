diff --git a/app/graphql/mutations/email_mutations.rb b/app/graphql/mutations/email_mutations.rb
index b2476f5e..bee6ed6d 100644
--- a/app/graphql/mutations/email_mutations.rb
+++ b/app/graphql/mutations/email_mutations.rb
@@ -16,5 +16,18 @@ module Mutations
       field :update_signature, mutation: Mutations::Emails::UpdateSignature
       field :delete_signature, mutation: Mutations::Emails::DeleteSignature
       field :resend_email_signature, mutation: Mutations::Emails::ResendEmailSignature
+
+      # Filter mutations
+      field :create_filter, mutation: Mutations::Emails::CreateFilter
+      field :update_filter, mutation: Mutations::Emails::UpdateFilter
+      field :delete_filter, mutation: Mutations::Emails::DeleteFilter
+
+      # Broadcast mutations
+      field :create_broadcast, mutation: Mutations::Emails::CreateBroadcast
+      field :update_broadcast, mutation: Mutations::Emails::UpdateBroadcast
+      field :delete_broadcast, mutation: Mutations::Emails::DeleteBroadcast
+
+      # Activity mutations
+      field :create_email_activity, mutation: Mutations::Emails::CreateActivity
     end
 end
diff --git a/app/graphql/mutations/emails/create_activity.rb b/app/graphql/mutations/emails/create_activity.rb
new file mode 100644
index 00000000..1b733b7c
--- /dev/null
+++ b/app/graphql/mutations/emails/create_activity.rb
@@ -0,0 +1,18 @@
+module Mutations
+  class Emails::CreateActivity < BaseMutation
+    field :activity, Types::Objects::Emails::ActivityType, null: false
+    field :message, String, null: true
+
+    argument :activity_input, Types::Inputs::Emails::ActivityInputType, required: true
+
+    def resolve(activity_input:)
+      activity = context[:current_tenant].email_activities.new(**activity_input)
+      activity.save!
+      
+      {
+        activity: activity,
+        message: 'Email activity created successfully'
+      }
+    end
+  end
+end
diff --git a/app/graphql/mutations/emails/create_broadcast.rb b/app/graphql/mutations/emails/create_broadcast.rb
new file mode 100644
index 00000000..88447b3b
--- /dev/null
+++ b/app/graphql/mutations/emails/create_broadcast.rb
@@ -0,0 +1,32 @@
+module Mutations
+  class Emails::CreateBroadcast < BaseMutation
+    field :broadcast, Types::Objects::Emails::BroadcastType, null: false
+    field :message, String, null: true
+
+    argument :broadcast_input, Types::Inputs::Emails::BroadcastInputType, required: true
+
+    def resolve(broadcast_input:)
+  
+      if broadcast_input.to == 'choose_saved_list'
+        unless broadcast_input.filter_id.present?
+          raise Sabiorealm::Error, "Filter is required when 'to' is 'choose_saved_list'"
+        end
+
+        filter = context[:current_tenant].filters.find_by(id: broadcast_input.filter_id)
+        unless filter
+          raise Sabiorealm::Error, "Filter not found"
+        end
+      end
+
+      broadcast_email = ::Emails::Broadcast.new(**broadcast_input)
+      if broadcast_email.save
+        {
+          broadcast: broadcast_email,
+          message: 'Broadcast created successfully'
+        }
+      else
+        raise Sabiorealm::Error, broadcast_email.errors.full_messages.join(', ')
+      end
+    end
+  end
+end
diff --git a/app/graphql/mutations/emails/create_filter.rb b/app/graphql/mutations/emails/create_filter.rb
new file mode 100644
index 00000000..b7e53b50
--- /dev/null
+++ b/app/graphql/mutations/emails/create_filter.rb
@@ -0,0 +1,18 @@
+module Mutations
+  class Emails::CreateFilter < BaseMutation
+    field :filter, Types::Objects::FilterType, null: false
+    field :message, String, null: true
+
+    argument :filter_input, Types::Inputs::FilterInputType, required: true
+
+    def resolve(filter_input:)
+      filter = context[:current_tenant].filters.new(**filter_input)
+      filter.save!
+
+      {
+        filter: filter,
+        message: 'Filter created successfully'
+      }
+    end
+  end
+end
diff --git a/app/graphql/mutations/emails/delete_broadcast.rb b/app/graphql/mutations/emails/delete_broadcast.rb
new file mode 100644
index 00000000..9d81b7b5
--- /dev/null
+++ b/app/graphql/mutations/emails/delete_broadcast.rb
@@ -0,0 +1,21 @@
+module Mutations
+  class Emails::DeleteBroadcast < BaseMutation
+    field :success, Boolean, null: false
+    field :message, String, null: true
+
+    argument :id, ID, required: true
+
+    def resolve(id:)
+      broadcast = context[:current_tenant].emails_broadcasts.find(id)
+      
+      if broadcast.destroy
+        {
+          success: true,
+          message: 'Broadcast deleted successfully'
+        }
+      else
+        raise Sabiorealm::Error, 'Failed to delete broadcast'
+      end
+    end
+  end
+end
diff --git a/app/graphql/mutations/emails/delete_filter.rb b/app/graphql/mutations/emails/delete_filter.rb
new file mode 100644
index 00000000..ebf886f7
--- /dev/null
+++ b/app/graphql/mutations/emails/delete_filter.rb
@@ -0,0 +1,25 @@
+module Mutations
+  class Emails::DeleteFilter < BaseMutation
+    field :success, Boolean, null: false
+    field :message, String, null: true
+
+    argument :id, ID, required: true
+
+    def resolve(id:)
+      filter = context[:current_tenant].filters.find(id)
+
+      if filter.broadcasts.exists?
+        raise Sabiorealm::Error, 'Cannot delete filter that is being used by broadcasts'
+      end
+
+      if filter.destroy
+        {
+          success: true,
+          message: 'Filter deleted successfully'
+        }
+      else
+        raise Sabiorealm::Error, 'Failed to delete filter'
+      end
+    end
+  end
+end
diff --git a/app/graphql/mutations/emails/update_broadcast.rb b/app/graphql/mutations/emails/update_broadcast.rb
new file mode 100644
index 00000000..25d5898d
--- /dev/null
+++ b/app/graphql/mutations/emails/update_broadcast.rb
@@ -0,0 +1,33 @@
+module Mutations
+  class Emails::UpdateBroadcast < BaseMutation
+    field :broadcast, Types::Objects::Emails::BroadcastType, null: false
+    field :message, String, null: true
+
+    argument :id, ID, required: true
+    argument :broadcast_input, Types::Inputs::Emails::BroadcastInputType, required: true
+
+    def resolve(id:, broadcast_input:)
+      broadcast = context[:current_tenant].emails_broadcasts.find(id)
+
+      if broadcast_input.to == 'choose_saved_list'
+        unless broadcast_input.saved_list_id.present?
+          raise Sabiorealm::Error, "Saved list is required when 'to' is 'choose_saved_list'"
+        end
+        
+        saved_list = context[:current_tenant].emails_saved_lists.find_by(id: broadcast_input.saved_list_id)
+        unless saved_list
+          raise Sabiorealm::Error, "Saved list not found"
+        end
+      end
+
+      if broadcast.update(update_params)
+        {
+          broadcast: broadcast,
+          message: 'Broadcast updated successfully'
+        }
+      else
+        raise Sabiorealm::Error, broadcast.errors.full_messages.join(', ')
+      end
+    end
+  end
+end
diff --git a/app/graphql/mutations/emails/update_filter.rb b/app/graphql/mutations/emails/update_filter.rb
new file mode 100644
index 00000000..7a7abf20
--- /dev/null
+++ b/app/graphql/mutations/emails/update_filter.rb
@@ -0,0 +1,19 @@
+module Mutations
+  class Emails::UpdateFilter < BaseMutation
+    field :filter, Types::Objects::FilterType, null: false
+    field :message, String, null: true
+
+    argument :id, ID, required: true
+    argument :filter_input, Types::Inputs::FilterInputType, required: true
+
+    def resolve(id:, filter_input:)
+      filter = context[:current_tenant].filters.find(id)
+      filter.update!(**filter_input)
+
+      {
+        filter: filter,
+        message: 'Filter updated successfully'
+      }
+    end
+  end
+end
diff --git a/app/graphql/queries/admin/email_query_type.rb b/app/graphql/queries/admin/email_query_type.rb
index 0df25033..10ef722e 100644
--- a/app/graphql/queries/admin/email_query_type.rb
+++ b/app/graphql/queries/admin/email_query_type.rb
@@ -2,6 +2,8 @@ module Queries
     module Admin
       module EmailQueryType
         include Types::BaseInterface
+        include Queries::Emails::BroadcastQueryType
+        include Queries::Emails::ActivityQueryType
 
         field :signature_list, Types::Objects::Emails::SignatureType.connection_type, null: false
         def signature_list
diff --git a/app/graphql/queries/emails/activity_query_type.rb b/app/graphql/queries/emails/activity_query_type.rb
new file mode 100644
index 00000000..75479ff2
--- /dev/null
+++ b/app/graphql/queries/emails/activity_query_type.rb
@@ -0,0 +1,62 @@
+module Queries
+  module Emails
+    module ActivityQueryType
+      include Types::BaseInterface
+
+      # Get all email activities
+      field :email_activities, [Types::Objects::Emails::ActivityType], null: false do
+        description "Get all email activities for the current tenant"
+        argument :broadcast_id, ID, required: false
+        argument :activity_type, String, required: false
+        argument :status, String, required: false
+        argument :email_address, String, required: false
+        argument :limit, Integer, required: false
+      end
+
+      # Get specific email activity by ID
+      field :email_activity, Types::Objects::Emails::ActivityType, null: false do
+        description "Get a specific email activity by ID"
+        argument :id, ID, required: true
+      end
+
+      # Get email activity statistics
+      field :email_activity_stats, Types::Objects::Emails::ActivityStatsType, null: false do
+        description "Get email activity statistics"
+        argument :broadcast_id, ID, required: false
+        argument :date_from, GraphQL::Types::ISO8601DateTime, required: false
+        argument :date_to, GraphQL::Types::ISO8601DateTime, required: false
+      end
+
+      def email_activities(broadcast_id: nil, activity_type: nil, status: nil, email_address: nil, limit: 100)
+        activities = context[:current_tenant].email_activities.recent
+        activities = activities.by_broadcast(broadcast_id) if broadcast_id.present?
+        activities = activities.by_activity_type(activity_type) if activity_type.present?
+        activities = activities.by_status(status) if status.present?
+        activities = activities.by_email(email_address) if email_address.present?
+        activities = activities.limit(limit) if limit.present?
+        activities
+      end
+
+      def email_activity(id:)
+        context[:current_tenant].email_activities.find(id)
+      end
+
+      def email_activity_stats(broadcast_id: nil, date_from: nil, date_to: nil)
+        activities = context[:current_tenant].email_activities
+        activities = activities.by_broadcast(broadcast_id) if broadcast_id.present?
+        activities = activities.where(occurred_at: date_from..date_to) if date_from.present? && date_to.present?
+        
+        stats = Emails::Activity.activity_stats(broadcast_id)
+        engagement = Emails::Activity.engagement_rate(broadcast_id)
+        
+        delivered_count = stats[:delivered]
+        
+        stats.merge(engagement).merge({
+          bounce_rate: delivered_count.zero? ? 0 : (stats[:bounced].to_f / delivered_count * 100).round(2),
+          complaint_rate: delivered_count.zero? ? 0 : (stats[:complained].to_f / delivered_count * 100).round(2),
+          unsubscribe_rate: delivered_count.zero? ? 0 : (stats[:unsubscribed].to_f / delivered_count * 100).round(2)
+        })
+      end
+    end
+  end
+end
diff --git a/app/graphql/queries/emails/broadcast_query_type.rb b/app/graphql/queries/emails/broadcast_query_type.rb
new file mode 100644
index 00000000..9e3931bc
--- /dev/null
+++ b/app/graphql/queries/emails/broadcast_query_type.rb
@@ -0,0 +1,43 @@
+module Queries
+  module Emails
+    module BroadcastQueryType
+      include Types::BaseInterface
+
+      field :broadcasts, Types::Objects::Emails::BroadcastType.connection_type, null: false do
+        argument :status, String, required: false
+        argument :sent_to, String, required: false
+      end
+
+      def broadcasts(search: nil, status: nil, to: nil)
+        scope = context[:current_tenant].emails_broadcasts.includes( :signature, :saved_list)
+        
+        scope = scope.where(status: status) if status.present?
+        
+        scope = scope.where(to: to) if to.present?
+        
+        
+        scope
+      end
+
+      field :broadcast, Types::Objects::Emails::BroadcastType, null: false do
+        argument :id, ID, required: true
+      end
+
+      def broadcast(id:)
+        context[:current_tenant].emails_broadcasts.includes(:created_by, :signature, :template, :saved_list).find(id)
+      end
+
+      # Preview recipients for a broadcast
+      # field :broadcast_recipients_preview, Types::Objects::UserType.connection_type, null: false do
+      #   argument :id, ID, required: true
+      #   argument :limit, Integer, required: false
+      # end
+
+      # def broadcast_recipients_preview(id:, limit: 10)
+      #   broadcast = context[:current_tenant].emails_broadcasts.find(id)
+      #   broadcast.recipients_preview(limit)
+      # end
+
+    end
+  end
+end
diff --git a/app/graphql/queries/filter_query_type.rb b/app/graphql/queries/filter_query_type.rb
new file mode 100644
index 00000000..7fcd5f86
--- /dev/null
+++ b/app/graphql/queries/filter_query_type.rb
@@ -0,0 +1,26 @@
+module Queries
+  module FilterQueryType
+    include Types::BaseInterface
+
+    field :filters, [Types::Objects::FilterType], null: false do
+      description "Get all filters for the current tenant"
+      argument :list_type, String, required: false
+    end
+
+    field :filter, Types::Objects::FilterType, null: false do
+      description "Get a specific filter by ID"
+      argument :id, ID, required: true
+    end
+
+
+    def filters(list_type: nil)
+      filters = context[:current_tenant].filters.recent
+      filters = filters.where(list_type: list_type) if list_type.present?
+      filters
+    end
+
+    def filter(id:)
+      context[:current_tenant].filters.find(id)
+    end
+  end
+end
diff --git a/app/graphql/types/inputs/emails/activity_input_type.rb b/app/graphql/types/inputs/emails/activity_input_type.rb
new file mode 100644
index 00000000..a26d0e18
--- /dev/null
+++ b/app/graphql/types/inputs/emails/activity_input_type.rb
@@ -0,0 +1,24 @@
+module Types
+  module Inputs
+    module Emails
+      class ActivityInputType < Types::BaseInputObject
+        argument :broadcast_id, ID, required: true
+        argument :user_id, ID, required: false
+        argument :activity_type, String, required: true
+        argument :email_address, String, required: true
+        argument :status, String, required: false
+        argument :message_id, String, required: false
+        argument :provider, String, required: false
+        argument :provider_data, GraphQL::Types::JSON, required: false
+        argument :user_agent, String, required: false
+        argument :ip_address, String, required: false
+        argument :location, String, required: false
+        argument :device_type, String, required: false
+        argument :error_message, String, required: false
+        argument :error_code, Integer, required: false
+        argument :metadata, GraphQL::Types::JSON, required: false
+        argument :occurred_at, GraphQL::Types::ISO8601DateTime, required: false
+      end
+    end
+  end
+end
diff --git a/app/graphql/types/inputs/emails/broadcast_input_type.rb b/app/graphql/types/inputs/emails/broadcast_input_type.rb
new file mode 100644
index 00000000..152852f1
--- /dev/null
+++ b/app/graphql/types/inputs/emails/broadcast_input_type.rb
@@ -0,0 +1,13 @@
+module Types
+  class Inputs::Emails::BroadcastInputType < Types::BaseInputObject
+    argument :title, String, required: true
+    argument :subject, String, required: true
+    argument :content, String, required: true
+    argument :status, String, required: false
+    argument :sent_to, String, required: true
+    argument :filter_id, ID, required: false
+    argument :signature_id, ID, required: false
+    argument :scheduled_at, GraphQL::Types::ISO8601DateTime, required: false
+    argument :metadata, GraphQL::Types::JSON, required: false
+  end
+end
diff --git a/app/graphql/types/inputs/filter_input_type.rb b/app/graphql/types/inputs/filter_input_type.rb
new file mode 100644
index 00000000..05f23145
--- /dev/null
+++ b/app/graphql/types/inputs/filter_input_type.rb
@@ -0,0 +1,11 @@
+    module Types
+      module Inputs
+        class FilterInputType < Types::BaseInputObject
+        argument :name, String, required: true
+        argument :description, String, required: false
+        argument :list_type, String, required: true
+        argument :filters, GraphQL::Types::JSON, required: true
+        argument :metadata, GraphQL::Types::JSON, required: false
+        end
+      end
+    end
diff --git a/app/graphql/types/objects/emails/activity_stats_type.rb b/app/graphql/types/objects/emails/activity_stats_type.rb
new file mode 100644
index 00000000..a2de8d52
--- /dev/null
+++ b/app/graphql/types/objects/emails/activity_stats_type.rb
@@ -0,0 +1,23 @@
+module Types
+  module Objects
+    module Emails
+      class ActivityStatsType < Types::BaseObject
+        field :total, Integer, null: false
+        field :sent, Integer, null: false
+        field :delivered, Integer, null: false
+        field :opened, Integer, null: false
+        field :clicked, Integer, null: false
+        field :bounced, Integer, null: false
+        field :complained, Integer, null: false
+        field :unsubscribed, Integer, null: false
+        field :failed, Integer, null: false
+        field :open_rate, Float, null: false
+        field :click_rate, Float, null: false
+        field :click_to_open_rate, Float, null: false
+        field :bounce_rate, Float, null: false
+        field :complaint_rate, Float, null: false
+        field :unsubscribe_rate, Float, null: false
+      end
+    end
+  end
+end
diff --git a/app/graphql/types/objects/emails/activity_type.rb b/app/graphql/types/objects/emails/activity_type.rb
new file mode 100644
index 00000000..3c7b28d1
--- /dev/null
+++ b/app/graphql/types/objects/emails/activity_type.rb
@@ -0,0 +1,46 @@
+module Types
+  module Objects
+    module Emails
+      class ActivityType < Types::BaseObject
+        field :id, ID, null: false
+        field :activity_type, String, null: false
+        field :email_address, String, null: false
+        field :status, String, null: false
+        field :message_id, String, null: true
+        field :provider, String, null: true
+        field :provider_data, GraphQL::Types::JSON, null: false
+        field :user_agent, String, null: true
+        field :ip_address, String, null: true
+        field :location, String, null: true
+        field :device_type, String, null: true
+        field :error_message, String, null: true
+        field :error_code, Integer, null: true
+        field :metadata, GraphQL::Types::JSON, null: false
+        field :occurred_at, GraphQL::Types::ISO8601DateTime, null: false
+        field :created_at, GraphQL::Types::ISO8601DateTime, null: false
+        field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
+        
+        # Associations
+        field :broadcast, Types::Objects::Emails::BroadcastType, null: false
+        field :user, Types::Interfaces::UserType, null: true
+        
+        # Computed fields
+        field :is_successful, Boolean, null: false
+        field :is_engagement_activity, Boolean, null: false
+        field :is_negative_activity, Boolean, null: false
+        
+        def is_successful
+          object.successful?
+        end
+        
+        def is_engagement_activity
+          object.engagement_activity?
+        end
+        
+        def is_negative_activity
+          object.negative_activity?
+        end
+      end
+    end
+  end
+end
diff --git a/app/graphql/types/objects/emails/broadcast_type.rb b/app/graphql/types/objects/emails/broadcast_type.rb
new file mode 100644
index 00000000..44122cbf
--- /dev/null
+++ b/app/graphql/types/objects/emails/broadcast_type.rb
@@ -0,0 +1,19 @@
+# frozen_string_literal: true
+module Types
+  class Objects::Emails::BroadcastType < Types::BaseObject
+    field :id, ID, null: false
+    field :title, String, null: false
+    field :subject, String, null: false
+    field :content, String, null: false
+    field :status, String, null: false
+    field :to, String, null: false
+    field :scheduled_at, GraphQL::Types::ISO8601DateTime, null: true
+    field :metadata, GraphQL::Types::JSON, null: false
+    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
+    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
+    
+    # Associations
+    field :signature, Types::Objects::Emails::SignatureType, null: true
+    field :filter, Types::Objects::FilterType, null: true
+  end
+end
diff --git a/app/graphql/types/objects/filter_type.rb b/app/graphql/types/objects/filter_type.rb
new file mode 100644
index 00000000..03860519
--- /dev/null
+++ b/app/graphql/types/objects/filter_type.rb
@@ -0,0 +1,15 @@
+# frozen_string_literal: true
+module Types
+  module Objects
+    class FilterType < Types::BaseObject
+    field :id, ID, null: false
+    field :name, String, null: false
+    field :description, String, null: true
+    field :list_type, String, null: false
+    field :filters, GraphQL::Types::JSON, null: false
+    field :metadata, GraphQL::Types::JSON, null: false
+    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
+    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
+    end
+  end
+end
diff --git a/app/graphql/types/query_type.rb b/app/graphql/types/query_type.rb
index 0aea09b4..9808f28f 100644
--- a/app/graphql/types/query_type.rb
+++ b/app/graphql/types/query_type.rb
@@ -27,6 +27,7 @@ module Types
     implements Queries::Admin::GamificationQueryType
 
     implements Queries::UserQueryType
+    implements Queries::FilterQueryType
 
     implements Queries::SocialQueryType
     implements Queries::MediaQueryType
diff --git a/app/models/emails/activity.rb b/app/models/emails/activity.rb
new file mode 100644
index 00000000..01e054b9
--- /dev/null
+++ b/app/models/emails/activity.rb
@@ -0,0 +1,60 @@
+class Emails::Activity < ApplicationRecord
+  self.table_name = 'email_activities'
+  
+  belongs_to_tenant :tenant
+  belongs_to :broadcast, class_name: 'Emails::Broadcast'
+  belongs_to :user, optional: true
+  
+  enum activity_type: {
+    sent: 'sent',
+    delivered: 'delivered',
+    opened: 'opened',
+    clicked: 'clicked',
+    bounced: 'bounced',
+    unsubscribed: 'unsubscribed',
+    failed: 'failed'
+  }
+  
+  enum status: {
+    pending: 'pending',
+    success: 'success',
+    failed: 'failed'
+  }
+  
+  validates :activity_type, presence: true
+  validates :email_address, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
+  validates :status, presence: true
+  validates :occurred_at, presence: true
+  
+  def self.activity_stats(broadcast_id = nil)
+    activities = broadcast_id ? where(broadcast_id: broadcast_id) : all
+    
+    {
+      total: activities.count,
+      sent: activities.sent.count,
+      delivered: activities.delivered.count,
+      opened: activities.opened.count,
+      clicked: activities.clicked.count,
+      bounced: activities.bounced.count,
+      complained: activities.complained.count,
+      unsubscribed: activities.unsubscribed.count,
+      failed: activities.failed_activities.count
+    }
+  end
+  
+  def self.engagement_rate(broadcast_id = nil)
+    activities = broadcast_id ? where(broadcast_id: broadcast_id) : all
+    delivered_count = activities.delivered.count
+    
+    return 0 if delivered_count.zero?
+    
+    opened_count = activities.opened.count
+    clicked_count = activities.clicked.count
+    
+    {
+      open_rate: (opened_count.to_f / delivered_count * 100).round(2),
+      click_rate: (clicked_count.to_f / delivered_count * 100).round(2),
+      click_to_open_rate: opened_count.zero? ? 0 : (clicked_count.to_f / opened_count * 100).round(2)
+    }
+  end
+end
diff --git a/app/models/emails/broadcast.rb b/app/models/emails/broadcast.rb
new file mode 100644
index 00000000..97a426ec
--- /dev/null
+++ b/app/models/emails/broadcast.rb
@@ -0,0 +1,36 @@
+class Emails::Broadcast < ApplicationRecord
+  belongs_to_tenant :tenant
+  belongs_to :signature, class_name: 'Emails::Signature', optional: true
+  belongs_to :filter, optional: true
+  has_many :activities, class_name: 'Emails::Activity', dependent: :destroy
+  
+  has_many :activities, class_name: 'Emails::Activity', dependent: :destroy
+  has_many :broadcast_logs, class_name: 'Emails::BroadcastLog', dependent: :destroy
+  
+  enum status: {
+    draft: 'draft',
+    ready: 'ready',
+    sent: 'sent',
+    failed: 'failed'
+  }
+  
+  enum to: {
+    all_members: 'all_members',
+    create_list_with_filters: 'create_list_with_filters',
+    choose_saved_list: 'choose_saved_list'
+  }
+  enum scheduled_type: {
+    sent_now: 'sent_now',
+    scheduled: 'scheduled'
+  }  
+  validates :title, presence: true, uniqueness: { scope: :tenant_id }
+  validates :subject, presence: true
+  validates :content, presence: true
+  validates :status, presence: true
+  validates :to, presence: true
+  
+  validates :filter, presence: true, if: :choose_saved_list?
+  validates :filters, presence: true, if: :create_list_with_filters?
+  
+  scope :by_status, ->(status) { where(status: status) }
+end
diff --git a/app/models/filter.rb b/app/models/filter.rb
new file mode 100644
index 00000000..fc23dc58
--- /dev/null
+++ b/app/models/filter.rb
@@ -0,0 +1,17 @@
+class Filter < ApplicationRecord
+  belongs_to_tenant :tenant
+
+  has_many :broadcasts, class_name: 'Emails::Broadcast', foreign_key: :filter_id, dependent: :nullify
+
+  enum list_type: {
+    email_filter: 'email_filter',
+    user_list_filter: 'user_list_filter'
+  }
+  
+  validates :name, presence: true, uniqueness: { scope: [:tenant_id, :list_type] }
+  validates :list_type, presence: true
+  validates :filters, presence: true
+  
+  scope :email_filters, -> { where(list_type: 'email_filter') }
+  scope :user_list_filters, -> { where(list_type: 'user_list_filter') }
+end
diff --git a/app/models/tenant.rb b/app/models/tenant.rb
index 3a6549e0..c12f04d1 100644
--- a/app/models/tenant.rb
+++ b/app/models/tenant.rb
@@ -67,6 +67,9 @@ class Tenant < ApplicationRecord
   has_many :affiliates_tenants, dependent: :destroy
   has_one :gamification_setting, dependent: :destroy, class_name: 'Gamification::Setting'
   has_one :tenant_metric, dependent: :destroy
+  has_many :emails_broadcasts, dependent: :destroy, class_name: 'Emails::Broadcast'
+  has_many :filters, dependent: :destroy
+  has_many :email_activities, dependent: :destroy, class_name: 'Emails::Activity'
   scope :trials, -> { where(is_trial: true) }
   scope :expired_trials, -> { trials.where('created_at < ?', 180.days.ago) }
 
diff --git a/app/models/user.rb b/app/models/user.rb
index 6b00e898..8fd8fc35 100644
--- a/app/models/user.rb
+++ b/app/models/user.rb
@@ -45,6 +45,7 @@ class User < ApplicationRecord
   has_many :gamification_points_activities, dependent: :destroy, class_name: 'Gamification::UserPointsActivity'
   has_many :social_networks, dependent: :destroy, class_name: 'UserSocialNetwork'
   accepts_nested_attributes_for :social_networks, allow_destroy: true
+  has_many :email_activities, dependent: :destroy, class_name: 'Emails::Activity'
 
   belongs_to :affiliate, optional: true
 
diff --git a/db/migrate/20250731074700_create_filters.rb b/db/migrate/20250731074700_create_filters.rb
new file mode 100644
index 00000000..6176a89c
--- /dev/null
+++ b/db/migrate/20250731074700_create_filters.rb
@@ -0,0 +1,17 @@
+class CreateFilters < ActiveRecord::Migration[7.0]
+  def change
+    create_table :filters do |t|
+      t.references :tenant, null: false, foreign_key: { on_delete: :cascade }
+
+      t.string :name, null: false
+      t.text :description, null: true
+      t.string :list_type, null: false, default: 'email_filter'
+      t.jsonb :filters, null: false, default: {}
+      t.jsonb :metadata, null: false, default: {}
+
+      t.timestamps
+    end
+
+    add_index :filters, [:tenant_id, :list_type, :name], unique: true
+  end
+end
diff --git a/db/migrate/20250731074710_create_emails_broadcasts.rb b/db/migrate/20250731074710_create_emails_broadcasts.rb
new file mode 100644
index 00000000..88e9a65d
--- /dev/null
+++ b/db/migrate/20250731074710_create_emails_broadcasts.rb
@@ -0,0 +1,22 @@
+class CreateEmailsBroadcasts < ActiveRecord::Migration[7.0]
+  def change
+    create_table :emails_broadcasts do |t|
+      t.references :tenant, null: false, foreign_key: { on_delete: :cascade }
+      t.references :signature, null: true, foreign_key: { to_table: :emails_signatures }
+      t.bigint :filter_id, null: true
+
+      t.string :title, null: false
+      t.string :subject, null: false
+      t.text :content, null: false
+
+      t.string :status, null: false
+      t.string :scheduled_type, null: false
+      t.datetime :scheduled_at, null: true
+      t.string :sent_to, null: false, default: 'all_members'
+      t.timestamps
+    end
+
+    add_foreign_key :emails_broadcasts, :filters, column: :filter_id
+
+  end
+end
diff --git a/db/migrate/20250731074720_create_email_activities.rb b/db/migrate/20250731074720_create_email_activities.rb
new file mode 100644
index 00000000..5c309afd
--- /dev/null
+++ b/db/migrate/20250731074720_create_email_activities.rb
@@ -0,0 +1,36 @@
+class CreateEmailActivities < ActiveRecord::Migration[7.0]
+  def change
+    create_table :email_activities do |t|
+      t.references :tenant, null: false, foreign_key: { on_delete: :cascade }
+      t.references :broadcast, null: false, foreign_key: { to_table: :emails_broadcasts, on_delete: :cascade }
+      t.references :user, null: true, foreign_key: { on_delete: :nullify }
+
+      # Activity details
+      t.string :activity_type, null: false # 'sent', 'delivered', 'opened', 'clicked', 'bounced', 'complained', 'unsubscribed'
+      t.string :email_address, null: false
+      t.string :status, null: false, default: 'pending' # 'pending', 'success', 'failed'
+
+      # Email service provider details
+      t.string :message_id, null: true # External email service message ID
+      t.string :provider, null: true # 'postmark', 'sendgrid', 'mailgun', etc.
+      t.jsonb :provider_data, null: false, default: {} # Additional data from email provider
+
+
+      # Metadata
+      t.jsonb :metadata, null: false, default: {}
+      t.datetime :occurred_at, null: false, default: -> { 'CURRENT_TIMESTAMP' }
+
+      t.timestamps
+    end
+
+    # Indexes for performance
+    # add_index :email_activities, [:tenant_id, :broadcast_id]
+    # add_index :email_activities, [:tenant_id, :activity_type]
+    # add_index :email_activities, [:tenant_id, :email_address]
+    # add_index :email_activities, [:tenant_id, :status]
+    # add_index :email_activities, [:tenant_id, :occurred_at]
+    # add_index :email_activities, :message_id
+    # add_index :email_activities, [:broadcast_id, :activity_type]
+    # add_index :email_activities, [:user_id, :activity_type]
+  end
+end
